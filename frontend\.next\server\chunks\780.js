exports.id=780,exports.ids=[780],exports.modules={4780:(e,t,o)=>{"use strict";o.d(t,{cn:()=>s});var r=o(49384),n=o(82348);function s(...e){return(0,n.QP)((0,r.$)(e))}},5551:(e,t,o)=>{"use strict";o.d(t,{C:()=>a});var r=o(74112),n=o(62185);let s="pluto_current_session",i=()=>"server";class a{constructor(){this.sessions=new Map,this.currentSessionId=null,this.useBackend=!0,this.sessionStartTimes=new Map,this.windowId=i(),console.log(`🪟 SessionManager initialized for window: ${this.windowId}`),this.loadSessionsFromStorage(),this.sessionStartTimes.clear(),this.useBackend=!1,this.loadSessionsFromStorage(),this.setupStorageListener(),setTimeout(()=>{this.checkBackendConnection().catch(()=>{})},1e3),console.log(`🪟 SessionManager initialized for window ${this.windowId}`)}static getInstance(){return a.instance||(a.instance=new a),a.instance}generateSessionName(e){let t=e.crypto1||"Crypto1",o=e.crypto2||"Crypto2",r=e.tradingMode||"SimpleSpot",n=`${t}/${o} ${r}`,s=Array.from(this.sessions.values()).filter(e=>e.name.startsWith(n));if(0===s.length)return n;let i=0;return s.forEach(e=>{let t=e.name.match(RegExp(`^${n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")} Session (\\d+)$`));if(t){let e=parseInt(t[1],10);e>i&&(i=e)}else e.name===n&&(i=Math.max(i,1))}),`${n} Session ${i+1}`}async checkBackendConnection(){try{let e=new AbortController,t=setTimeout(()=>e.abort(),1500),o=await fetch("http://localhost:5000/",{method:"GET",signal:e.signal});if(clearTimeout(t),o.status<500)this.useBackend=!0,console.log("✅ Session Manager: Backend connection established");else throw Error("Backend returned server error")}catch(e){this.useBackend=!1,console.warn("⚠️ Session Manager: Backend unavailable, using localStorage fallback"),console.warn("\uD83D\uDCA1 To enable backend features, start the backend server: python run.py")}}getWindowSpecificKey(e){return`${e}_${this.windowId}`}setupStorageListener(){}loadSessionsFromStorage(){try{return}catch(e){console.error("Failed to load sessions from storage:",e)}}saveSessionsToStorage(){try{return}catch(e){console.error("Failed to save sessions to storage:",e)}}async createNewSessionWithAutoName(e,t){let o=t||this.generateSessionName(e);return this.createNewSession(o,e)}async createNewSession(e,t){if(this.useBackend)try{let o=(await n.Rk.createSession({name:e,config:t,targetPriceRows:[],currentMarketPrice:0,crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0})).session.id;return console.log("✅ Session created on backend:",o),o}catch(e){console.error("❌ Failed to create session on backend, falling back to localStorage:",e),this.useBackend=!1}let o=(0,r.A)(),s=Date.now();return this.sessions.set(o,{id:o,name:e,config:t,targetPriceRows:[],orderHistory:[],currentMarketPrice:0,crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0,createdAt:s,lastModified:s,isActive:!1,runtime:0}),this.saveSessionsToStorage(),o}saveSession(e,t,o,r,n,s,i,a,c=!1,l){try{let d;let p=this.sessions.get(e);if(!p)return console.error("Session not found:",e),!1;if(void 0!==l)d=l,console.log(`📊 Using override runtime: ${d}ms for session ${e}`);else{d=p.runtime;let t=this.sessionStartTimes.get(e);t&&c?(d=p.runtime+(Date.now()-t),this.sessionStartTimes.set(e,Date.now())):!c&&t?(d=p.runtime+(Date.now()-t),this.sessionStartTimes.delete(e)):c&&!t&&this.sessionStartTimes.set(e,Date.now())}let u={...p,config:t,targetPriceRows:[...o],orderHistory:[...r],currentMarketPrice:n,crypto1Balance:s,crypto2Balance:i,stablecoinBalance:a,isActive:c,lastModified:Date.now(),runtime:d};return this.sessions.set(e,u),this.saveSessionsToStorage(),!0}catch(e){return console.error("Failed to save session:",e),!1}}loadSession(e){return this.sessions.get(e)||null}deleteSession(e){let t=this.sessions.delete(e);if(t){if(this.currentSessionId===e){this.currentSessionId=null;let e=this.getWindowSpecificKey(s);localStorage.removeItem(e)}this.saveSessionsToStorage()}return t}getAllSessions(){return Array.from(this.sessions.values()).map(e=>({id:e.id,name:e.name,pair:`${e.config.crypto1}/${e.config.crypto2}`,createdAt:e.createdAt,lastModified:e.lastModified,isActive:e.isActive,runtime:this.getCurrentRuntime(e.id),totalTrades:e.orderHistory.length,totalProfitLoss:e.orderHistory.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0)}))}getFilteredSessions(e=5e3){return this.getAllSessions().filter(t=>!!t.isActive||t.runtime>=e)}setCurrentSession(e){if(this.sessions.has(e)){this.currentSessionId=e;let t=this.getWindowSpecificKey(s);localStorage.setItem(t,e);let o=this.sessions.get(e);o&&!o.isActive&&(o.isActive=!0,o.lastModified=Date.now(),this.sessions.set(e,o),this.saveSessionsToStorage(),console.log(`✅ Session ${e} marked as active for window ${this.windowId}`))}}getCurrentSessionId(){return this.currentSessionId}clearCurrentSession(){if(this.currentSessionId){let e=this.sessions.get(this.currentSessionId);e&&e.isActive&&(e.isActive=!1,e.lastModified=Date.now(),this.sessions.set(this.currentSessionId,e),this.saveSessionsToStorage(),console.log(`⏹️ Session ${this.currentSessionId} marked as inactive for window ${this.windowId}`))}this.currentSessionId=null,console.log(`🗑️ Cleared current session for window ${this.windowId}`)}startSessionRuntime(e){this.sessionStartTimes.set(e,Date.now())}stopSessionRuntime(e){let t=this.sessionStartTimes.get(e);if(t){let o=this.sessions.get(e);if(o){let r=Date.now()-t;o.runtime+=r,o.lastModified=Date.now(),this.sessions.set(e,o),this.saveSessionsToStorage()}this.sessionStartTimes.delete(e)}}deactivateSession(e){let t=this.sessions.get(e);t&&t.isActive&&(t.isActive=!1,t.lastModified=Date.now(),this.sessions.set(e,t),this.saveSessionsToStorage(),console.log(`⏹️ Session ${e} deactivated`))}getCurrentRuntime(e){let t=this.sessions.get(e);if(!t)return 0;let o=this.sessionStartTimes.get(e);return o?t.runtime+(Date.now()-o):t.runtime}exportSessionToJSON(e){let t=this.sessions.get(e);return t?JSON.stringify(t,null,2):null}importSessionFromJSON(e){try{let t=JSON.parse(e),o=(0,r.A)(),n={...t,id:o,isActive:!1,lastModified:Date.now()};return this.sessions.set(o,n),this.saveSessionsToStorage(),o}catch(e){return console.error("Failed to import session:",e),null}}renameSession(e,t){let o=this.sessions.get(e);return!!o&&(o.name=t,o.lastModified=Date.now(),this.sessions.set(e,o),this.saveSessionsToStorage(),!0)}getSessionHistory(e){let t=this.sessions.get(e);return t?[...t.orderHistory]:[]}exportSessionToCSV(e){let t=this.sessions.get(e);return t?["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...t.orderHistory.map(e=>[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,e.amountCrypto1?.toFixed(t.config.numDigits)||"",e.avgPrice?.toFixed(t.config.numDigits)||"",e.valueCrypto2?.toFixed(t.config.numDigits)||"",e.price1?.toFixed(t.config.numDigits)||"",e.crypto1Symbol,e.price2?.toFixed(t.config.numDigits)||"",e.crypto2Symbol,e.realizedProfitLossCrypto1?.toFixed(t.config.numDigits)||"",e.realizedProfitLossCrypto2?.toFixed(t.config.numDigits)||""].join(","))].join("\n"):null}clearAllSessions(){this.sessions.clear(),this.currentSessionId=null,localStorage.removeItem("pluto_trading_sessions");let e=this.getWindowSpecificKey(s);localStorage.removeItem(e)}enableAutoSave(e,t,o=3e4){let r=setInterval(()=>{let o=t();this.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,o.isActive)},o);return()=>clearInterval(r)}}},10074:(e,t,o)=>{Promise.resolve().then(o.bind(o,14947)),Promise.resolve().then(o.bind(o,17617)),Promise.resolve().then(o.bind(o,63213)),Promise.resolve().then(o.bind(o,78895))},14947:(e,t,o)=>{"use strict";o.d(t,{Toaster:()=>f});var r=o(60687),n=o(29867),s=o(43210),i=o(47313),a=o(24224),c=o(78726),l=o(4780);let d=i.Kq,p=s.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.LM,{ref:o,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));p.displayName=i.LM.displayName;let u=(0,a.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),g=s.forwardRef(({className:e,variant:t,...o},n)=>(0,r.jsx)(i.bL,{ref:n,className:(0,l.cn)(u({variant:t}),e),...o}));g.displayName=i.bL.displayName,s.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.rc,{ref:o,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=i.rc.displayName;let y=s.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.bm,{ref:o,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,r.jsx)(c.A,{className:"h-4 w-4"})}));y.displayName=i.bm.displayName;let S=s.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.hE,{ref:o,className:(0,l.cn)("text-sm font-semibold",e),...t}));S.displayName=i.hE.displayName;let m=s.forwardRef(({className:e,...t},o)=>(0,r.jsx)(i.VY,{ref:o,className:(0,l.cn)("text-sm opacity-90",e),...t}));function f(){let{toasts:e}=(0,n.dj)();return(0,r.jsxs)(d,{children:[e.map(function({id:e,title:t,description:o,action:n,...s}){return(0,r.jsxs)(g,{...s,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[t&&(0,r.jsx)(S,{children:t}),o&&(0,r.jsx)(m,{children:o})]}),n,(0,r.jsx)(y,{})]},e)}),(0,r.jsx)(p,{})]})}m.displayName=i.VY.displayName},16949:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,16444,23)),Promise.resolve().then(o.t.bind(o,16042,23)),Promise.resolve().then(o.t.bind(o,88170,23)),Promise.resolve().then(o.t.bind(o,49477,23)),Promise.resolve().then(o.t.bind(o,29345,23)),Promise.resolve().then(o.t.bind(o,12089,23)),Promise.resolve().then(o.t.bind(o,46577,23)),Promise.resolve().then(o.t.bind(o,31307,23))},17617:(e,t,o)=>{"use strict";o.d(t,{AIProvider:()=>c,f:()=>l});var r=o(60687),n=o(43210),s=o(6475);let i=(0,s.createServerReference)("40cceb4bed07fea6b3b3e9b4e4cdba28882f7c0d1b",s.callServer,void 0,s.findSourceMapURL,"suggestTradingMode"),a=(0,n.createContext)(void 0),c=({children:e})=>{let[t,o]=(0,n.useState)(null),[s,c]=(0,n.useState)(!1),[l,d]=(0,n.useState)(null),p=async e=>{c(!0),d(null),o(null);try{let t=await i(e);o(t)}catch(e){d(e instanceof Error?e.message:"An unknown error occurred during AI suggestion."),console.error("Error fetching trading mode suggestion:",e)}finally{c(!1)}};return(0,r.jsx)(a.Provider,{value:{suggestion:t,isLoading:s,error:l,getTradingModeSuggestion:p},children:e})},l=()=>{let e=(0,n.useContext)(a);if(void 0===e)throw Error("useAIContext must be used within an AIProvider");return e}},26443:(e,t,o)=>{"use strict";o.d(t,{AIProvider:()=>n});var r=o(12907);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call AIProvider() from the server but AIProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AIContext.tsx","AIProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAIContext() from the server but useAIContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AIContext.tsx","useAIContext")},29131:(e,t,o)=>{"use strict";o.d(t,{AuthProvider:()=>n});var r=o(12907);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\AuthContext.tsx","useAuth")},29867:(e,t,o)=>{"use strict";o.d(t,{dj:()=>u});var r=o(43210);let n=0,s=new Map,i=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:o}=t;return o?i(o):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===o||void 0===o?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],l={toasts:[]};function d(e){l=a(l,e),c.forEach(e=>{e(l)})}function p({...e}){let t=(n=(n+1)%Number.MAX_SAFE_INTEGER).toString(),o=()=>d({type:"DISMISS_TOAST",toastId:t});return d({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||o()}}}),{id:t,dismiss:o,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,t]=r.useState(l);return r.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:p,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},47002:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=47002,e.exports=t},47506:(e,t,o)=>{"use strict";o.d(t,{TradingProvider:()=>n});var r=o(12907);let n=(0,r.registerClientReference)(function(){throw Error("Attempted to call TradingProvider() from the server but TradingProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx","TradingProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useTradingContext() from the server but useTradingContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx","useTradingContext")},55280:(e,t,o)=>{"use strict";o.d(t,{Oh:()=>r,Ql:()=>i,hg:()=>n,vA:()=>s});let r={soundAlertsEnabled:!0,alertOnOrderExecution:!0,alertOnError:!0,soundOrderExecution:"/sounds/order-executed.mp3",soundError:"/sounds/error.mp3",clearOrderHistoryOnStart:!1},n=["BTC","ETH","ADA","SOL","DOGE","LINK","MATIC","DOT","AVAX","XRP","LTC","BCH","BNB","SHIB"],s={BTC:["USDT","USDC","FDUSD","EUR"],ETH:["USDT","USDC","FDUSD","BTC","EUR"],ADA:["USDT","USDC","BTC","ETH"],SOL:["USDT","USDC","BTC","ETH"]},i=["USDT","USDC","FDUSD","DAI"]},58805:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,86346,23)),Promise.resolve().then(o.t.bind(o,27924,23)),Promise.resolve().then(o.t.bind(o,35656,23)),Promise.resolve().then(o.t.bind(o,40099,23)),Promise.resolve().then(o.t.bind(o,38243,23)),Promise.resolve().then(o.t.bind(o,28827,23)),Promise.resolve().then(o.t.bind(o,62763,23)),Promise.resolve().then(o.t.bind(o,97173,23))},61135:()=>{},62185:(e,t,o)=>{"use strict";o.d(t,{Rk:()=>a,ZQ:()=>s,oc:()=>i});let r="http://localhost:5000";async function n(e,t={}){let o=`${r}${e}`,s=localStorage.getItem("plutoAuthToken"),i={"Content-Type":"application/json",...s?{Authorization:`Bearer ${s}`}:{},...t.headers};try{let e;let r=new AbortController,n=setTimeout(()=>r.abort(),1e4),s=await fetch(o,{...t,headers:i,signal:r.signal}).finally(()=>clearTimeout(n));if(401===s.status)throw localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),Error("Authentication expired. Please login again.");let a=s.headers.get("content-type");if(a&&a.includes("application/json"))e=await s.json();else{let t=await s.text();try{e=JSON.parse(t)}catch(o){e={message:t}}}if(!s.ok)throw console.error("API error response:",e),Error(e.error||e.message||`API error: ${s.status}`);return e}catch(e){if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw console.error("Network error - Is the backend server running?:",e),Error("Cannot connect to server. Please check if the backend is running.");if("AbortError"===e.name)throw console.error("Request timeout:",e),Error("Request timed out. Server may be unavailable.");throw console.error("API request failed:",e),e}}console.log("API Base URL:",r);let s={login:async(e,t)=>{try{let o=await c(async()=>await n("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:t})}));if(o&&o.access_token)return localStorage.setItem("plutoAuthToken",o.access_token),localStorage.setItem("plutoAuth","true"),o.user&&localStorage.setItem("plutoUser",JSON.stringify(o.user)),!0;return!1}catch(e){return console.error("Login API error:",e),!1}},register:async(e,t,o)=>c(async()=>await n("/auth/register",{method:"POST",body:JSON.stringify({username:e,password:t,email:o})})),logout:async()=>(localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),!0)},i={getConfig:async e=>n(e?`/trading/config/${e}`:"/trading/config"),saveConfig:async e=>n("/trading/config",{method:"POST",body:JSON.stringify(e)}),updateConfig:async(e,t)=>n(`/trading/config/${e}`,{method:"PUT",body:JSON.stringify(t)}),startBot:async e=>n(`/trading/bot/start/${e}`,{method:"POST"}),stopBot:async e=>n(`/trading/bot/stop/${e}`,{method:"POST"}),getBotStatus:async e=>n(`/trading/bot/status/${e}`),getTradeHistory:async e=>{let t=e?`?configId=${e}`:"";return n(`/trading/history${t}`)},getBalances:async()=>n("/trading/balances"),getMarketPrice:async e=>n(`/trading/market-data/${e}`),getTradingPairs:async(e="binance")=>n(`/trading/exchange/trading-pairs?exchange=${e}`),getCryptocurrencies:async(e="binance")=>n(`/trading/exchange/cryptocurrencies?exchange=${e}`)},a={getAllSessions:async(e=!0)=>n(`/sessions/?include_inactive=${e}`),createSession:async e=>n("/sessions/",{method:"POST",body:JSON.stringify(e)}),getSession:async e=>n(`/sessions/${e}`),updateSession:async(e,t)=>n(`/sessions/${e}`,{method:"PUT",body:JSON.stringify(t)}),deleteSession:async e=>n(`/sessions/${e}`,{method:"DELETE"}),activateSession:async e=>n(`/sessions/${e}/activate`,{method:"POST"}),getSessionHistory:async e=>n(`/sessions/${e}/history`),getActiveSession:async()=>n("/sessions/active")},c=async(e,t=3)=>{let o=0,r=async()=>{try{return await e()}catch(e){if((e instanceof TypeError&&e.message.includes("Failed to fetch")||"AbortError"===e.name)&&o<t){let e=500*Math.pow(2,o);return console.log(`Retrying after ${e}ms (attempt ${o+1}/${t})...`),o++,await new Promise(t=>setTimeout(t,e)),r()}throw e}};return r()}},63213:(e,t,o)=>{"use strict";o.d(t,{A:()=>d,AuthProvider:()=>l});var r=o(60687),n=o(43210),s=o(16189),i=o(11516),a=o(62185);let c=(0,n.createContext)(void 0),l=({children:e})=>{let[t,o]=(0,n.useState)(!1),[l,d]=(0,n.useState)(!0),p=(0,s.useRouter)(),u=(0,s.usePathname)();(0,n.useEffect)(()=>{let e=localStorage.getItem("plutoAuth"),t=localStorage.getItem("plutoAuthToken");"true"===e&&t&&o(!0),d(!1)},[]),(0,n.useEffect)(()=>{l||t||"/login"===u?!l&&t&&"/login"===u&&p.push("/dashboard"):p.push("/login")},[t,l,u,p]);let g=async(e,t)=>{d(!0);try{if(await a.ZQ.login(e,t))return o(!0),p.push("/dashboard"),!0;return o(!1),!1}catch(e){return console.error("Login failed:",e),o(!1),!1}finally{d(!1)}},y=async()=>{try{await a.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{o(!1),p.push("/login")}};return l&&!u?.startsWith("/_next/static/")?(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,r.jsx)(i.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,r.jsx)("p",{className:"ml-4 text-xl",children:"Loading Pluto..."})]}):t||"/login"===u||u?.startsWith("/_next/static/")?(0,r.jsx)(c.Provider,{value:{isAuthenticated:t,login:g,logout:y,isLoading:l},children:e}):(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,r.jsx)(i.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,r.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting to login..."})]})},d=()=>{let e=(0,n.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},73231:(e,t,o)=>{"use strict";o.r(t),o.d(t,{"40cceb4bed07fea6b3b3e9b4e4cdba28882f7c0d1b":()=>d});var r=o(91199);o(42087);var n=o(37612),s=o(56758);let i=(0,n.genkit)({plugins:[(0,s.YF)()],model:"googleai/gemini-2.0-flash"});var a=o(33331);let c=n.z.object({riskTolerance:n.z.string().describe("The user risk tolerance, can be low, medium, or high."),preferredCryptocurrencies:n.z.string().describe("The user preferred cryptocurrencies, comma separated."),investmentGoals:n.z.string().describe("The user investment goals, such as long term investment or short term profit.")}),l=n.z.object({suggestedMode:n.z.enum(["Simple Spot","Stablecoin Swap"]).describe("The suggested trading mode."),reason:n.z.string().describe("The reason for the suggestion.")});async function d(e){return u(e)}let p=i.definePrompt({name:"tradingModeSuggestionPrompt",input:{schema:c},output:{schema:l},prompt:`You are an expert in trading mode selection. You will suggest the most suitable trading mode (Simple Spot or Stablecoin Swap) based on the user's risk tolerance, preferred cryptocurrencies, and investment goals.

Risk Tolerance: {{{riskTolerance}}}
Preferred Cryptocurrencies: {{{preferredCryptocurrencies}}}
Investment Goals: {{{investmentGoals}}}

Consider the following:

*   Simple Spot Mode is suitable for users who are comfortable with higher risk and are looking for short term profits.
*   Stablecoin Swap Mode is suitable for users who are risk averse and are looking for long term investment.

Based on the information above, suggest a trading mode and explain your reasoning.`}),u=i.defineFlow({name:"suggestTradingModeFlow",inputSchema:c,outputSchema:l},async e=>{let{output:t}=await p(e);return t});(0,a.D)([d]),(0,r.A)(d,"40cceb4bed07fea6b3b3e9b4e4cdba28882f7c0d1b",null)},78895:(e,t,o)=>{"use strict";o.d(t,{TradingProvider:()=>D,U:()=>M});var r=o(60687),n=o(43210),s=o(55280),i=o(74112),a=o(29867),c=o(62185),l=o(5551);class d{constructor(){this.isOnline=navigator.onLine,this.listeners=new Set,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=5e3,this.hasInitialized=!1,this.setupEventListeners(),this.startPeriodicCheck(),setTimeout(()=>{this.hasInitialized=!0},1e3)}static getInstance(){return d.instance||(d.instance=new d),d.instance}setupEventListeners(){window.addEventListener("online",this.handleOnline.bind(this)),window.addEventListener("offline",this.handleOffline.bind(this)),document.addEventListener("visibilitychange",()=>{document.hidden||this.checkConnection()})}handleOnline(){console.log("\uD83C\uDF10 Network: Back online"),this.isOnline=!0,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.notifyListeners(!0,!this.hasInitialized)}handleOffline(){console.log("\uD83C\uDF10 Network: Gone offline"),this.isOnline=!1,this.notifyListeners(!1,!this.hasInitialized)}async checkConnection(){let e=navigator.onLine;return e!==this.isOnline&&(this.isOnline=e,this.notifyListeners(e,!this.hasInitialized),e&&(this.lastOnlineTime=Date.now(),this.reconnectAttempts=0)),e}startPeriodicCheck(){let e=setInterval(()=>{this.checkConnection()},6e4);this.periodicInterval=e}cleanup(){this.periodicInterval&&clearInterval(this.periodicInterval),this.listeners.clear()}notifyListeners(e,t=!1){this.listeners.forEach(o=>{try{o(e,t)}catch(e){console.error("Error in network status listener:",e)}})}addListener(e){return this.listeners.add(e),()=>{this.listeners.delete(e)}}getStatus(){return{isOnline:this.isOnline,lastOnlineTime:this.lastOnlineTime,reconnectAttempts:this.reconnectAttempts}}async forceCheck(){return await this.checkConnection()}async attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return console.log("\uD83C\uDF10 Network: Max reconnect attempts reached"),!1;this.reconnectAttempts++;let e=Math.min(this.reconnectInterval*Math.pow(2,this.reconnectAttempts-1),3e4);console.log(`🌐 Network: Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${e}ms`),await new Promise(t=>setTimeout(t,e));let t=await this.checkConnection();return!t&&this.reconnectAttempts<this.maxReconnectAttempts&&setTimeout(()=>this.attemptReconnect(),1e3),t}}class p{constructor(){this.saveInterval=null,this.saveFunction=null,this.intervalMs=3e4,this.isEnabled=!0,this.lastSaveTime=0,this.networkMonitor=d.getInstance(),this.setupNetworkListener(),this.setupBeforeUnloadListener()}static getInstance(){return p.instance||(p.instance=new p),p.instance}setupNetworkListener(){this.networkMonitor.addListener(e=>{e&&this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving on network reconnection"),this.saveFunction(),this.lastSaveTime=Date.now())})}setupBeforeUnloadListener(){window.addEventListener("beforeunload",()=>{this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving before page unload"),this.saveFunction())}),document.addEventListener("visibilitychange",()=>{document.hidden&&this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving on tab switch"),this.saveFunction(),this.lastSaveTime=Date.now())})}enable(e,t=3e4){this.saveFunction=e,this.intervalMs=t,this.isEnabled=!0,this.stop(),this.saveInterval=setInterval(()=>{this.isEnabled&&this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(console.log("\uD83D\uDCBE Auto-save: Periodic save"),this.saveFunction(),this.lastSaveTime=Date.now())},this.intervalMs),console.log(`💾 Auto-save: Enabled with ${t}ms interval`)}disable(){this.isEnabled=!1,this.stop(),console.log("\uD83D\uDCBE Auto-save: Disabled")}stop(){this.saveInterval&&(clearInterval(this.saveInterval),this.saveInterval=null)}saveNow(){this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(console.log("\uD83D\uDCBE Auto-save: Manual save triggered"),this.saveFunction(),this.lastSaveTime=Date.now())}getStatus(){return{isEnabled:this.isEnabled,lastSaveTime:this.lastSaveTime,intervalMs:this.intervalMs,isOnline:this.networkMonitor.getStatus().isOnline}}}class u{constructor(){this.checkInterval=null,this.warningThreshold=262144e3,this.criticalThreshold=0x19000000,this.listeners=new Set,this.startMonitoring()}static getInstance(){return u.instance||(u.instance=new u),u.instance}startMonitoring(){console.log("\uD83D\uDCCA Memory monitoring disabled to prevent frequent notifications")}checkMemoryUsage(){if("memory"in performance){let e=performance.memory,t=e.usedJSHeapSize;this.notifyListeners(e),t>this.criticalThreshold?(console.warn("\uD83E\uDDE0 Memory: Critical memory usage detected:",{used:`${(t/1024/1024).toFixed(2)}MB`,total:`${(e.totalJSHeapSize/1024/1024).toFixed(2)}MB`,limit:`${(e.jsHeapSizeLimit/1024/1024).toFixed(2)}MB`}),"gc"in window&&window.gc()):t>this.warningThreshold&&console.log("\uD83E\uDDE0 Memory: High memory usage:",{used:`${(t/1024/1024).toFixed(2)}MB`,total:`${(e.totalJSHeapSize/1024/1024).toFixed(2)}MB`})}}notifyListeners(e){this.listeners.forEach(t=>{try{t(e)}catch(e){console.error("Error in memory monitor listener:",e)}})}addListener(e){return this.listeners.add(e),()=>this.listeners.delete(e)}getMemoryUsage(){return"memory"in performance?performance.memory:null}stop(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null)}}let g=e=>e.crypto1&&e.crypto2?h(e):0,y=async e=>{try{if(!e.crypto1||!e.crypto2)return 0;if("StablecoinSwap"===e.tradingMode){let t=e.preferredStablecoin||"USDT";try{let o=S(e.crypto1),r=S(e.crypto2),n=S(t);if(o&&r&&n){let s=await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${o},${r}&vs_currencies=${n}`);if(s.ok){let i=await s.json(),a=i[o]?.[n],c=i[r]?.[n];if(a>0&&c>0){let o=a/c;return console.log(`📊 StablecoinSwap price: ${e.crypto1}/${e.crypto2} = ${o} (${e.crypto1}: ${a} ${t}, ${e.crypto2}: ${c} ${t})`),o}}}let s=await m(e.crypto1,t),i=await m(e.crypto2,t),a=s/i;return console.log(`📊 StablecoinSwap price (fallback): ${e.crypto1}/${e.crypto2} = ${a} (via ${t})`),a}catch(r){console.error("Error fetching StablecoinSwap prices:",r);let t=f(e.crypto1),o=f(e.crypto2);return t/o}}let t=`${e.crypto1}${e.crypto2}`.toUpperCase();try{let o=await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${t}`);if(o.ok){let t=await o.json(),r=parseFloat(t.price);if(r>0)return console.log(`✅ Price fetched from Binance: ${e.crypto1}/${e.crypto2} = ${r}`),r}}catch(e){console.warn("Binance API failed, trying alternative...",e)}try{let t=S(e.crypto1),o=S(e.crypto2);if(t&&o){let r=await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${t}&vs_currencies=${o}`);if(r.ok){let n=await r.json(),s=n[t]?.[o];if(s>0)return console.log(`✅ Price fetched from CoinGecko: ${e.crypto1}/${e.crypto2} = ${s}`),s}}}catch(e){console.warn("CoinGecko API failed, using mock price...",e)}let o=h(e);return console.log(`⚠️ Using mock price: ${e.crypto1}/${e.crypto2} = ${o}`),o}catch(t){return console.error("Error fetching market price:",t),h(e)}},S=e=>({BTC:"bitcoin",ETH:"ethereum",SOL:"solana",ADA:"cardano",DOT:"polkadot",MATIC:"matic-network",AVAX:"avalanche-2",LINK:"chainlink",UNI:"uniswap",USDT:"tether",USDC:"usd-coin",BUSD:"binance-usd",DAI:"dai"})[e.toUpperCase()]||null,m=async(e,t)=>{try{if(S(e)&&S(t)){let o=S(e),r=S(t);if(o===r)return 1;let n=await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${o}&vs_currencies=${r}`);if(n.ok){let s=await n.json(),i=o&&r?s[o]?.[r]:null;if(i>0)return console.log(`📊 Stablecoin rate: ${e}/${t} = ${i}`),i}}let o=f(e),r=f(t),n=o/r;return console.log(`📊 Fallback stablecoin rate: ${e}/${t} = ${n} (via USD)`),n}catch(o){return console.error("Error fetching stablecoin exchange rate:",o),f(e)/f(t)}},f=e=>({BTC:109e3,ETH:4e3,SOL:240,ADA:1.2,DOGE:.4,LINK:25,MATIC:.5,DOT:8,AVAX:45,SHIB:3e-5,XRP:2.5,LTC:110,BCH:500,UNI:15,AAVE:180,MKR:1800,SNX:3.5,COMP:85,YFI:8500,SUSHI:2.1,"1INCH":.65,CRV:.85,UMA:3.2,ATOM:12,NEAR:6.5,ALGO:.35,ICP:14,HBAR:.28,APT:12.5,TON:5.8,FTM:.95,ONE:.025,FIL:8.5,TRX:.25,ETC:35,VET:.055,QNT:125,LDO:2.8,CRO:.18,LUNC:15e-5,MANA:.85,SAND:.75,AXS:8.5,ENJ:.45,CHZ:.12,THETA:2.1,FLOW:1.2,XTZ:1.8,EOS:1.1,GRT:.28,BAT:.35,ZEC:45,DASH:35,LRC:.45,ZRX:.65,KNC:.85,REN:.15,BAND:2.5,STORJ:.85,NMR:25,ANT:8.5,BNT:.95,MLN:35,REP:15,IOTX:.065,ZIL:.045,ICX:.35,QTUM:4.5,ONT:.45,WAVES:3.2,LSK:1.8,NANO:1.5,SC:.008,DGB:.025,RVN:.035,BTT:15e-7,WIN:15e-5,HOT:.0035,DENT:.0018,NPXS:85e-5,FUN:.0085,CELR:.025,USDT:1,USDC:1,FDUSD:1,BUSD:1,DAI:1})[e.toUpperCase()]||100,h=e=>{let t=f(e.crypto1),o=f(e.crypto2),r=t/o*(1+(Math.random()-.5)*.02);return console.log(`📊 Fallback price calculation: ${e.crypto1} ($${t}) / ${e.crypto2} ($${o}) = ${r.toFixed(6)}`),r},b="plutoTradingBot_globalBalances",v=()=>({crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:1e5,totalProfitLoss:0}),T=e=>{try{let t={crypto1Balance:e.crypto1Balance,crypto2Balance:e.crypto2Balance,stablecoinBalance:e.stablecoinBalance,totalProfitLoss:e.totalProfitLoss,lastUpdated:new Date().toISOString()};localStorage.setItem(b,JSON.stringify(t)),console.log("\uD83D\uDCBE Global balances saved:",t)}catch(e){console.error("Failed to save global balance to storage:",e)}},C={tradingMode:"SimpleSpot",crypto1:"",crypto2:"",baseBid:100,multiplier:1.005,numDigits:4,slippagePercent:.2,incomeSplitCrypto1Percent:50,incomeSplitCrypto2Percent:50,preferredStablecoin:s.Ql[0]},A=v(),E={config:C,targetPriceRows:[],orderHistory:[],appSettings:s.Oh,currentMarketPrice:g(C),botSystemStatus:"Stopped",crypto1Balance:A.crypto1Balance,crypto2Balance:A.crypto2Balance,stablecoinBalance:A.stablecoinBalance,backendStatus:"unknown",totalProfitLoss:A.totalProfitLoss,sessionAlarmConfig:{buyAlarmEnabled:!0,sellAlarmEnabled:!0,buyAlarmSound:"default.mp3",sellAlarmSound:"default.mp3",volume:50},telegramConfig:{enabled:!1,botToken:"",chatId:""},isTrading:!1},w=new Map,P="tradingSession",$=e=>{try{let t={config:e.config,targetPriceRows:e.targetPriceRows,orderHistory:e.orderHistory,currentMarketPrice:e.currentMarketPrice,crypto1Balance:e.crypto1Balance,crypto2Balance:e.crypto2Balance,stablecoinBalance:e.stablecoinBalance,totalProfitLoss:e.totalProfitLoss,sessionAlarmConfig:e.sessionAlarmConfig,telegramConfig:e.telegramConfig,isTrading:e.isTrading,botSystemStatus:e.botSystemStatus,lastUpdated:new Date().toISOString()};localStorage.setItem(P,JSON.stringify(t))}catch(e){console.error("Failed to save session to storage:",e)}},I=e=>{try{let t={crypto1Balance:e.crypto1Balance,crypto2Balance:e.crypto2Balance,stablecoinBalance:e.stablecoinBalance,totalProfitLoss:e.totalProfitLoss,lastUpdated:new Date().toISOString()};localStorage.setItem("tradingBalances",JSON.stringify(t))}catch(e){console.error("Failed to save balance to storage:",e)}},B=e=>{},R=()=>null,k=(e,t)=>{switch(t.type){case"SET_CONFIG":let o={...e.config,...t.payload};if(t.payload.crypto1||t.payload.crypto2)return{...e,config:o,currentMarketPrice:g(o)};return{...e,config:o};case"SET_TARGET_PRICE_ROWS":return{...e,targetPriceRows:t.payload.sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}))};case"ADD_TARGET_PRICE_ROW":{let o=[...e.targetPriceRows,t.payload].sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:o}}case"UPDATE_TARGET_PRICE_ROW":{let o=e.targetPriceRows.map(e=>e.id===t.payload.id?t.payload:e).sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:o}}case"REMOVE_TARGET_PRICE_ROW":{let o=e.targetPriceRows.filter(e=>e.id!==t.payload).sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:o}}case"ADD_ORDER_HISTORY_ENTRY":return{...e,orderHistory:[t.payload,...e.orderHistory]};case"CLEAR_ORDER_HISTORY":return{...e,orderHistory:[]};case"SET_APP_SETTINGS":return{...e,appSettings:{...e.appSettings,...t.payload}};case"SET_MARKET_PRICE":return{...e,currentMarketPrice:t.payload};case"FLUCTUATE_MARKET_PRICE":{if(e.currentMarketPrice<=0)return e;let t=(Math.random()-.5)*.006,o=e.currentMarketPrice*(1+t);return{...e,currentMarketPrice:o>0?o:e.currentMarketPrice}}case"UPDATE_BALANCES":let r={...e,crypto1Balance:void 0!==t.payload.crypto1?t.payload.crypto1:e.crypto1Balance,crypto2Balance:void 0!==t.payload.crypto2?t.payload.crypto2:e.crypto2Balance,stablecoinBalance:void 0!==t.payload.stablecoin?t.payload.stablecoin:e.stablecoinBalance};return T(r),r;case"UPDATE_STABLECOIN_BALANCE":let n={...e,stablecoinBalance:t.payload};return T(n),n;case"RESET_SESSION":let s={...e.config},i=v();return{...E,config:s,appSettings:{...e.appSettings},currentMarketPrice:g(s),crypto1Balance:i.crypto1Balance,crypto2Balance:i.crypto2Balance,stablecoinBalance:i.stablecoinBalance,totalProfitLoss:i.totalProfitLoss};case"SET_BACKEND_STATUS":return{...e,backendStatus:t.payload};case"SYSTEM_START_BOT_INITIATE":return{...e,botSystemStatus:"WarmingUp"};case"SYSTEM_COMPLETE_WARMUP":return{...e,botSystemStatus:"Running"};case"SYSTEM_STOP_BOT":return{...e,botSystemStatus:"Stopped"};case"SYSTEM_RESET_BOT":return w.clear(),l.C.getInstance().clearCurrentSession(),{...e,botSystemStatus:"Stopped",targetPriceRows:[],orderHistory:[]};case"SET_TARGET_PRICE_ROWS":return{...e,targetPriceRows:t.payload};case"RESET_FOR_NEW_CRYPTO":let a=v();return{...E,config:e.config,backendStatus:e.backendStatus,botSystemStatus:"Stopped",currentMarketPrice:0,crypto1Balance:a.crypto1Balance,crypto2Balance:a.crypto2Balance,stablecoinBalance:a.stablecoinBalance,totalProfitLoss:a.totalProfitLoss};case"RESTORE_SESSION":return{...e,...t.payload,isTrading:!1,botSystemStatus:"Stopped"};case"SET_TELEGRAM_CONFIG":return{...e,telegramConfig:{...e.telegramConfig,...t.payload}};case"SET_SESSION_ALARM_CONFIG":return{...e,sessionAlarmConfig:{...e.sessionAlarmConfig,...t.payload}};default:return e}},x=(0,n.createContext)(void 0),L=(e,t)=>{try{let o="buy"===e?t.buyAlarmSound:t.sellAlarmSound,r="buy"===e?t.buyAlarmEnabled:t.sellAlarmEnabled;if(o&&r){let e=new Audio(`/ringtones/${o}`);e.volume=t.volume/100,e.play().catch(console.error)}}catch(e){console.error("Failed to play session alarm:",e)}},O=async(e,t)=>{try{let o=await fetch(`https://api.coingecko.com/api/v3/simple/price?ids=${e.toLowerCase()}&vs_currencies=${t.toLowerCase()}`),r=await o.json(),n=r[e.toLowerCase()]?.[t.toLowerCase()];if(n)return n;throw Error("Price not found")}catch(e){return console.error("Error fetching stablecoin swap price:",e),null}},D=({children:e})=>{let[t,o]=(0,n.useReducer)(k,(()=>{if(!l.C.getInstance().getCurrentSessionId())return console.log("\uD83C\uDD95 New window detected - starting with fresh state"),E;let e=R();return e?{...E,...e}:E})()),{toast:s}=(0,a.dj)(),g=(0,n.useRef)(null),S=(0,n.useCallback)(async()=>{try{if(!t.config.crypto1||!t.config.crypto2){o({type:"SET_MARKET_PRICE",payload:0});return}let e=await y(t.config);o({type:"SET_MARKET_PRICE",payload:e})}catch(e){console.error("Failed to fetch market price:",e)}},[t.config,o]);(0,n.useEffect)(()=>{S();let e=setInterval(()=>{d.getInstance().getStatus().isOnline&&o({type:"FLUCTUATE_MARKET_PRICE"})},2e3);return()=>{clearInterval(e)}},[S,o]),(0,n.useEffect)(()=>{},[]);let m=(0,n.useCallback)(e=>{if(t.appSettings.soundAlertsEnabled&&g.current){let o;"soundOrderExecution"===e&&t.appSettings.alertOnOrderExecution?o=t.appSettings.soundOrderExecution:"soundError"===e&&t.appSettings.alertOnError&&(o=t.appSettings.soundError),o&&(g.current.src=o,g.current.currentTime=0,g.current.play().then(()=>{setTimeout(()=>{g.current&&(g.current.pause(),g.current.currentTime=0)},2e3)}).catch(e=>console.error("Error playing sound:",e)))}},[t.appSettings]),h=(0,n.useCallback)(async e=>{try{let t=localStorage.getItem("telegram_bot_token"),o=localStorage.getItem("telegram_chat_id");if(!t||!o){console.log("Telegram not configured - skipping notification");return}let r=await fetch(`https://api.telegram.org/bot${t}/sendMessage`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:o,text:e,parse_mode:"HTML"})});r.ok||console.error("Failed to send Telegram notification:",r.statusText)}catch(e){console.error("Error sending Telegram notification:",e)}},[]);(0,n.useEffect)(()=>{},[t.config.crypto1,t.config.crypto2]);let v=(0,n.useCallback)(e=>{e&&Array.isArray(e)&&o({type:"SET_TARGET_PRICE_ROWS",payload:[...e].filter(e=>!isNaN(e)&&e>0).sort((e,t)=>e-t).map((e,o)=>{let r=t.targetPriceRows.find(t=>t.targetPrice===e);return r?{...r,counter:o+1}:{id:(0,i.A)(),counter:o+1,status:"Free",orderLevel:0,valueLevel:t.config.baseBid,targetPrice:e}})})},[t.targetPriceRows,t.config.baseBid,o]);(0,n.useEffect)(()=>{let e=d.getInstance().getStatus().isOnline;if("Running"!==t.botSystemStatus||0===t.targetPriceRows.length||t.currentMarketPrice<=0||!e){e||"Running"!==t.botSystemStatus||console.log("\uD83D\uDD34 Trading paused - network offline");return}let{config:r,currentMarketPrice:n,targetPriceRows:a,crypto1Balance:c,crypto2Balance:l}=t,p=[...a].sort((e,t)=>e.targetPrice-t.targetPrice),u=c,g=l,y=0;console.log(`🚀 CONTINUOUS TRADING: Price $${n.toFixed(2)} | Targets: ${p.length} | Balance: $${g} ${r.crypto2}`);let S=p.filter(e=>Math.abs(n-e.targetPrice)/n*100<=r.slippagePercent);S.length>0&&console.log(`🎯 TARGETS IN RANGE (\xb1${r.slippagePercent}%):`,S.map(e=>`Counter ${e.counter} (${e.status})`));for(let e=0;e<p.length;e++){let t=p[e];if(Math.abs(n-t.targetPrice)/n*100<=r.slippagePercent){if("SimpleSpot"===r.tradingMode){if("Free"===t.status){let e=t.valueLevel;if(g>=e){let a=e/n;o({type:"UPDATE_TARGET_PRICE_ROW",payload:{...t,status:"Full",orderLevel:t.orderLevel+1,valueLevel:r.baseBid*Math.pow(r.multiplier,t.orderLevel+1),crypto1AmountHeld:a,originalCostCrypto2:e,crypto1Var:a,crypto2Var:-e}}),o({type:"UPDATE_BALANCES",payload:{crypto1:u+a,crypto2:g-e}}),o({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:`${r.crypto1}/${r.crypto2}`,crypto1:r.crypto1,orderType:"BUY",amountCrypto1:a,avgPrice:n,valueCrypto2:e,price1:n,crypto1Symbol:r.crypto1||"",crypto2Symbol:r.crypto2||"",tradingMode:"SimpleSpot",type:"BUY",costBasisCrypto2:e}}),console.log(`✅ BUY: Counter ${t.counter} bought ${a.toFixed(6)} ${r.crypto1} at $${n.toFixed(2)}`),s({title:"BUY Executed",description:`Counter ${t.counter}: ${a.toFixed(6)} ${r.crypto1}`,duration:2e3}),m("soundOrderExecution"),h(`🟢 <b>BUY EXECUTED</b>
📊 Counter: ${t.counter}
💰 Amount: ${a.toFixed(6)} ${r.crypto1}
💵 Price: $${n.toFixed(2)}
💸 Cost: $${e.toFixed(2)} ${r.crypto2}
📈 Mode: Simple Spot`),y++,g-=e,u+=a}}let e=t.counter,a=p.find(t=>t.counter===e-1);if(a&&"Full"===a.status&&a.crypto1AmountHeld&&a.originalCostCrypto2){let t=a.crypto1AmountHeld,c=t*n,l=c-a.originalCostCrypto2,d=n>0?l*r.incomeSplitCrypto1Percent/100/n:0;o({type:"UPDATE_TARGET_PRICE_ROW",payload:{...a,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:r.baseBid*Math.pow(r.multiplier,a.orderLevel),crypto1Var:-t,crypto2Var:c}}),o({type:"UPDATE_BALANCES",payload:{crypto1:u-t,crypto2:g+c}}),o({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:`${r.crypto1}/${r.crypto2}`,crypto1:r.crypto1,orderType:"SELL",amountCrypto1:t,avgPrice:n,valueCrypto2:c,price1:n,crypto1Symbol:r.crypto1||"",crypto2Symbol:r.crypto2||"",realizedProfitLossCrypto2:l,realizedProfitLossCrypto1:d,tradingMode:"SimpleSpot",type:"SELL",costBasisCrypto2:a.originalCostCrypto2,percentageGain:a.originalCostCrypto2>0?(c-a.originalCostCrypto2)/a.originalCostCrypto2*100:0}}),console.log(`✅ SELL: Counter ${e-1} sold ${t.toFixed(6)} ${r.crypto1}. Profit: $${l.toFixed(2)}`),s({title:"SELL Executed",description:`Counter ${e-1}: Profit $${l.toFixed(2)}`,duration:2e3}),m("soundOrderExecution");let p=l>0?"\uD83D\uDCC8":l<0?"\uD83D\uDCC9":"➖";h(`🔴 <b>SELL EXECUTED</b>
📊 Counter: ${e-1}
💰 Amount: ${t.toFixed(6)} ${r.crypto1}
💵 Price: $${n.toFixed(2)}
💸 Received: $${c.toFixed(2)} ${r.crypto2}
${p} Profit: $${l.toFixed(2)} ${r.crypto2}
📈 Mode: Simple Spot`),y++,u-=t,g+=c}}else if("StablecoinSwap"===r.tradingMode){if("Free"===t.status){let e=t.valueLevel;if(g>=e){let a=f(r.crypto2||"USDT")/f(r.preferredStablecoin||"USDT"),c=e*a,l=f(r.crypto1||"BTC")/f(r.preferredStablecoin||"USDT"),d=c/l,p=t.orderLevel+1,S=r.baseBid*Math.pow(r.multiplier,p);o({type:"UPDATE_TARGET_PRICE_ROW",payload:{...t,status:"Full",orderLevel:p,valueLevel:S,crypto1AmountHeld:d,originalCostCrypto2:e,crypto1Var:d,crypto2Var:-e}}),o({type:"UPDATE_BALANCES",payload:{crypto1:u+d,crypto2:g-e}}),o({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:`${r.crypto2}/${r.preferredStablecoin}`,crypto1:r.crypto2,orderType:"SELL",amountCrypto1:e,avgPrice:a,valueCrypto2:c,price1:a,crypto1Symbol:r.crypto2||"",crypto2Symbol:r.preferredStablecoin||"",tradingMode:"StablecoinSwap",type:"BUY",intermediateStablecoinAmount:c,stablecoinPrice:a}});let b=d*(n||l),v=b-e,T=l>0?v*r.incomeSplitCrypto1Percent/100/l:0;o({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:`${r.crypto1}/${r.preferredStablecoin}`,crypto1:r.crypto1,orderType:"BUY",amountCrypto1:d,avgPrice:l,valueCrypto2:c,price1:l,crypto1Symbol:r.crypto1||"",crypto2Symbol:r.preferredStablecoin||"",tradingMode:"StablecoinSwap",type:"BUY",costBasisCrypto2:e,intermediateStablecoinAmount:c,stablecoinPrice:l,potentialProfitLossCrypto2:v,potentialProfitLossCrypto1:T,currentMarketValueCrypto2:b}}),console.log(`✅ STABLECOIN BUY: Counter ${t.counter} | Step 1: Sold ${e} ${r.crypto2} → ${c.toFixed(2)} ${r.preferredStablecoin} | Step 2: Bought ${d.toFixed(6)} ${r.crypto1} | Level: ${t.orderLevel} → ${p}`),s({title:"BUY Executed (Stablecoin)",description:`Counter ${t.counter}: ${d.toFixed(6)} ${r.crypto1} via ${r.preferredStablecoin}`,duration:2e3}),m("soundOrderExecution"),h(`🟢 <b>BUY EXECUTED (Stablecoin Swap)</b>
📊 Counter: ${t.counter}
🔄 Step 1: Sold ${e.toFixed(2)} ${r.crypto2} → ${c.toFixed(2)} ${r.preferredStablecoin}
🔄 Step 2: Bought ${d.toFixed(6)} ${r.crypto1}
📊 Level: ${t.orderLevel} → ${p}
📈 Mode: Stablecoin Swap`),y++,g-=e,u+=d}}let e=t.counter,a=p.find(t=>t.counter===e-1);if(a&&"Full"===a.status&&a.crypto1AmountHeld&&a.originalCostCrypto2){let t=a.crypto1AmountHeld,n=f(r.crypto1||"BTC")/f(r.preferredStablecoin||"USDT"),c=t*n,l=f(r.crypto2||"USDT")/f(r.preferredStablecoin||"USDT"),d=c/l,p=a.originalCostCrypto2||0,S=d-p,b=p>0?(d-p)/p*100:0,v=n>0?S*r.incomeSplitCrypto1Percent/100/n:0;o({type:"UPDATE_TARGET_PRICE_ROW",payload:{...a,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:r.baseBid*Math.pow(r.multiplier,a.orderLevel),crypto1Var:0,crypto2Var:0}}),o({type:"UPDATE_BALANCES",payload:{crypto1:u-t,crypto2:g+d}}),o({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:`${r.crypto1}/${r.preferredStablecoin}`,crypto1:r.crypto1,orderType:"SELL",amountCrypto1:t,avgPrice:n,valueCrypto2:c,price1:n,crypto1Symbol:r.crypto1||"",crypto2Symbol:r.preferredStablecoin||"",tradingMode:"StablecoinSwap",type:"SELL",intermediateStablecoinAmount:c,stablecoinPrice:n}}),o({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:`${r.crypto2}/${r.preferredStablecoin}`,crypto1:r.crypto2,orderType:"BUY",amountCrypto1:d,avgPrice:l,valueCrypto2:c,price1:l,crypto1Symbol:r.crypto2||"",crypto2Symbol:r.preferredStablecoin||"",realizedProfitLossCrypto2:S,realizedProfitLossCrypto1:v,tradingMode:"StablecoinSwap",type:"SELL",costBasisCrypto2:p,percentageGain:b,intermediateStablecoinAmount:c,stablecoinPrice:l,stepAAmount:t,stepAPrice:n,stepBAmount:d,stepBPrice:l,twoStepProcess:!0}}),console.log(`✅ STABLECOIN SELL: Counter ${e-1} | Step A: Sold ${t.toFixed(6)} ${r.crypto1} → ${c.toFixed(2)} ${r.preferredStablecoin} | Step B: Bought ${d.toFixed(2)} ${r.crypto2} | Profit: ${S.toFixed(2)} ${r.crypto2} | Level: ${a.orderLevel} (unchanged)`),s({title:"SELL Executed (Stablecoin)",description:`Counter ${e-1}: Profit ${S.toFixed(2)} ${r.crypto2} via ${r.preferredStablecoin}`,duration:2e3}),m("soundOrderExecution");let T=S>0?"\uD83D\uDCC8":S<0?"\uD83D\uDCC9":"➖";h(`🔴 <b>SELL EXECUTED (Stablecoin Swap)</b>
📊 Counter: ${e-1}
🔄 Step A: Sold ${t.toFixed(6)} ${r.crypto1} → ${c.toFixed(2)} ${r.preferredStablecoin}
🔄 Step B: Bought ${d.toFixed(2)} ${r.crypto2}
${T} Profit: ${S.toFixed(2)} ${r.crypto2}
📊 Level: ${a.orderLevel} (unchanged)
📈 Mode: Stablecoin Swap`),y++,u-=t,g+=d}}}}y>0&&console.log(`🎯 CYCLE COMPLETE: ${y} actions taken at price $${n.toFixed(2)}`)},[t.botSystemStatus,t.currentMarketPrice,t.targetPriceRows,t.config,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,o,s,m,h]);let C=(0,n.useCallback)(()=>t.targetPriceRows&&Array.isArray(t.targetPriceRows)?t.targetPriceRows.map(e=>{let o,r;let n=t.currentMarketPrice||0,s=e.targetPrice||0,i=n&&s?(n/s-1)*100:0;if("Full"===e.status&&e.crypto1AmountHeld&&e.originalCostCrypto2){let s=n*e.crypto1AmountHeld-e.originalCostCrypto2;if("StablecoinSwap"===t.config.tradingMode){let e=i>=0?1:-1,a=Math.abs(s);r=e*(a*t.config.incomeSplitCrypto2Percent)/100,n>0&&(o=e*(a*t.config.incomeSplitCrypto1Percent/100)/n)}else r=s*t.config.incomeSplitCrypto2Percent/100,n>0&&(o=s*t.config.incomeSplitCrypto1Percent/100/n)}return{...e,currentPrice:n,priceDifference:s-n,priceDifferencePercent:n>0?(s-n)/n*100:0,potentialProfitCrypto1:t.config.incomeSplitCrypto1Percent/100*e.valueLevel/(s||1),potentialProfitCrypto2:t.config.incomeSplitCrypto2Percent/100*e.valueLevel,percentFromActualPrice:i,incomeCrypto1:o,incomeCrypto2:r}}).sort((e,t)=>t.targetPrice-e.targetPrice):[],[t.targetPriceRows,t.currentMarketPrice,t.config.incomeSplitCrypto1Percent,t.config.incomeSplitCrypto2Percent,t.config.baseBid,t.config.multiplier]),A=(0,n.useCallback)(async e=>{try{let o={name:`${e.crypto1}/${e.crypto2} ${e.tradingMode}`,tradingMode:e.tradingMode,crypto1:e.crypto1,crypto2:e.crypto2,baseBid:e.baseBid,multiplier:e.multiplier,numDigits:e.numDigits,slippagePercent:e.slippagePercent,incomeSplitCrypto1Percent:e.incomeSplitCrypto1Percent,incomeSplitCrypto2Percent:e.incomeSplitCrypto2Percent,preferredStablecoin:e.preferredStablecoin,targetPrices:t.targetPriceRows.map(e=>e.targetPrice)},r=await c.oc.saveConfig(o);return console.log("✅ Config saved to backend:",r),r.config?.id||null}catch(e){return console.error("❌ Failed to save config to backend:",e),s({title:"Backend Error",description:"Failed to save configuration to backend",variant:"destructive",duration:3e3}),null}},[t.targetPriceRows,s]),w=(0,n.useCallback)(async e=>{try{let t=await c.oc.startBot(e);return console.log("✅ Bot started on backend:",t),s({title:"Bot Started",description:"Trading bot started successfully on backend",duration:3e3}),!0}catch(e){return console.error("❌ Failed to start bot on backend:",e),s({title:"Backend Error",description:"Failed to start bot on backend",variant:"destructive",duration:3e3}),!1}},[s]),D=(0,n.useCallback)(async e=>{try{let t=await c.oc.stopBot(e);return console.log("✅ Bot stopped on backend:",t),s({title:"Bot Stopped",description:"Trading bot stopped successfully on backend",duration:3e3}),!0}catch(e){return console.error("❌ Failed to stop bot on backend:",e),s({title:"Backend Error",description:"Failed to stop bot on backend",variant:"destructive",duration:3e3}),!1}},[s]),M=(0,n.useCallback)(async()=>{let e="http://localhost:5000";if(!e){console.error("Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed."),o({type:"SET_BACKEND_STATUS",payload:"offline"});return}try{let t=await fetch(`${e}/health/`);if(!t.ok){console.error(`Backend health check failed with status: ${t.status} ${t.statusText}`);let e=await t.text().catch(()=>"Could not read response text.");console.error("Backend health check response body:",e)}o({type:"SET_BACKEND_STATUS",payload:t.ok?"online":"offline"})}catch(t){o({type:"SET_BACKEND_STATUS",payload:"offline"}),console.error("Backend connectivity check failed. Error details:",t),t.cause&&console.error("Fetch error cause:",t.cause),console.error("Attempted to fetch API URL:",`${e}/health/`)}},[o]);(0,n.useEffect)(()=>{M()},[M]),(0,n.useEffect)(()=>{B(t)},[t]),(0,n.useEffect)(()=>{"WarmingUp"===t.botSystemStatus&&(console.log("Bot is Warming Up... Immediate execution enabled."),o({type:"SYSTEM_COMPLETE_WARMUP"}),console.log("Bot is now Running immediately."))},[t.botSystemStatus,o]),(0,n.useEffect)(()=>{let e=l.C.getInstance(),o=e.getCurrentSessionId();o&&("Running"===t.botSystemStatus?(e.startSessionRuntime(o),console.log("✅ Started runtime tracking for session:",o)):"Stopped"===t.botSystemStatus&&(e.stopSessionRuntime(o),console.log("⏹️ Stopped runtime tracking for session:",o)))},[t.botSystemStatus]),(0,n.useEffect)(()=>{let e=l.C.getInstance();"WarmingUp"===t.botSystemStatus&&!e.getCurrentSessionId()&&t.config.crypto1&&t.config.crypto2&&t.targetPriceRows.length>0&&e.createNewSessionWithAutoName(t.config).then(t=>{e.setCurrentSession(t),console.log("✅ Auto-created session:",t)}).catch(e=>{console.error("❌ Failed to auto-create session:",e)});let o=e.getCurrentSessionId();o&&("Running"===t.botSystemStatus?(e.startSessionRuntime(o),console.log("⏱️ Started runtime tracking for session:",o)):"Stopped"===t.botSystemStatus&&(e.stopSessionRuntime(o),console.log("⏹️ Stopped runtime tracking for session:",o)))},[t.botSystemStatus,t.config.crypto1,t.config.crypto2]),(0,n.useEffect)(()=>{let e=l.C.getInstance(),r=e.getCurrentSessionId();if(r&&"Running"===t.botSystemStatus){let n=e.loadSession(r);if(n&&(n.config.crypto1!==t.config.crypto1||n.config.crypto2!==t.config.crypto2)){if(console.log("\uD83D\uDD04 Crypto pair changed during active trading, auto-saving and resetting..."),"Running"===t.botSystemStatus||t.targetPriceRows.length>0||t.orderHistory.length>0){"Running"===t.botSystemStatus&&D();let o=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),r=`${n.name} (AutoSaved ${o})`;e.createNewSession(r,n.config).then(o=>{e.saveSession(o,n.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,!1),console.log("\uD83D\uDCBE AutoSaved session:",r)})}o({type:"RESET_FOR_NEW_CRYPTO"}),t.config.crypto1&&t.config.crypto2&&e.createNewSessionWithAutoName(t.config).then(o=>{e.setCurrentSession(o),console.log("\uD83C\uDD95 Created new session for crypto pair during active trading:",t.config.crypto1,"/",t.config.crypto2),s({title:"Crypto Pair Changed During Trading",description:`Previous session AutoSaved. New session created for ${t.config.crypto1}/${t.config.crypto2}`,duration:5e3})})}}},[t.config.crypto1,t.config.crypto2,t.botSystemStatus]),(0,n.useEffect)(()=>{let e=d.getInstance(),r=p.getInstance(),n=u.getInstance(),i=l.C.getInstance(),a=e.addListener((e,r)=>{if(console.log(`🌐 Network status changed: ${e?"Online":"Offline"}`),e||r)e&&!r&&s({title:"Network Reconnected",description:"Connection restored. You can resume trading.",duration:3e3});else{if("Running"===t.botSystemStatus){console.log("\uD83D\uDD34 Internet lost - stopping bot and saving session"),o({type:"SYSTEM_STOP_BOT"});let e=l.C.getInstance(),r=e.getCurrentSessionId();if(r){let o=e.loadSession(r);if(o){let r=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),n=`${o.name} (Offline Backup ${r})`;e.createNewSession(n,o.config).then(o=>{e.saveSession(o,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,!1),console.log("\uD83D\uDCBE Created offline backup session:",n)})}}}s({title:"Network Disconnected",description:"Bot stopped and session saved. Trading paused until connection restored.",variant:"destructive",duration:8e3})}}),c=n.addListener(e=>{let t=e.usedJSHeapSize/1024/1024;t>150&&(console.warn(`🧠 High memory usage: ${t.toFixed(2)}MB`),s({title:"High Memory Usage",description:`Memory usage is high (${t.toFixed(0)}MB). Consider refreshing the page.`,variant:"destructive",duration:5e3}))}),g=()=>{try{let e=i.getCurrentSessionId();e&&i.saveSession(e,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,"Running"===t.botSystemStatus),B(t)}catch(e){console.error("Auto-save failed:",e)}};r.enable(g,3e4);let y=e=>{if(g(),"Running"===t.botSystemStatus){let t="Trading bot is currently running. Are you sure you want to leave?";return e.returnValue=t,t}};return window.addEventListener("beforeunload",y),()=>{a(),c(),r.disable(),window.removeEventListener("beforeunload",y)}},[t,s]),(0,n.useEffect)(()=>{p.getInstance().saveNow()},[t.botSystemStatus]),(0,n.useEffect)(()=>{let e=localStorage.getItem(P);if(e)try{let t=JSON.parse(e);o({type:"RESTORE_SESSION",payload:t}),console.log("\uD83D\uDD04 Session restored from localStorage - manual bot start required")}catch(e){console.error("Failed to restore session:",e)}},[]),(0,n.useEffect)(()=>{let e=setInterval(()=>{t.isTrading&&$(t)},3e4);return()=>clearInterval(e)},[t.isTrading,t]),(0,n.useEffect)(()=>{I(t),T(t)},[t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,t.totalProfitLoss]);let _=(0,n.useCallback)(()=>{try{let e=l.C.getInstance(),o=e.getCurrentSessionId();if(!o){if(t.config.crypto1&&t.config.crypto2)return e.createNewSessionWithAutoName(t.config).then(o=>{e.setCurrentSession(o),e.saveSession(o,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,"Running"===t.botSystemStatus)}),!0;return!1}return e.saveSession(o,t.config,t.targetPriceRows,t.orderHistory,t.currentMarketPrice,t.crypto1Balance,t.crypto2Balance,t.stablecoinBalance,"Running"===t.botSystemStatus)}catch(e){return console.error("Failed to save current session:",e),!1}},[t]),N=(0,n.useCallback)(async(e,o)=>{await h(e,o,t.telegramConfig)},[t.telegramConfig]),F=(0,n.useCallback)(()=>{let e={crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0,totalProfitLoss:0};return o({type:"UPDATE_BALANCES",payload:{crypto1:e.crypto1Balance,crypto2:e.crypto2Balance,stablecoin:e.stablecoinBalance}}),localStorage.removeItem(b),console.log("\uD83D\uDD04 Global balances reset to defaults"),e},[]),U=(0,n.useCallback)(e=>{L(e,t.sessionAlarmConfig)},[t.sessionAlarmConfig]),H=(0,n.useCallback)(async(e,t)=>await O(e,t),[]),j=(0,n.useCallback)(()=>"StablecoinSwap"===t.config.tradingMode?t.orderHistory.filter(e=>("SELL"===e.type||"SELL"===e.orderType)&&"StablecoinSwap"===e.tradingMode&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0):t.orderHistory.filter(e=>("SELL"===e.type||"SELL"===e.orderType)&&("SimpleSpot"===e.tradingMode||!e.tradingMode)&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0),[t.orderHistory,t.config.tradingMode]),Y={...t,dispatch:o,setTargetPrices:v,getDisplayOrders:C,checkBackendStatus:M,fetchMarketPrice:S,startBackendBot:w,stopBackendBot:D,saveConfigToBackend:A,saveCurrentSession:_,sendTelegramNotification:N,playSessionAlarm:U,fetchStablecoinSwapPrice:H,calculateTotalPL:j,resetGlobalBalances:F,backendStatus:t.backendStatus,botSystemStatus:t.botSystemStatus,isBotActive:"Running"===t.botSystemStatus};return(0,r.jsx)(x.Provider,{value:Y,children:e})},M=()=>{let e=(0,n.useContext)(x);if(void 0===e)throw Error("useTradingContext must be used within a TradingProvider");return e}},79737:(e,t,o)=>{"use strict";o.d(t,{Toaster:()=>r});let r=(0,o(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\components\\ui\\toaster.tsx","Toaster")},86514:(e,t,o)=>{Promise.resolve().then(o.bind(o,79737)),Promise.resolve().then(o.bind(o,26443)),Promise.resolve().then(o.bind(o,29131)),Promise.resolve().then(o.bind(o,47506))},94431:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>p,metadata:()=>d});var r=o(37413),n=o(7653),s=o.n(n);o(61135);var i=o(79737),a=o(29131),c=o(47506),l=o(26443);let d={title:"Pluto Trading Bot",description:"Simulated cryptocurrency trading bot with Neo Brutalist UI."};function p({children:e}){return(0,r.jsx)("html",{lang:"en",className:s().variable,suppressHydrationWarning:!0,children:(0,r.jsxs)("body",{className:"font-sans antialiased",suppressHydrationWarning:!0,children:[" ",(0,r.jsx)(a.AuthProvider,{children:(0,r.jsx)(c.TradingProvider,{children:(0,r.jsxs)(l.AIProvider,{children:[e,(0,r.jsx)(i.Toaster,{})]})})})]})})}}};