(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{7926:(e,a,t)=>{Promise.resolve().then(t.bind(t,28439))},17313:(e,a,t)=>{"use strict";t.d(a,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>i});var r=t(95155),s=t(12115),n=t(30064),l=t(59434);let i=n.bL,o=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(n.B8,{ref:a,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...s})});o.displayName=n.B8.displayName;let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(n.l9,{ref:a,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...s})});c.displayName=n.l9.displayName;let d=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(n.UC,{ref:a,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...s})});d.displayName=n.UC.displayName},22346:(e,a,t)=>{"use strict";t.d(a,{w:()=>i});var r=t(95155),s=t(12115),n=t(87489),l=t(59434);let i=s.forwardRef((e,a)=>{let{className:t,orientation:s="horizontal",decorative:i=!0,...o}=e;return(0,r.jsx)(n.b,{ref:a,decorative:i,orientation:s,className:(0,l.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",t),...o})});i.displayName=n.b.displayName},26126:(e,a,t)=>{"use strict";t.d(a,{E:()=>i});var r=t(95155);t(12115);var s=t(74466),n=t(59434);let l=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:a,variant:t,...s}=e;return(0,r.jsx)("div",{className:(0,n.cn)(l({variant:t}),a),...s})}},28439:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>eT});var r=t(95155),s=t(12115),n=t(6874),l=t.n(n),i=t(35695),o=t(59435),c=t(30285),d=t(40283),m=t(18186),p=t(57082),u=t(29532),x=t(33349),h=t(26126),g=t(66695),f=t(48639),b=t(32087),y=t(37648);function v(e){let{className:a=""}=e,[t,n]=(0,s.useState)(navigator.onLine),[l,i]=(0,s.useState)(new Date);return(0,s.useEffect)(()=>{let e=()=>n(!0),a=()=>n(!1);window.addEventListener("online",e),window.addEventListener("offline",a);let t=setInterval(()=>{i(new Date)},1e3);return()=>{window.removeEventListener("online",e),window.removeEventListener("offline",a),clearInterval(t)}},[]),(0,r.jsxs)("div",{className:"flex items-center gap-2 ".concat(a),children:[(0,r.jsxs)(h.E,{variant:t?"default":"destructive",className:"flex items-center gap-1 ".concat(t?"bg-green-600 hover:bg-green-600/90 text-white":""),children:[t?(0,r.jsx)(f.A,{className:"h-3 w-3"}):(0,r.jsx)(b.A,{className:"h-3 w-3"}),t?"Online":"Offline"]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,r.jsx)(y.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:l.toLocaleTimeString()})]})]})}function j(){let{logout:e}=(0,d.A)();(0,i.useRouter)();let a=(0,i.usePathname)(),t=[{href:"/dashboard",label:"Home",icon:(0,r.jsx)(m.A,{})},{href:"/admin",label:"Admin Panel",icon:(0,r.jsx)(p.A,{})}];return(0,r.jsxs)("header",{className:"sticky top-0 z-50 flex items-center justify-between h-16 px-4 md:px-6 bg-card border-b-2 border-border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(o.A,{useFullName:!1}),(0,r.jsx)(v,{})]}),(0,r.jsxs)("nav",{className:"flex items-center gap-2 sm:gap-3",children:[t.map(e=>(0,r.jsx)(c.$,{variant:a===e.href?"default":"ghost",size:"sm",asChild:!0,className:"".concat(a===e.href?"btn-neo":"hover:bg-accent/50"),children:(0,r.jsxs)(l(),{href:e.href,className:"flex items-center gap-2",children:[e.icon,(0,r.jsx)("span",{className:"hidden sm:inline",children:e.label})]})},e.label)),(0,r.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:()=>{window.open("/dashboard?newSession=true","_blank")},className:"hover:bg-accent/50 flex items-center gap-2",title:"Open a new independent trading session in a new tab",children:[(0,r.jsx)(u.A,{}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"New Session"})]}),(0,r.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:()=>{e()},className:"hover:bg-destructive/80 hover:text-destructive-foreground flex items-center gap-2",children:[(0,r.jsx)(x.A,{}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Logout"})]})]})]})}var S=t(77213),N=t(35286),T=t(62523),w=t(85057),A=t(76981),C=t(10518),D=t(59434);let E=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(A.bL,{ref:a,className:(0,D.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...s,children:(0,r.jsx)(A.C1,{className:(0,D.cn)("flex items-center justify-center text-current"),children:(0,r.jsx)(C.A,{className:"h-4 w-4"})})})});E.displayName=A.bL.displayName;var k=t(59409),U=t(66424),M=t(22346),O=t(15452),P=t(25318);let R=O.bL;O.l9;let L=O.ZL,B=O.bm,I=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(O.hJ,{ref:a,className:(0,D.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...s})});I.displayName=O.hJ.displayName;let _=s.forwardRef((e,a)=>{let{className:t,children:s,...n}=e;return(0,r.jsxs)(L,{children:[(0,r.jsx)(I,{}),(0,r.jsxs)(O.UC,{ref:a,className:(0,D.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...n,children:[s,(0,r.jsxs)(O.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(P.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});_.displayName=O.UC.displayName;let F=e=>{let{className:a,...t}=e;return(0,r.jsx)("div",{className:(0,D.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...t})};F.displayName="DialogHeader";let G=e=>{let{className:a,...t}=e;return(0,r.jsx)("div",{className:(0,D.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};G.displayName="DialogFooter";let V=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(O.hE,{ref:a,className:(0,D.cn)("text-lg font-semibold leading-none tracking-tight",t),...s})});V.displayName=O.hE.displayName;let z=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(O.VY,{ref:a,className:(0,D.cn)("text-sm text-muted-foreground",t),...s})});z.displayName=O.VY.displayName;let H=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("textarea",{className:(0,D.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...s})});H.displayName="Textarea";var J=t(17313),W=t(87481);function Z(e){var a,t,n,l;let i,{isOpen:o,onClose:d,onSetTargetPrices:m}=e,[p,u]=(0,s.useState)("manual"),[x,h]=(0,s.useState)(""),{toast:g}=(0,W.dj)();try{i=(0,S.U)()}catch(e){console.warn("Trading context not available:",e),i=null}let[f,b]=(0,s.useState)("8"),[y,v]=(0,s.useState)("5"),[j,N]=(0,s.useState)("even"),T=(null==i?void 0:i.currentMarketPrice)||(null==i?void 0:null===(a=i.state)||void 0===a?void 0:a.currentMarketPrice)||1e5,A=(null==i?void 0:null===(t=i.config)||void 0===t?void 0:t.slippagePercent)||(null==i?void 0:null===(l=i.state)||void 0===l?void 0:null===(n=l.config)||void 0===n?void 0:n.slippagePercent)||.2,C=()=>{let e=parseInt(f),a=parseFloat(y);if(!e||e<2||e>20||!a||a<=0)return[];let t=[],r=T*(1-a/100),s=T*(1+a/100);if("even"===j)for(let a=0;a<e;a++){let n=r+a/(e-1)*(s-r);t.push(Math.round(n))}else if("fibonacci"===j){let a=[0,.236,.382,.5,.618,.764,.854,.927,1];for(let n=0;n<e;n++){let l=r+(s-r)*(a[Math.min(n,a.length-1)]||n/(e-1));t.push(Math.round(l))}}else if("exponential"===j)for(let a=0;a<e;a++){let n=r+(s-r)*Math.pow(a/(e-1),1.5);t.push(Math.round(n))}let n=3*A/100*T,l=t.sort((e,a)=>e-a),i=[];for(let e=0;e<l.length;e++){let a=l[e];if(i.length>0){let e=i[i.length-1];a-e<n&&(a=e+n)}i.push(Math.round(a))}return i},D=()=>{let e=x.split("\n").filter(e=>""!==e.trim()).map(e=>parseFloat(e.trim())).filter(e=>!isNaN(e)&&e>0).sort((e,a)=>e-a);if(e.length<2)return{hasOverlap:!1,message:""};let a=A/100*T;for(let t=0;t<e.length-1;t++)if(e[t]+a>=e[t+1]-a){let r=2*a,s=e[t+1]-e[t];return{hasOverlap:!0,message:"Overlap detected between ".concat(e[t]," and ").concat(e[t+1],". Minimum gap needed: ").concat(r.toFixed(0),", actual gap: ").concat(s.toFixed(0))}}return{hasOverlap:!1,message:"No slippage zone overlaps detected ✓"}},E=D();return(0,r.jsx)(R,{open:o,onOpenChange:d,children:(0,r.jsxs)(_,{className:"sm:max-w-2xl bg-card border-2 border-border",children:[(0,r.jsxs)(F,{children:[(0,r.jsx)(V,{className:"text-primary",children:"Set Target Prices"}),(0,r.jsx)(z,{children:"Set target prices manually or generate them automatically with optimal spacing to avoid slippage zone overlaps."})]}),(0,r.jsxs)(J.tU,{value:p,onValueChange:u,className:"w-full",children:[(0,r.jsxs)(J.j7,{className:"grid w-full grid-cols-2",children:[(0,r.jsx)(J.Xi,{value:"manual",children:"Manual Entry"}),(0,r.jsx)(J.Xi,{value:"automatic",children:"Automatic Generation"})]}),(0,r.jsx)(J.av,{value:"manual",className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(w.J,{htmlFor:"target-prices-input",className:"text-left",children:"Target Prices"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Paste target prices from Excel or enter manually, one price per line. Invalid entries will be ignored."}),(0,r.jsx)(H,{id:"target-prices-input",value:x,onChange:e=>h(e.target.value),placeholder:"50000 50500 49800",className:"min-h-[200px] bg-input border-2 border-border focus:border-primary font-mono"}),E.message&&(0,r.jsx)("p",{className:"text-sm ".concat(E.hasOverlap?"text-red-500":"text-green-500"),children:E.message})]})}),(0,r.jsxs)(J.av,{value:"automatic",className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{htmlFor:"targetCount",children:"Number of Targets"}),(0,r.jsxs)(k.l6,{value:f,onValueChange:b,children:[(0,r.jsx)(k.bq,{children:(0,r.jsx)(k.yv,{})}),(0,r.jsx)(k.gC,{children:[4,6,8,10,12,15,20].map(e=>(0,r.jsxs)(k.eb,{value:e.toString(),children:[e," targets"]},e))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{htmlFor:"priceRange",children:"Price Range (%)"}),(0,r.jsxs)(k.l6,{value:y,onValueChange:v,children:[(0,r.jsx)(k.bq,{children:(0,r.jsx)(k.yv,{})}),(0,r.jsx)(k.gC,{children:[2,2.5,3,3.5,4,4.5,5,6,7,8,10].map(e=>(0,r.jsxs)(k.eb,{value:e.toString(),children:["\xb1",e,"%"]},e))})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{htmlFor:"distribution",children:"Distribution Pattern"}),(0,r.jsxs)(k.l6,{value:j,onValueChange:N,children:[(0,r.jsx)(k.bq,{children:(0,r.jsx)(k.yv,{})}),(0,r.jsxs)(k.gC,{children:[(0,r.jsx)(k.eb,{value:"even",children:"Even Distribution"}),(0,r.jsx)(k.eb,{value:"fibonacci",children:"Fibonacci (More near current price)"}),(0,r.jsx)(k.eb,{value:"exponential",children:"Exponential (Wider spread)"})]})]})]}),(0,r.jsx)("div",{className:"bg-muted p-3 rounded-md",children:(0,r.jsxs)("p",{className:"text-sm",children:[(0,r.jsx)("strong",{children:"Current Market Price:"})," $",T.toLocaleString(),(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Slippage:"})," \xb1",A,"% ($",(T*A/100).toFixed(0),")",(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Range:"})," $",(T*(1-parseFloat(y)/100)).toLocaleString()," - $",(T*(1+parseFloat(y)/100)).toLocaleString()]})}),(0,r.jsxs)(c.$,{onClick:()=>{h(C().join("\n"))},className:"w-full btn-neo",children:["Generate ",f," Target Prices"]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(w.J,{children:"Generated Prices (Preview)"}),(0,r.jsx)(H,{value:x,onChange:e=>h(e.target.value),className:"min-h-[150px] bg-input border-2 border-border focus:border-primary font-mono",placeholder:"Click 'Generate' to create automatic target prices..."}),E.message&&(0,r.jsx)("p",{className:"text-sm ".concat(E.hasOverlap?"text-red-500":"text-green-500"),children:E.message})]})]})]}),(0,r.jsxs)(G,{children:[(0,r.jsx)(B,{asChild:!0,children:(0,r.jsx)(c.$,{type:"button",variant:"outline",className:"btn-outline-neo",children:"Cancel"})}),(0,r.jsx)(c.$,{type:"button",onClick:()=>{let e=x.split("\n").map(e=>e.trim()).filter(e=>""!==e),a=e.map(e=>parseFloat(e)).filter(e=>!isNaN(e)&&e>0);if(0===a.length&&e.length>0){g({title:"Invalid Input",description:"No valid prices found. Please enter numbers, one per line.",variant:"destructive"});return}let t=D();if(t.hasOverlap){g({title:"Slippage Zone Overlap",description:t.message,variant:"destructive"});return}m(a),g({title:"Target Prices Updated",description:"".concat(a.length," target prices have been set.")}),h(""),d()},disabled:E.hasOverlap,className:"btn-neo",children:"Save Prices"})]})]})})}var $=t(29348),X=t(11133);function Y(e){let{label:a,value:t,allowedCryptos:n,onValidCrypto:l,placeholder:i="Enter crypto symbol",description:o,className:d}=e,[m,p]=(0,s.useState)(""),[u,x]=(0,s.useState)("idle"),[h,g]=(0,s.useState)(""),[f,b]=(0,s.useState)(!1);(0,s.useEffect)(()=>{t&&t!==m?(p(t),x("valid"),b(!0)):t||b(!1)},[t]);let y=()=>{let e=m.toUpperCase().trim();if(!e){x("invalid"),g("Please enter a crypto symbol");return}if(!n||!Array.isArray(n)){x("invalid"),g("No allowed cryptocurrencies configured");return}n.includes(e)?(x("valid"),g(""),b(!0),l(e)):(x("invalid"),g("".concat(e," is not available. Allowed: ").concat(n.join(", "))))};return(0,r.jsxs)("div",{className:(0,D.cn)("space-y-2",d),children:[(0,r.jsx)(w.J,{htmlFor:"crypto-input-".concat(a),children:a}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)(T.p,{id:"crypto-input-".concat(a),value:m||t,onChange:e=>{p(e.target.value),x("idle"),g("")},onKeyPress:e=>{"Enter"===e.key&&y()},placeholder:i,className:(0,D.cn)("pr-8",(()=>{switch(u){case"valid":return"border-green-500";case"invalid":return"border-red-500";default:return""}})())}),"idle"!==u&&(0,r.jsx)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:(()=>{switch(u){case"valid":return(0,r.jsx)(C.A,{className:"h-4 w-4 text-green-500"});case"invalid":return(0,r.jsx)(P.A,{className:"h-4 w-4 text-red-500"});default:return null}})()})]}),(0,r.jsx)(c.$,{onClick:y,variant:"outline",className:"btn-neo",disabled:!m.trim(),children:"Check"})]}),t&&f&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(C.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsxs)("span",{className:"text-green-600 font-medium",children:["Selected: ",t]})]}),"invalid"===u&&h&&(0,r.jsxs)("div",{className:"flex items-start space-x-2 text-sm text-red-600",children:[(0,r.jsx)(X.A,{className:"h-4 w-4 mt-0.5 flex-shrink-0"}),(0,r.jsx)("span",{children:h})]}),o&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:o}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[n&&Array.isArray(n)?n.length:0," cryptocurrencies available"]})]})}var q=t(54073);let K=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsxs)(q.bL,{ref:a,className:(0,D.cn)("relative flex w-full touch-none select-none items-center",t),...s,children:[(0,r.jsx)(q.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,r.jsx)(q.Q6,{className:"absolute h-full bg-primary"})}),(0,r.jsx)(q.zi,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})});K.displayName=q.bL.displayName;var Q=t(18271),ee=t(60620),ea=t(13300);let et=[{value:"G_hades_curse.wav",label:"Hades Curse"},{value:"G_hades_demat.wav",label:"Hades Demat"},{value:"G_hades_mat.wav",label:"Hades Mat"},{value:"G_hades_sanctify.wav",label:"Hades Sanctify"},{value:"S_mon1.mp3",label:"Monster 1"},{value:"S_mon2.mp3",label:"Monster 2"},{value:"Satyr_atk4.wav",label:"Satyr Attack"},{value:"bells.wav",label:"Bells"},{value:"bird1.wav",label:"Bird 1"},{value:"bird7.wav",label:"Bird 7"},{value:"cheer.wav",label:"Cheer"},{value:"chest1.wav",label:"Chest"},{value:"chime2.wav",label:"Chime"},{value:"dark2.wav",label:"Dark"},{value:"foundry2.wav",label:"Foundry"},{value:"goatherd1.wav",label:"Goatherd"},{value:"marble1.wav",label:"Marble"},{value:"sanctuary1.wav",label:"Sanctuary"},{value:"space_bells4a.wav",label:"Space Bells"},{value:"sparrow1.wav",label:"Sparrow"},{value:"tax3.wav",label:"Tax"},{value:"wolf4.wav",label:"Wolf"}];function er(e){let{isOpen:a,onClose:t}=e,{sessionAlarmConfig:n,dispatch:l,playSessionAlarm:i}=(0,S.U)(),{toast:o}=(0,W.dj)(),[d,m]=(0,s.useState)(n),p=e=>{try{let a=new Audio("/ringtones/".concat(e));a.volume=d.volume/100,a.play().catch(console.error)}catch(e){console.error("Failed to play test sound:",e),o({title:"Sound Test Failed",description:"Could not play the selected sound file.",variant:"destructive"})}};return(0,r.jsx)(R,{open:a,onOpenChange:t,children:(0,r.jsxs)(_,{className:"sm:max-w-[500px] max-h-[80vh]",children:[(0,r.jsxs)(F,{children:[(0,r.jsxs)(V,{className:"flex items-center gap-2",children:[(0,r.jsx)(Q.A,{className:"h-5 w-5"}),"Session Alarm Settings"]}),(0,r.jsx)(z,{children:"Configure custom alarm sounds and settings for this trading session."})]}),(0,r.jsxs)("div",{className:"space-y-6 overflow-y-auto max-h-[60vh] pr-2",children:[(0,r.jsxs)(g.Zp,{children:[(0,r.jsx)(g.aR,{children:(0,r.jsxs)(g.ZB,{className:"text-sm flex items-center gap-2",children:[(0,r.jsx)(ee.A,{className:"h-4 w-4"}),"Volume Control"]})}),(0,r.jsx)(g.Wu,{children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(w.J,{children:["Volume: ",d.volume,"%"]}),(0,r.jsx)(K,{value:[d.volume],onValueChange:e=>m({...d,volume:e[0]}),max:100,min:0,step:5,className:"w-full"})]})})]}),(0,r.jsxs)(g.Zp,{children:[(0,r.jsx)(g.aR,{children:(0,r.jsx)(g.ZB,{className:"text-sm text-green-600",children:"Order Execution Success Alarms"})}),(0,r.jsxs)(g.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(E,{id:"buyAlarmEnabled",checked:d.buyAlarmEnabled,onCheckedChange:e=>m({...d,buyAlarmEnabled:e})}),(0,r.jsx)(w.J,{htmlFor:"buyAlarmEnabled",children:"Enable alerts on successful order execution"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{children:"Success Alarm Sound"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(k.l6,{value:d.buyAlarmSound,onValueChange:e=>m({...d,buyAlarmSound:e}),children:[(0,r.jsx)(k.bq,{className:"flex-1",children:(0,r.jsx)(k.yv,{})}),(0,r.jsx)(k.gC,{className:"max-h-[200px] overflow-y-auto",children:et.map(e=>(0,r.jsx)(k.eb,{value:e.value,children:e.label},e.value))})]}),(0,r.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>p(d.buyAlarmSound),disabled:!d.buyAlarmEnabled,children:(0,r.jsx)(ea.A,{className:"h-4 w-4"})})]})]})]})]}),(0,r.jsxs)(g.Zp,{children:[(0,r.jsx)(g.aR,{children:(0,r.jsx)(g.ZB,{className:"text-sm text-red-600",children:"Error/Failure Alarms"})}),(0,r.jsxs)(g.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(E,{id:"sellAlarmEnabled",checked:d.sellAlarmEnabled,onCheckedChange:e=>m({...d,sellAlarmEnabled:e})}),(0,r.jsx)(w.J,{htmlFor:"sellAlarmEnabled",children:"Enable alerts on errors/failures"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{children:"Error Alarm Sound"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(k.l6,{value:d.sellAlarmSound,onValueChange:e=>m({...d,sellAlarmSound:e}),children:[(0,r.jsx)(k.bq,{className:"flex-1",children:(0,r.jsx)(k.yv,{})}),(0,r.jsx)(k.gC,{className:"max-h-[200px] overflow-y-auto",children:et.map(e=>(0,r.jsx)(k.eb,{value:e.value,children:e.label},e.value))})]}),(0,r.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>p(d.sellAlarmSound),disabled:!d.sellAlarmEnabled,children:(0,r.jsx)(ea.A,{className:"h-4 w-4"})})]})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,r.jsx)(c.$,{variant:"outline",onClick:t,children:"Cancel"}),(0,r.jsx)(c.$,{onClick:()=>{l({type:"SET_SESSION_ALARM_CONFIG",payload:d}),o({title:"Alarm Settings Saved",description:"Your session-specific alarm settings have been updated.",duration:3e3}),t()},className:"btn-neo",children:"Save Settings"})]})]})]})})}var es=t(94063),en=t(73672);async function el(e,a,t,r){try{if(!t.enabled||!t.botToken||!t.chatId)return console.warn("Telegram notifications disabled or not configured"),!1;let s={error:"\uD83D\uDEA8",success:"✅",info:"ℹ️",warning:"⚠️",trade:"\uD83D\uDCB0",connection:"\uD83C\uDF10"}[a]||"ℹ️",n=function(e,a,t,r){let s=new Date().toLocaleString(),n="".concat(t," <b>PLUTO TRADING BOT</b>\n\n").concat(e,"\n\n⏰ ").concat(s);return r&&Object.keys(r).length>0&&(n+="\n\n<i>Additional Info:</i>",Object.entries(r).forEach(e=>{let[a,t]=e;n+="\n• ".concat(a,": ").concat(t)})),n}(e,0,s,r),l=await fetch("https://api.telegram.org/bot".concat(t.botToken,"/sendMessage"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:t.chatId,text:n,parse_mode:"HTML",disable_web_page_preview:!0})});if(!l.ok)throw Error("Telegram API error: ".concat(l.status," ").concat(l.statusText));return console.log("\uD83D\uDCF1 Telegram notification sent: ".concat(a)),!0}catch(e){return console.error("Failed to send Telegram notification:",e),!1}}async function ei(e){return await el("Telegram notifications are working correctly! \uD83C\uDF89","success",e,{test:!0,timestamp:new Date().toISOString()})}function eo(e){let a=[];return e.enabled?(e.botToken?e.botToken.match(/^\d+:[A-Za-z0-9_-]+$/)||a.push("Invalid bot token format"):a.push("Bot token is required"),e.chatId?e.chatId.match(/^-?\d+$/)||a.push("Invalid chat ID format"):a.push("Chat ID is required"),{valid:0===a.length,errors:a}):{valid:!0,errors:[]}}function ec(e){let{isOpen:a,onClose:t}=e,{telegramConfig:n,dispatch:l}=(0,S.U)(),{toast:i}=(0,W.dj)(),[o,d]=(0,s.useState)(n),[m,p]=(0,s.useState)(!1),u=async()=>{let e=eo(o);if(!e.valid){i({title:"Configuration Error",description:e.errors.join(", "),variant:"destructive"});return}p(!0);try{await ei(o)?i({title:"Test Successful",description:"Test message sent successfully! Check your Telegram chat.",duration:5e3}):i({title:"Test Failed",description:"Could not send test message. Please check your configuration.",variant:"destructive"})}catch(e){i({title:"Test Failed",description:"An error occurred while testing the connection.",variant:"destructive"})}finally{p(!1)}};return(0,r.jsx)(R,{open:a,onOpenChange:t,children:(0,r.jsxs)(_,{className:"sm:max-w-[600px] max-h-[90vh] flex flex-col",children:[(0,r.jsxs)(F,{className:"flex-shrink-0",children:[(0,r.jsxs)(V,{className:"flex items-center gap-2",children:[(0,r.jsx)(es.A,{className:"h-5 w-5"}),"Telegram Notifications"]}),(0,r.jsx)(z,{children:"Configure Telegram notifications for trading alerts, errors, and system events."})]}),(0,r.jsxs)("div",{className:"space-y-6 overflow-y-auto flex-1 pr-2",children:[(0,r.jsxs)(g.Zp,{children:[(0,r.jsx)(g.aR,{children:(0,r.jsx)(g.ZB,{className:"text-sm",children:"Notification Settings"})}),(0,r.jsx)(g.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(E,{id:"telegramEnabled",checked:o.enabled,onCheckedChange:e=>d({...o,enabled:e})}),(0,r.jsx)(w.J,{htmlFor:"telegramEnabled",children:"Enable Telegram notifications"})]})})]}),o.enabled&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(g.Zp,{children:[(0,r.jsxs)(g.aR,{children:[(0,r.jsx)(g.ZB,{className:"text-sm",children:"Bot Configuration"}),(0,r.jsx)(g.BT,{children:"Create a Telegram bot via @BotFather and get your bot token."})]}),(0,r.jsxs)(g.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"botToken",children:"Bot Token"}),(0,r.jsx)(T.p,{id:"botToken",type:"password",value:o.botToken,onChange:e=>d({...o,botToken:e.target.value}),placeholder:"123456789:ABCdefGHIjklMNOpqrsTUVwxyz"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Format: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.J,{htmlFor:"chatId",children:"Chat ID"}),(0,r.jsx)(T.p,{id:"chatId",value:o.chatId,onChange:e=>d({...o,chatId:e.target.value}),placeholder:"-1001234567890 or 123456789"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Your personal chat ID or group chat ID (starts with -)"})]})]})]}),(0,r.jsxs)(g.Zp,{children:[(0,r.jsx)(g.aR,{children:(0,r.jsx)(g.ZB,{className:"text-sm",children:"Setup Instructions"})}),(0,r.jsx)(g.Wu,{children:(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"font-medium text-primary",children:"1."}),(0,r.jsx)("span",{children:"Message @BotFather on Telegram and create a new bot with /newbot"})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"font-medium text-primary",children:"2."}),(0,r.jsx)("span",{children:"Copy the bot token and paste it above"})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"font-medium text-primary",children:"3."}),(0,r.jsx)("span",{children:"Message @userinfobot to get your chat ID"})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"font-medium text-primary",children:"4."}),(0,r.jsx)("span",{children:"Start a conversation with your bot first"})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"font-medium text-primary",children:"5."}),(0,r.jsx)("span",{children:"Test the connection using the button below"})]})]})})]}),(0,r.jsxs)(g.Zp,{children:[(0,r.jsx)(g.aR,{children:(0,r.jsx)(g.ZB,{className:"text-sm",children:"Test Connection"})}),(0,r.jsx)(g.Wu,{children:(0,r.jsx)(c.$,{onClick:u,disabled:m||!o.botToken||!o.chatId,className:"w-full",variant:"outline",children:m?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"}),"Sending Test Message..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(en.A,{className:"h-4 w-4 mr-2"}),"Send Test Message"]})})})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-border flex-shrink-0",children:[(0,r.jsx)(c.$,{variant:"outline",onClick:t,children:"Cancel"}),(0,r.jsx)(c.$,{onClick:()=>{let e=eo(o);if(!e.valid){i({title:"Configuration Error",description:e.errors.join(", "),variant:"destructive"});return}l({type:"SET_TELEGRAM_CONFIG",payload:o}),i({title:"Telegram Settings Saved",description:"Your Telegram notification settings have been updated.",duration:3e3}),t()},className:"btn-neo",children:"Save Settings"})]})]})})}var ed=t(80659),em=t(75074),ep=t(50594);let eu=[{id:"BTC_USDT",crypto1:"BTC",crypto2:"USDT",displayName:"Bitcoin / Tether",category:"major",minTradeAmount:1e-4,maxTradeAmount:10,priceDecimals:2,amountDecimals:6,description:"The most popular cryptocurrency pair",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"ETH_USDT",crypto1:"ETH",crypto2:"USDT",displayName:"Ethereum / Tether",category:"major",minTradeAmount:.001,maxTradeAmount:100,priceDecimals:2,amountDecimals:6,description:"Second largest cryptocurrency by market cap",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"BNB_USDT",crypto1:"BNB",crypto2:"USDT",displayName:"Binance Coin / Tether",category:"major",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Binance exchange native token",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"ADA_USDT",crypto1:"ADA",crypto2:"USDT",displayName:"Cardano / Tether",category:"major",minTradeAmount:1,maxTradeAmount:1e5,priceDecimals:4,amountDecimals:2,description:"Proof-of-stake blockchain platform",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"SOL_USDT",crypto1:"SOL",crypto2:"USDT",displayName:"Solana / Tether",category:"major",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"High-performance blockchain for DeFi and NFTs",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"DOT_USDT",crypto1:"DOT",crypto2:"USDT",displayName:"Polkadot / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:1e4,priceDecimals:3,amountDecimals:3,description:"Multi-chain interoperability protocol",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"LINK_USDT",crypto1:"LINK",crypto2:"USDT",displayName:"Chainlink / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:5e3,priceDecimals:3,amountDecimals:3,description:"Decentralized oracle network",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"MATIC_USDT",crypto1:"MATIC",crypto2:"USDT",displayName:"Polygon / Tether",category:"altcoin",minTradeAmount:1,maxTradeAmount:5e4,priceDecimals:4,amountDecimals:2,description:"Ethereum scaling solution",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"AVAX_USDT",crypto1:"AVAX",crypto2:"USDT",displayName:"Avalanche / Tether",category:"altcoin",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Fast and eco-friendly blockchain platform",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"ATOM_USDT",crypto1:"ATOM",crypto2:"USDT",displayName:"Cosmos / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:5e3,priceDecimals:3,amountDecimals:3,description:"Internet of blockchains",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"UNI_USDT",crypto1:"UNI",crypto2:"USDT",displayName:"Uniswap / Tether",category:"defi",minTradeAmount:.1,maxTradeAmount:5e3,priceDecimals:3,amountDecimals:3,description:"Leading decentralized exchange token",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"AAVE_USDT",crypto1:"AAVE",crypto2:"USDT",displayName:"Aave / Tether",category:"defi",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Decentralized lending protocol",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"COMP_USDT",crypto1:"COMP",crypto2:"USDT",displayName:"Compound / Tether",category:"defi",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Algorithmic money market protocol",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"SUSHI_USDT",crypto1:"SUSHI",crypto2:"USDT",displayName:"SushiSwap / Tether",category:"defi",minTradeAmount:.1,maxTradeAmount:1e4,priceDecimals:4,amountDecimals:3,description:"Community-driven DEX and DeFi platform",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"CRV_USDT",crypto1:"CRV",crypto2:"USDT",displayName:"Curve DAO / Tether",category:"defi",minTradeAmount:1,maxTradeAmount:5e4,priceDecimals:4,amountDecimals:2,description:"Decentralized exchange for stablecoins",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"DOGE_USDT",crypto1:"DOGE",crypto2:"USDT",displayName:"Dogecoin / Tether",category:"meme",minTradeAmount:10,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:0,description:"The original meme cryptocurrency",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"SHIB_USDT",crypto1:"SHIB",crypto2:"USDT",displayName:"Shiba Inu / Tether",category:"meme",minTradeAmount:1e5,maxTradeAmount:1e8,priceDecimals:8,amountDecimals:0,description:"Dogecoin killer meme token",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"USDC_USDT",crypto1:"USDC",crypto2:"USDT",displayName:"USD Coin / Tether",category:"stablecoin",minTradeAmount:1,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:2,description:"Stablecoin arbitrage pair",supportedModes:["StablecoinSwap"]},{id:"DAI_USDT",crypto1:"DAI",crypto2:"USDT",displayName:"Dai / Tether",category:"stablecoin",minTradeAmount:1,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:2,description:"Decentralized stablecoin pair",supportedModes:["StablecoinSwap"]},{id:"BTC_ETH",crypto1:"BTC",crypto2:"ETH",displayName:"Bitcoin / Ethereum",category:"major",minTradeAmount:1e-4,maxTradeAmount:10,priceDecimals:4,amountDecimals:6,description:"Top two cryptocurrencies pair",supportedModes:["StablecoinSwap"]},{id:"ETH_BNB",crypto1:"ETH",crypto2:"BNB",displayName:"Ethereum / Binance Coin",category:"major",minTradeAmount:.001,maxTradeAmount:100,priceDecimals:4,amountDecimals:6,description:"Ethereum vs Binance ecosystem",supportedModes:["StablecoinSwap"]},{id:"XRP_USDT",crypto1:"XRP",crypto2:"USDT",displayName:"Ripple / Tether",category:"major",minTradeAmount:1,maxTradeAmount:1e5,priceDecimals:4,amountDecimals:2,description:"Cross-border payment cryptocurrency",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"LTC_USDT",crypto1:"LTC",crypto2:"USDT",displayName:"Litecoin / Tether",category:"major",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Silver to Bitcoin's gold",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"BCH_USDT",crypto1:"BCH",crypto2:"USDT",displayName:"Bitcoin Cash / Tether",category:"major",minTradeAmount:.001,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:5,description:"Bitcoin fork with larger blocks",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"TRX_USDT",crypto1:"TRX",crypto2:"USDT",displayName:"TRON / Tether",category:"altcoin",minTradeAmount:10,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:0,description:"Decentralized entertainment ecosystem",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"EOS_USDT",crypto1:"EOS",crypto2:"USDT",displayName:"EOS / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:1e4,priceDecimals:4,amountDecimals:3,description:"Delegated proof-of-stake blockchain",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"XLM_USDT",crypto1:"XLM",crypto2:"USDT",displayName:"Stellar / Tether",category:"altcoin",minTradeAmount:1,maxTradeAmount:1e5,priceDecimals:5,amountDecimals:2,description:"Fast and low-cost cross-border payments",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"VET_USDT",crypto1:"VET",crypto2:"USDT",displayName:"VeChain / Tether",category:"altcoin",minTradeAmount:10,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:0,description:"Supply chain management blockchain",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"FIL_USDT",crypto1:"FIL",crypto2:"USDT",displayName:"Filecoin / Tether",category:"altcoin",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:3,amountDecimals:4,description:"Decentralized storage network",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"THETA_USDT",crypto1:"THETA",crypto2:"USDT",displayName:"Theta Network / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:1e4,priceDecimals:4,amountDecimals:3,description:"Decentralized video streaming network",supportedModes:["SimpleSpot","StablecoinSwap"]}],ex=[{value:"major",label:"Major Cryptocurrencies",description:"Top market cap cryptocurrencies"},{value:"altcoin",label:"Altcoins",description:"Alternative cryptocurrencies"},{value:"defi",label:"DeFi Tokens",description:"Decentralized finance tokens"},{value:"meme",label:"Meme Coins",description:"Community-driven meme tokens"},{value:"stablecoin",label:"Stablecoins",description:"Price-stable cryptocurrencies"}];function eh(e){let{isOpen:a,onClose:t}=e,{config:n,dispatch:l}=(0,S.U)(),{toast:i}=(0,W.dj)(),[o,d]=(0,s.useState)(""),[m,p]=(0,s.useState)("all"),[u,x]=(0,s.useState)(null),f=(0,s.useMemo)(()=>{var e;let a=(e=n.tradingMode||"SimpleSpot",eu.filter(a=>a.supportedModes.includes(e)));return"all"!==m&&(a=a.filter(e=>e.category===m)),o.trim()&&(a=(function(e){let a=e.toLowerCase();return eu.filter(e=>e.crypto1.toLowerCase().includes(a)||e.crypto2.toLowerCase().includes(a)||e.displayName.toLowerCase().includes(a)||e.description.toLowerCase().includes(a))})(o).filter(e=>e.supportedModes.includes(n.tradingMode||"SimpleSpot"))),a},[n.tradingMode,m,o]),b=e=>{x(e)},y=e=>({major:"bg-blue-100 text-blue-800",altcoin:"bg-green-100 text-green-800",defi:"bg-purple-100 text-purple-800",meme:"bg-orange-100 text-orange-800",stablecoin:"bg-gray-100 text-gray-800"})[e]||"bg-gray-100 text-gray-800";return(0,r.jsx)(R,{open:a,onOpenChange:t,children:(0,r.jsxs)(_,{className:"sm:max-w-[700px] max-h-[80vh]",children:[(0,r.jsxs)(F,{children:[(0,r.jsxs)(V,{className:"flex items-center gap-2",children:[(0,r.jsx)(ed.A,{className:"h-5 w-5"}),"Select Trading Pair"]}),(0,r.jsxs)(z,{children:["Choose from 50+ supported trading pairs for ",n.tradingMode||"SimpleSpot"," mode."]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(w.J,{htmlFor:"search",children:"Search Pairs"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(em.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(T.p,{id:"search",placeholder:"Search by symbol or name...",value:o,onChange:e=>d(e.target.value),className:"pl-10"})]})]}),(0,r.jsxs)("div",{className:"w-48",children:[(0,r.jsx)(w.J,{htmlFor:"category",children:"Category"}),(0,r.jsxs)(k.l6,{value:m,onValueChange:p,children:[(0,r.jsx)(k.bq,{id:"category",children:(0,r.jsx)(k.yv,{})}),(0,r.jsxs)(k.gC,{children:[(0,r.jsx)(k.eb,{value:"all",children:"All Categories"}),ex.map(e=>(0,r.jsx)(k.eb,{value:e.value,children:e.label},e.value))]})]})]})]}),(0,r.jsx)(U.F,{className:"h-[400px] border rounded-lg",children:(0,r.jsx)("div",{className:"p-4 space-y-2",children:0===f.length?(0,r.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,r.jsx)(ed.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{children:"No trading pairs found matching your criteria."})]}):f.map(e=>(0,r.jsx)(g.Zp,{className:"cursor-pointer transition-all hover:shadow-md ".concat((null==u?void 0:u.id)===e.id?"ring-2 ring-primary":""),onClick:()=>b(e),children:(0,r.jsx)(g.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsxs)("h3",{className:"font-semibold text-lg",children:[e.crypto1,"/",e.crypto2]}),(0,r.jsx)(h.E,{className:y(e.category),children:e.category})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,r.jsxs)("span",{children:["Min: ",e.minTradeAmount," ",e.crypto1]}),(0,r.jsxs)("span",{children:["Max: ",e.maxTradeAmount," ",e.crypto1]}),(0,r.jsxs)("span",{children:["Decimals: ",e.priceDecimals]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:e.displayName}),(0,r.jsx)("div",{className:"flex gap-1 mt-1",children:e.supportedModes.map(e=>(0,r.jsx)(h.E,{variant:"outline",className:"text-xs",children:e},e))})]})]})})},e.id))})}),u&&(0,r.jsxs)(g.Zp,{className:"bg-muted/50",children:[(0,r.jsx)(g.aR,{children:(0,r.jsxs)(g.ZB,{className:"text-sm flex items-center gap-2",children:[(0,r.jsx)(ep.A,{className:"h-4 w-4"}),"Selected Pair Details"]})}),(0,r.jsx)(g.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Pair:"})," ",u.displayName]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Category:"})," ",u.category]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Min Trade:"})," ",u.minTradeAmount," ",u.crypto1]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Max Trade:"})," ",u.maxTradeAmount," ",u.crypto1]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Price Decimals:"})," ",u.priceDecimals]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Amount Decimals:"})," ",u.amountDecimals]})]})})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,r.jsx)(c.$,{variant:"outline",onClick:t,children:"Cancel"}),(0,r.jsx)(c.$,{onClick:()=>{if(!u){i({title:"No Pair Selected",description:"Please select a trading pair first.",variant:"destructive"});return}l({type:"SET_CONFIG",payload:{crypto1:u.crypto1,crypto2:u.crypto2}}),i({title:"Trading Pair Updated",description:"Now trading ".concat(u.displayName),duration:3e3}),t()},disabled:!u,className:"btn-neo",children:"Apply Trading Pair"})]})]})]})})}var eg=t(17569),ef=t(50172),eb=t(8803),ey=t(30955),ev=t(8531);let ej=["USDT","USDC","BTC"],eS=["USDC","DAI","TUSD","FDUSD","USDT","EUR"];function eN(){let e,a;try{e=(0,S.U)()}catch(e){return console.error("Trading context not available:",e),(0,r.jsx)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("p",{className:"text-red-500",children:"Trading context not available. Please refresh the page."})})})}let{config:t,dispatch:n,botSystemStatus:l,appSettings:i,setTargetPrices:o}=e,d="Running"===l,m="WarmingUp"===l;try{a=(0,N.f)()}catch(e){console.warn("AI context not available:",e),a={suggestion:null,isLoading:!1,error:null,getTradingModeSuggestion:()=>Promise.resolve()}}let{suggestion:p,isLoading:u,error:x,getTradingModeSuggestion:h}=a,{toast:f}=(0,W.dj)(),[b,y]=(0,s.useState)(!1),[v,j]=(0,s.useState)(!1),[A,C]=(0,s.useState)(!1),[O,P]=(0,s.useState)(!1),[R,L]=(0,s.useState)(!1),[B,I]=(0,s.useState)("medium"),[_,F]=(0,s.useState)(""),[G,V]=(0,s.useState)(""),z=e=>{let a;let{name:t,value:r,type:s,checked:l}=e.target;if("checkbox"===s)a=l;else if("number"===s){if(""===r||null==r)a=0;else{let e=parseFloat(r);a=isNaN(e)?0:e}}else a=r;n({type:"SET_CONFIG",payload:{[t]:a}})},H=(e,a)=>{if(n({type:"SET_CONFIG",payload:{[e]:a}}),"crypto1"===e){let e=$.vA[a]||ej||["USDT","USDC","BTC"];t.crypto2&&Array.isArray(e)&&e.includes(t.crypto2)||n({type:"SET_CONFIG",payload:{crypto2:e[0]||"USDT"}})}},J=(e,a)=>{let t=parseFloat(a);isNaN(t)&&(t=0),t<0&&(t=0),t>100&&(t=100),"incomeSplitCrypto1Percent"===e?n({type:"SET_CONFIG",payload:{incomeSplitCrypto1Percent:t,incomeSplitCrypto2Percent:100-t}}):n({type:"SET_CONFIG",payload:{incomeSplitCrypto2Percent:t,incomeSplitCrypto1Percent:100-t}})},X=async()=>{if(!B||!_||!G){f({title:"AI Suggestion Error",description:"Please fill all AI suggestion fields.",variant:"destructive"});return}await h({riskTolerance:B,preferredCryptocurrencies:_,investmentGoals:G})};(0,s.useEffect)(()=>{p&&f({title:"AI Suggestion Received",description:(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Mode:"})," ",p.suggestedMode]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Reason:"})," ",p.reason]}),(0,r.jsx)(c.$,{size:"sm",className:"mt-2 btn-neo",onClick:()=>n({type:"SET_CONFIG",payload:{tradingMode:"Simple Spot"===p.suggestedMode?"SimpleSpot":"StablecoinSwap"}}),children:"Apply Suggestion"})]}),duration:1/0}),x&&f({title:"AI Suggestion Error",description:x,variant:"destructive",duration:1/0})},[p,x,f,n]);let q=$.hg||[];return"SimpleSpot"===t.tradingMode?$.vA[t.crypto1]:($.hg||[]).filter(e=>e!==t.crypto1),console.log("\uD83D\uDD0D DEBUG: AVAILABLE_CRYPTOS length:",null===$.hg||void 0===$.hg?void 0:$.hg.length),console.log("\uD83D\uDD0D DEBUG: crypto1Options length:",q.length),console.log("\uD83D\uDD0D DEBUG: First 20 cryptos:",null===$.hg||void 0===$.hg?void 0:$.hg.slice(0,20)),console.log("\uD83D\uDD0D DEBUG: ALLOWED_CRYPTO1:",$.ALLOWED_CRYPTO1),console.log("\uD83D\uDD0D DEBUG: ALLOWED_CRYPTO2:",$.ALLOWED_CRYPTO2),console.log("\uD83D\uDD0D DEBUG: AVAILABLE_STABLECOINS:",$.Ql),(0,r.jsxs)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-sidebar-primary",children:"Trading Configuration"}),(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>L(!R),className:"text-sidebar-accent-foreground hover:bg-sidebar-accent",title:R?"Hide AI Suggestions":"Show AI Suggestions",children:(0,r.jsx)(eg.A,{className:"h-4 w-4 ".concat(R?"text-primary":"text-muted-foreground")})})]}),(0,r.jsx)(U.F,{className:"flex-1 pr-2",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(g.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,r.jsx)(g.aR,{children:(0,r.jsx)(g.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Mode"})}),(0,r.jsxs)(g.Wu,{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(E,{id:"enableStablecoinSwap",checked:"StablecoinSwap"===t.tradingMode,onCheckedChange:e=>{let a;let r=e?"StablecoinSwap":"SimpleSpot";a="StablecoinSwap"===r?($.ALLOWED_CRYPTO1||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(e=>e!==t.crypto1)[0]:($.ALLOWED_CRYPTO2||["USDC","DAI","TUSD","FDUSD","USDT","EUR"])[0],n({type:"SET_CONFIG",payload:{tradingMode:r,crypto2:a}})}}),(0,r.jsx)(w.J,{htmlFor:"enableStablecoinSwap",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Enable Stablecoin Swap Mode"})]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Current mode: ","SimpleSpot"===t.tradingMode?"Simple Spot":"Stablecoin Swap"]}),"StablecoinSwap"===t.tradingMode&&(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{htmlFor:"preferredStablecoin",children:"Preferred Stablecoin"}),(0,r.jsxs)(k.l6,{name:"preferredStablecoin",value:t.preferredStablecoin,onValueChange:e=>H("preferredStablecoin",e),children:[(0,r.jsx)(k.bq,{id:"preferredStablecoin",children:(0,r.jsx)(k.yv,{placeholder:"Select stablecoin"})}),(0,r.jsx)(k.gC,{className:"max-h-[300px] overflow-y-auto",children:eS.map(e=>(0,r.jsx)(k.eb,{value:e,children:e},e))})]})]})]})]}),R&&(0,r.jsxs)(g.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,r.jsx)(g.aR,{children:(0,r.jsxs)(g.ZB,{className:"text-sidebar-accent-foreground flex items-center",children:[(0,r.jsx)(eg.A,{className:"mr-2 h-5 w-5 text-primary"})," AI Mode Suggestion"]})}),(0,r.jsxs)(g.Wu,{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{htmlFor:"riskTolerance",children:"Risk Tolerance"}),(0,r.jsxs)(k.l6,{value:B,onValueChange:I,children:[(0,r.jsx)(k.bq,{id:"riskTolerance",children:(0,r.jsx)(k.yv,{placeholder:"Select risk tolerance"})}),(0,r.jsxs)(k.gC,{children:[(0,r.jsx)(k.eb,{value:"low",children:"Low"}),(0,r.jsx)(k.eb,{value:"medium",children:"Medium"}),(0,r.jsx)(k.eb,{value:"high",children:"High"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{htmlFor:"preferredCryptos",children:"Preferred Cryptocurrencies (comma-separated)"}),(0,r.jsx)(T.p,{id:"preferredCryptos",value:_,onChange:e=>F(e.target.value),placeholder:"e.g., BTC, ETH"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{htmlFor:"investmentGoals",children:"Investment Goals"}),(0,r.jsx)(T.p,{id:"investmentGoals",value:G,onChange:e=>V(e.target.value),placeholder:"e.g., Long term, Short term profit"})]}),(0,r.jsxs)(c.$,{onClick:X,disabled:u,className:"w-full btn-neo",children:[u&&(0,r.jsx)(ef.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Get AI Suggestion"]})]})]}),(0,r.jsxs)(g.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,r.jsx)(g.aR,{children:(0,r.jsx)(g.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Pair"})}),(0,r.jsxs)(g.Wu,{className:"space-y-4",children:[(0,r.jsx)(Y,{label:"Crypto 1 (Base)",value:t.crypto1,allowedCryptos:$.ALLOWED_CRYPTO1||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"],onValidCrypto:e=>{if(n({type:"SET_CONFIG",payload:{crypto1:e}}),"StablecoinSwap"===t.tradingMode){let a=($.ALLOWED_CRYPTO1||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(a=>a!==e);a.includes(t.crypto2)&&t.crypto2!==e||n({type:"SET_CONFIG",payload:{crypto2:a[0]}})}else{let e=$.ALLOWED_CRYPTO2||["USDC","DAI","TUSD","FDUSD","USDT","EUR"];e.includes(t.crypto2)||n({type:"SET_CONFIG",payload:{crypto2:e[0]}})}},placeholder:"e.g., BTC, ETH, SOL",description:"Enter the base cryptocurrency symbol"}),(0,r.jsx)(Y,{label:"StablecoinSwap"===t.tradingMode?"Crypto 2":"Crypto 2 (Stablecoin)",value:t.crypto2,allowedCryptos:"StablecoinSwap"===t.tradingMode?($.ALLOWED_CRYPTO1||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(e=>e!==t.crypto1):$.ALLOWED_CRYPTO2||["USDC","DAI","TUSD","FDUSD","USDT","EUR"],onValidCrypto:e=>{("StablecoinSwap"!==t.tradingMode||e!==t.crypto1)&&n({type:"SET_CONFIG",payload:{crypto2:e}})},placeholder:"StablecoinSwap"===t.tradingMode?"e.g., BTC, ETH, SOL":"e.g., USDT, USDC, DAI",description:"StablecoinSwap"===t.tradingMode?"Enter the second cryptocurrency symbol":"Enter the quote/stablecoin symbol"})]})]}),(0,r.jsxs)(g.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,r.jsx)(g.aR,{children:(0,r.jsx)(g.ZB,{className:"text-sidebar-accent-foreground",children:"Parameters"})}),(0,r.jsxs)(g.Wu,{className:"space-y-3",children:[[{name:"baseBid",label:"Base Bid (Crypto 2)",type:"number",step:"0.01"},{name:"multiplier",label:"Multiplier",type:"number",step:"0.001"},{name:"numDigits",label:"Display Digits",type:"number",step:"1"},{name:"slippagePercent",label:"Slippage %",type:"number",step:"0.01"}].map(e=>(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{htmlFor:e.name,children:e.label}),(0,r.jsx)(T.p,{id:e.name,name:e.name,type:e.type,value:t[e.name],onChange:z,step:e.step,min:"0"})]},e.name)),(0,r.jsxs)("div",{children:[(0,r.jsx)(w.J,{children:"Couple Income % Split (must sum to 100)"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(w.J,{htmlFor:"incomeSplitCrypto1Percent",className:"text-xs",children:[t.crypto1||"Crypto 1","%"]}),(0,r.jsx)(T.p,{id:"incomeSplitCrypto1Percent",type:"number",value:t.incomeSplitCrypto1Percent,onChange:e=>J("incomeSplitCrypto1Percent",e.target.value),min:"0",max:"100"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)(w.J,{htmlFor:"incomeSplitCrypto2Percent",className:"text-xs",children:[t.crypto2||"Crypto 2","%"]}),(0,r.jsx)(T.p,{id:"incomeSplitCrypto2Percent",type:"number",value:t.incomeSplitCrypto2Percent,onChange:e=>J("incomeSplitCrypto2Percent",e.target.value),min:"0",max:"100"})]})]})]})]})]})]})}),(0,r.jsxs)("div",{className:"flex-shrink-0 mt-4",children:[(0,r.jsx)(M.w,{className:"mb-4 bg-sidebar-border"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:(0,D.cn)("text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border",d?"bg-green-600 text-primary-foreground":"bg-muted text-muted-foreground"),children:[d?(0,r.jsx)(eb.A,{className:"h-4 w-4"}):m?(0,r.jsx)(ef.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,r.jsx)(ey.A,{className:"h-4 w-4"}),"Bot Status: ",d?"Running":m?"Warming Up":"Stopped"]}),(0,r.jsx)(c.$,{onClick:()=>y(!0),className:"w-full btn-outline-neo",children:"Set Target Prices"}),(0,r.jsx)(c.$,{onClick:()=>j(!0),className:"w-full btn-outline-neo",children:"Session Alarms"}),(0,r.jsx)(c.$,{onClick:()=>C(!0),className:"w-full btn-outline-neo",children:"Telegram Settings"}),(0,r.jsx)(c.$,{onClick:()=>P(!0),className:"w-full btn-outline-neo",children:"Select Trading Pair"}),(0,r.jsxs)(c.$,{onClick:()=>{d?n({type:"SYSTEM_STOP_BOT"}):n({type:"SYSTEM_START_BOT_INITIATE"})},className:(0,D.cn)("w-full btn-neo",d||m?"bg-destructive hover:bg-destructive/90":"bg-green-600 hover:bg-green-600/90"),disabled:m,children:[d?(0,r.jsx)(eb.A,{className:"h-4 w-4"}):m?(0,r.jsx)(ef.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,r.jsx)(ey.A,{className:"h-4 w-4"}),d?"Stop Bot":m?"Warming Up...":"Start Bot"]}),(0,r.jsxs)(c.$,{onClick:()=>{n({type:"SYSTEM_RESET_BOT"}),f({title:"Bot Reset",description:"Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.",duration:4e3})},variant:"outline",className:"w-full btn-outline-neo",disabled:m,children:[(0,r.jsx)(ev.A,{className:"h-4 w-4 mr-2"}),"Reset Bot"]})]})]}),(0,r.jsx)(Z,{isOpen:b,onClose:()=>y(!1),onSetTargetPrices:o}),(0,r.jsx)(er,{isOpen:v,onClose:()=>j(!1)}),(0,r.jsx)(ec,{isOpen:A,onClose:()=>C(!1)}),(0,r.jsx)(eh,{isOpen:O,onClose:()=>P(!1)})]})}function eT(e){let{children:a}=e,{isAuthenticated:t,isLoading:s}=(0,d.A)(),n=(0,i.useRouter)();return s?(0,r.jsx)("div",{className:"flex items-center justify-center h-screen bg-background",children:(0,r.jsx)(ef.A,{className:"h-12 w-12 animate-spin text-primary"})}):t?(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-background text-foreground",children:[(0,r.jsx)(j,{}),(0,r.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,r.jsx)(eN,{}),(0,r.jsx)("main",{className:"flex-1 overflow-y-auto p-4 md:p-6 lg:p-8",children:a})]})]}):(n.push("/login"),null)}},30285:(e,a,t)=>{"use strict";t.d(a,{$:()=>c});var r=t(95155),s=t(12115),n=t(99708),l=t(74466),i=t(59434);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,a)=>{let{className:t,variant:s,size:l,asChild:c=!1,...d}=e,m=c?n.DX:"button";return(0,r.jsx)(m,{className:(0,i.cn)(o({variant:s,size:l,className:t})),ref:a,...d})});c.displayName="Button"},35286:(e,a,t)=>{"use strict";t.d(a,{AIProvider:()=>o,f:()=>c});var r=t(95155),s=t(12115),n=t(34477);let l=(0,n.createServerReference)("405f4d7808150aa660558d7cb3c1e71740bf45cf90",n.callServer,void 0,n.findSourceMapURL,"suggestTradingMode"),i=(0,s.createContext)(void 0),o=e=>{let{children:a}=e,[t,n]=(0,s.useState)(null),[o,c]=(0,s.useState)(!1),[d,m]=(0,s.useState)(null),p=async e=>{c(!0),m(null),n(null);try{let a=await l(e);n(a)}catch(e){m(e instanceof Error?e.message:"An unknown error occurred during AI suggestion."),console.error("Error fetching trading mode suggestion:",e)}finally{c(!1)}};return(0,r.jsx)(i.Provider,{value:{suggestion:t,isLoading:o,error:d,getTradingModeSuggestion:p},children:a})},c=()=>{let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAIContext must be used within an AIProvider");return e}},40283:(e,a,t)=>{"use strict";t.d(a,{A:()=>d,AuthProvider:()=>c});var r=t(95155),s=t(12115),n=t(35695),l=t(50172),i=t(25731);let o=(0,s.createContext)(void 0),c=e=>{let{children:a}=e,[t,c]=(0,s.useState)(!1),[d,m]=(0,s.useState)(!0),p=(0,n.useRouter)(),u=(0,n.usePathname)();(0,s.useEffect)(()=>{let e=localStorage.getItem("plutoAuth"),a=localStorage.getItem("plutoAuthToken");"true"===e&&a&&c(!0),m(!1)},[]),(0,s.useEffect)(()=>{d||t||"/login"===u?!d&&t&&"/login"===u&&p.push("/dashboard"):p.push("/login")},[t,d,u,p]);let x=async(e,a)=>{m(!0);try{if(await i.ZQ.login(e,a))return c(!0),p.push("/dashboard"),!0;return c(!1),!1}catch(e){return console.error("Login failed:",e),c(!1),!1}finally{m(!1)}},h=async()=>{try{await i.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{c(!1),p.push("/login")}};return!d||(null==u?void 0:u.startsWith("/_next/static/"))?t||"/login"===u||(null==u?void 0:u.startsWith("/_next/static/"))?(0,r.jsx)(o.Provider,{value:{isAuthenticated:t,login:x,logout:h,isLoading:d},children:a}):(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,r.jsx)(l.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,r.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting to login..."})]}):(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,r.jsx)(l.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,r.jsx)("p",{className:"ml-4 text-xl",children:"Loading Pluto..."})]})},d=()=>{let e=(0,s.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},59409:(e,a,t)=>{"use strict";t.d(a,{bq:()=>p,eb:()=>g,gC:()=>h,l6:()=>d,yv:()=>m});var r=t(95155),s=t(12115),n=t(50663),l=t(79556),i=t(77381),o=t(10518),c=t(59434);let d=n.bL;n.YJ;let m=n.WT,p=s.forwardRef((e,a)=>{let{className:t,children:s,...i}=e;return(0,r.jsxs)(n.l9,{ref:a,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...i,children:[s,(0,r.jsx)(n.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]})});p.displayName=n.l9.displayName;let u=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(n.PP,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})});u.displayName=n.PP.displayName;let x=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(n.wn,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})});x.displayName=n.wn.displayName;let h=s.forwardRef((e,a)=>{let{className:t,children:s,position:l="popper",...i}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{ref:a,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...i,children:[(0,r.jsx)(u,{}),(0,r.jsx)(n.LM,{className:(0,c.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(x,{})]})})});h.displayName=n.UC.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(n.JU,{ref:a,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...s})}).displayName=n.JU.displayName;let g=s.forwardRef((e,a)=>{let{className:t,children:s,...l}=e;return(0,r.jsxs)(n.q7,{ref:a,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(n.p4,{children:s})]})});g.displayName=n.q7.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(n.wv,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...s})}).displayName=n.wv.displayName},59435:(e,a,t)=>{"use strict";t.d(a,{A:()=>l});var r=t(95155),s=t(12115),n=t(49502);let l=e=>{let{className:a,useFullName:t=!0}=e,[l,i]=(0,s.useState)(!1);return(0,r.jsxs)("div",{className:"flex items-center text-2xl font-bold text-primary ".concat(a),children:[l?(0,r.jsx)(n.A,{className:"mr-2 h-7 w-7"}):(0,r.jsx)("img",{src:"https://i.imgur.com/Q0HDcMH.png",alt:"Pluto Trading Bot Logo",className:"mr-2 h-7 w-7 rounded-full object-cover",onError:()=>{i(!0)},onLoad:()=>console.log("Pluto logo loaded successfully")}),(0,r.jsxs)("span",{children:["Pluto",t?" Trading Bot":""]})]})}},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>l});var r=t(95155),s=t(12115),n=t(59434);let l=s.forwardRef((e,a)=>{let{className:t,type:s,...l}=e;return(0,r.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...l})});l.displayName="Input"},66424:(e,a,t)=>{"use strict";t.d(a,{$:()=>o,F:()=>i});var r=t(95155),s=t(12115),n=t(47655),l=t(59434);let i=s.forwardRef((e,a)=>{let{className:t,children:s,...i}=e;return(0,r.jsxs)(n.bL,{ref:a,className:(0,l.cn)("relative overflow-hidden",t),...i,children:[(0,r.jsx)(n.LM,{className:"h-full w-full rounded-[inherit]",children:s}),(0,r.jsx)(o,{}),(0,r.jsx)(n.OK,{})]})});i.displayName=n.bL.displayName;let o=s.forwardRef((e,a)=>{let{className:t,orientation:s="vertical",...i}=e;return(0,r.jsx)(n.VM,{ref:a,orientation:s,className:(0,l.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...i,children:(0,r.jsx)(n.lr,{className:"relative flex-1 rounded-full bg-border"})})});o.displayName=n.VM.displayName},66695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>l,aR:()=>i});var r=t(95155),s=t(12115),n=t(59434);let l=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",t),...s})});l.displayName="Card";let i=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.cn)("flex flex-col space-y-1.5 p-4 md:p-6",t),...s})});i.displayName="CardHeader";let o=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("h3",{ref:a,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",t),...s})});o.displayName="CardTitle";let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("p",{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",t),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.cn)("p-4 md:p-6 pt-0",t),...s})});d.displayName="CardContent",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.cn)("flex items-center p-4 md:p-6 pt-0",t),...s})}).displayName="CardFooter"},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>c});var r=t(95155),s=t(12115),n=t(40968),l=t(74466),i=t(59434);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(n.b,{ref:a,className:(0,i.cn)(o(),t),...s})});c.displayName=n.b.displayName}},e=>{var a=a=>e(e.s=a);e.O(0,[823,98,842,377,655,631,847,318,441,684,358],()=>a(7926)),_N_E=e.O()}]);