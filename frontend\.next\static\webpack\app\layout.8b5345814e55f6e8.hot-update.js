"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // For StablecoinSwap mode, we need to calculate the price differently\n        if (config.tradingMode === \"StablecoinSwap\") {\n            // Get both crypto prices in terms of the preferred stablecoin\n            const stablecoin = config.preferredStablecoin || 'USDT';\n            try {\n                // Fetch real-time prices from CoinGecko API\n                const crypto1Id = getCoinGeckoId(config.crypto1);\n                const crypto2Id = getCoinGeckoId(config.crypto2);\n                const stablecoinId = getCoinGeckoId(stablecoin);\n                if (crypto1Id && crypto2Id && stablecoinId) {\n                    const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \",\").concat(crypto2Id, \"&vs_currencies=\").concat(stablecoinId));\n                    if (response.ok) {\n                        var _data_crypto1Id, _data_crypto2Id;\n                        const data = await response.json();\n                        const crypto1Price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[stablecoinId];\n                        const crypto2Price = (_data_crypto2Id = data[crypto2Id]) === null || _data_crypto2Id === void 0 ? void 0 : _data_crypto2Id[stablecoinId];\n                        if (crypto1Price > 0 && crypto2Price > 0) {\n                            const ratio = crypto1Price / crypto2Price;\n                            console.log(\"\\uD83D\\uDCCA StablecoinSwap price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(ratio, \" (\").concat(config.crypto1, \": \").concat(crypto1Price, \" \").concat(stablecoin, \", \").concat(config.crypto2, \": \").concat(crypto2Price, \" \").concat(stablecoin, \")\"));\n                            return ratio;\n                        }\n                    }\n                }\n                // Fallback to the existing stablecoin exchange rate method\n                const crypto1Price = await getStablecoinExchangeRate(config.crypto1, stablecoin);\n                const crypto2Price = await getStablecoinExchangeRate(config.crypto2, stablecoin);\n                // Calculate crypto1/crypto2 ratio\n                const ratio = crypto1Price / crypto2Price;\n                console.log(\"\\uD83D\\uDCCA StablecoinSwap price (fallback): \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(ratio, \" (via \").concat(stablecoin, \")\"));\n                return ratio;\n            } catch (error) {\n                console.error('Error fetching StablecoinSwap prices:', error);\n                // Final fallback to mock prices\n                const crypto1Price = getUSDPrice(config.crypto1);\n                const crypto2Price = getUSDPrice(config.crypto2);\n                return crypto1Price / crypto2Price;\n            }\n        }\n        // For SimpleSpot mode, use direct pair pricing\n        // Try multiple API endpoints for better coverage\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // First try Binance API\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Price fetched from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed, trying alternative...', binanceError);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id));\n                if (response.ok) {\n                    var _data_crypto1Id1;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id1 = data[crypto1Id]) === null || _data_crypto1Id1 === void 0 ? void 0 : _data_crypto1Id1[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ Price fetched from CoinGecko: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using mock price...', geckoError);\n        }\n        // Final fallback to mock price\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using mock price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Helper function to get USD price (extracted from calculateFallbackMarketPrice)\nconst getUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies\n        'BTC': 109000,\n        'ETH': 4000,\n        'SOL': 240,\n        'ADA': 1.2,\n        'DOGE': 0.4,\n        'LINK': 25,\n        'MATIC': 0.5,\n        'DOT': 8,\n        'AVAX': 45,\n        'SHIB': 0.000030,\n        'XRP': 2.5,\n        'LTC': 110,\n        'BCH': 500,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    console.log(\"\\uD83D\\uDCCA Fallback price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(finalPrice.toFixed(6)));\n    return finalPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\n// Load global balances for initial state\nconst globalBalances = loadGlobalBalanceFromStorage();\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: globalBalances.crypto1Balance,\n    crypto2Balance: globalBalances.crypto2Balance,\n    stablecoinBalance: globalBalances.stablecoinBalance,\n    backendStatus: 'unknown',\n    totalProfitLoss: globalBalances.totalProfitLoss,\n    sessionAlarmConfig: {\n        buyAlarmEnabled: true,\n        sellAlarmEnabled: true,\n        buyAlarmSound: 'default.mp3',\n        sellAlarmSound: 'default.mp3',\n        volume: 50\n    },\n    telegramConfig: {\n        enabled: false,\n        botToken: '',\n        chatId: ''\n    },\n    isTrading: false\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst SESSION_STORAGE_KEY = 'tradingSession';\nconst BALANCE_STORAGE_KEY = 'tradingBalances';\n// Session persistence functions\nconst saveSessionToStorage = (state)=>{\n    try {\n        const sessionData = {\n            config: state.config,\n            targetPriceRows: state.targetPriceRows,\n            orderHistory: state.orderHistory,\n            currentMarketPrice: state.currentMarketPrice,\n            crypto1Balance: state.crypto1Balance,\n            crypto2Balance: state.crypto2Balance,\n            stablecoinBalance: state.stablecoinBalance,\n            totalProfitLoss: state.totalProfitLoss,\n            sessionAlarmConfig: state.sessionAlarmConfig,\n            telegramConfig: state.telegramConfig,\n            isTrading: state.isTrading,\n            botSystemStatus: state.botSystemStatus,\n            lastUpdated: new Date().toISOString()\n        };\n        localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(sessionData));\n    } catch (error) {\n        console.error('Failed to save session to storage:', error);\n    }\n};\n// Global balance persistence functions - tracks cumulative spending across all sessions\nconst GLOBAL_BALANCE_STORAGE_KEY = 'plutoTradingBot_globalBalances';\nconst saveGlobalBalanceToStorage = (state)=>{\n    try {\n        const balanceData = {\n            crypto1Balance: state.crypto1Balance,\n            crypto2Balance: state.crypto2Balance,\n            stablecoinBalance: state.stablecoinBalance,\n            totalProfitLoss: state.totalProfitLoss,\n            lastUpdated: new Date().toISOString()\n        };\n        localStorage.setItem(GLOBAL_BALANCE_STORAGE_KEY, JSON.stringify(balanceData));\n        console.log('💾 Global balances saved:', balanceData);\n    } catch (error) {\n        console.error('Failed to save global balance to storage:', error);\n    }\n};\nconst loadGlobalBalanceFromStorage = ()=>{\n    try {\n        const savedBalance = localStorage.getItem(GLOBAL_BALANCE_STORAGE_KEY);\n        if (savedBalance) {\n            const balanceData = JSON.parse(savedBalance);\n            console.log('📥 Global balances loaded:', balanceData);\n            return {\n                crypto1Balance: balanceData.crypto1Balance || 10,\n                crypto2Balance: balanceData.crypto2Balance || 100000,\n                stablecoinBalance: balanceData.stablecoinBalance || 0,\n                totalProfitLoss: balanceData.totalProfitLoss || 0\n            };\n        }\n    } catch (error) {\n        console.error('Failed to load global balance from storage:', error);\n    }\n    // Return default balances if no saved data or error\n    return {\n        crypto1Balance: 10,\n        crypto2Balance: 100000,\n        stablecoinBalance: 0,\n        totalProfitLoss: 0\n    };\n};\n// Legacy balance persistence for session-specific data\nconst saveBalanceToStorage = (state)=>{\n    try {\n        const balanceData = {\n            crypto1Balance: state.crypto1Balance,\n            crypto2Balance: state.crypto2Balance,\n            stablecoinBalance: state.stablecoinBalance,\n            totalProfitLoss: state.totalProfitLoss,\n            lastUpdated: new Date().toISOString()\n        };\n        localStorage.setItem(BALANCE_STORAGE_KEY, JSON.stringify(balanceData));\n    } catch (error) {\n        console.error('Failed to save balance to storage:', error);\n    }\n};\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    // Enforce manual bot control - never auto-start on state restoration\n                    return {\n                        ...parsed,\n                        isTrading: false,\n                        botSystemStatus: 'Stopped'\n                    };\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            const updatedState = {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n            // Save to global storage whenever balances change\n            saveGlobalBalanceToStorage(updatedState);\n            return updatedState;\n        case 'UPDATE_STABLECOIN_BALANCE':\n            const updatedStablecoinState = {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n            // Save to global storage whenever stablecoin balance changes\n            saveGlobalBalanceToStorage(updatedStablecoinState);\n            return updatedStablecoinState;\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            // Preserve global balances when resetting session\n            const currentGlobalBalances = loadGlobalBalanceFromStorage();\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset),\n                // Preserve current global balances instead of resetting to defaults\n                crypto1Balance: currentGlobalBalances.crypto1Balance,\n                crypto2Balance: currentGlobalBalances.crypto2Balance,\n                stablecoinBalance: currentGlobalBalances.stablecoinBalance,\n                totalProfitLoss: currentGlobalBalances.totalProfitLoss\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            // Preserve global balances when switching crypto pairs\n            const newCryptoGlobalBalances = loadGlobalBalanceFromStorage();\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0,\n                // Preserve current global balances instead of resetting to defaults\n                crypto1Balance: newCryptoGlobalBalances.crypto1Balance,\n                crypto2Balance: newCryptoGlobalBalances.crypto2Balance,\n                stablecoinBalance: newCryptoGlobalBalances.stablecoinBalance,\n                totalProfitLoss: newCryptoGlobalBalances.totalProfitLoss\n            };\n        case 'RESTORE_SESSION':\n            return {\n                ...state,\n                ...action.payload,\n                isTrading: false,\n                botSystemStatus: 'Stopped' // Prevent auto-start on session restoration\n            };\n        case 'SET_TELEGRAM_CONFIG':\n            return {\n                ...state,\n                telegramConfig: {\n                    ...state.telegramConfig,\n                    ...action.payload\n                }\n            };\n        case 'SET_SESSION_ALARM_CONFIG':\n            return {\n                ...state,\n                sessionAlarmConfig: {\n                    ...state.sessionAlarmConfig,\n                    ...action.payload\n                }\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Telegram notification function\nconst sendTelegramNotification = async (message, type, telegramConfig)=>{\n    try {\n        if (!telegramConfig.enabled || !telegramConfig.botToken || !telegramConfig.chatId) {\n            return;\n        }\n        const emoji = type === 'error' ? '🚨' : type === 'success' ? '✅' : 'ℹ️';\n        const formattedMessage = \"\".concat(emoji, \" \").concat(message);\n        await fetch(\"https://api.telegram.org/bot\".concat(telegramConfig.botToken, \"/sendMessage\"), {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                chat_id: telegramConfig.chatId,\n                text: formattedMessage,\n                parse_mode: 'HTML'\n            })\n        });\n    } catch (error) {\n        console.error('Failed to send Telegram notification:', error);\n    }\n};\n// Session-specific alarm function\nconst playSessionAlarm = (type, alarmConfig)=>{\n    try {\n        const soundFile = type === 'buy' ? alarmConfig.buyAlarmSound : alarmConfig.sellAlarmSound;\n        const enabled = type === 'buy' ? alarmConfig.buyAlarmEnabled : alarmConfig.sellAlarmEnabled;\n        if (soundFile && enabled) {\n            const audio = new Audio(\"/ringtones/\".concat(soundFile));\n            audio.volume = alarmConfig.volume / 100;\n            audio.play().catch(console.error);\n        }\n    } catch (error) {\n        console.error('Failed to play session alarm:', error);\n    }\n};\n// Enhanced price fetching for StablecoinSwap mode\nconst fetchStablecoinSwapPrice = async (crypto1, crypto2)=>{\n    try {\n        var _data_crypto1_toLowerCase;\n        const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1.toLowerCase(), \"&vs_currencies=\").concat(crypto2.toLowerCase()));\n        const data = await response.json();\n        const price = (_data_crypto1_toLowerCase = data[crypto1.toLowerCase()]) === null || _data_crypto1_toLowerCase === void 0 ? void 0 : _data_crypto1_toLowerCase[crypto2.toLowerCase()];\n        if (price) {\n            return price;\n        }\n        throw new Error('Price not found');\n    } catch (error) {\n        console.error('Error fetching stablecoin swap price:', error);\n        return null;\n    }\n};\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Clear the URL parameter to avoid confusion\n                const newUrl = window.location.pathname;\n                window.history.replaceState({}, '', newUrl);\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load saved state\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                const price = await getMarketPriceFromAPI(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Market price fluctuation effect - simulates real-time price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up price fluctuation interval for simulation\n            const priceFluctuationInterval = setInterval({\n                \"TradingProvider.useEffect.priceFluctuationInterval\": ()=>{\n                    // Only fluctuate price if online\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        dispatch({\n                            type: 'FLUCTUATE_MARKET_PRICE'\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceFluctuationInterval\"], 2000); // Update every 2 seconds for realistic simulation\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceFluctuationInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch\n    ]);\n    // Other effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            if (state.appSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && state.appSettings.alertOnOrderExecution) {\n                    soundPath = state.appSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && state.appSettings.alertOnError) {\n                    soundPath = state.appSettings.soundError;\n                }\n                if (soundPath) {\n                    audioRef.current.src = soundPath;\n                    audioRef.current.currentTime = 0; // Reset to beginning\n                    // Play the sound and limit duration to 2 seconds\n                    audioRef.current.play().then({\n                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                            // Set a timeout to pause the audio after 2 seconds\n                            setTimeout({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0; // Reset for next play\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                        }\n                    }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                        \"TradingProvider.useCallback[playSound]\": (err)=>console.error(\"Error playing sound:\", err)\n                    }[\"TradingProvider.useCallback[playSound]\"]);\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Telegram notification function\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async (message)=>{\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || '',\n                                        tradingMode: 'SimpleSpot',\n                                        type: 'BUY',\n                                        costBasisCrypto2: costCrypto2\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                    tradingMode: 'SimpleSpot',\n                                    type: 'SELL',\n                                    costBasisCrypto2: inferiorRow.originalCostCrypto2,\n                                    percentageGain: inferiorRow.originalCostCrypto2 > 0 ? (crypto2Received - inferiorRow.originalCostCrypto2) / inferiorRow.originalCostCrypto2 * 100 : 0\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Get real market price for Crypto2/Stablecoin pair (synchronous for now)\n                                const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Get real market price for Crypto1/Stablecoin pair\n                                const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        tradingMode: 'StablecoinSwap',\n                                        type: 'BUY',\n                                        intermediateStablecoinAmount: stablecoinObtained,\n                                        stablecoinPrice: crypto2StablecoinPrice\n                                    }\n                                });\n                                // Calculate potential profit information for BUY entry (for future reference)\n                                const currentMarketValueInCrypto2 = crypto1Bought * (currentMarketPrice || crypto1StablecoinPrice);\n                                const potentialProfitInCrypto2 = currentMarketValueInCrypto2 - amountCrypto2ToUse;\n                                const potentialProfitCrypto1 = crypto1StablecoinPrice > 0 ? potentialProfitInCrypto2 * config.incomeSplitCrypto1Percent / 100 / crypto1StablecoinPrice : 0;\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        tradingMode: 'StablecoinSwap',\n                                        type: 'BUY',\n                                        costBasisCrypto2: amountCrypto2ToUse,\n                                        intermediateStablecoinAmount: stablecoinObtained,\n                                        stablecoinPrice: crypto1StablecoinPrice,\n                                        // Store potential profit information for reference\n                                        potentialProfitLossCrypto2: potentialProfitInCrypto2,\n                                        potentialProfitLossCrypto1: potentialProfitCrypto1,\n                                        currentMarketValueCrypto2: currentMarketValueInCrypto2\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Enhanced StablecoinSwap P/L calculation with proper two-step process\n                            const costBasisInCrypto2 = inferiorRow.originalCostCrypto2 || 0;\n                            // Two-step SELL formula:\n                            // Step A: Crypto1 → Stablecoin (already calculated as stablecoinFromC1Sell)\n                            // Step B: Stablecoin → Crypto2 (already calculated as crypto2Reacquired)\n                            // Profit = Final Crypto2 Amount - Original Crypto2 Cost\n                            const realizedProfitInCrypto2 = crypto2Reacquired - costBasisInCrypto2;\n                            // Calculate percentage gain/loss based on original cost basis\n                            const percentageGain = costBasisInCrypto2 > 0 ? (crypto2Reacquired - costBasisInCrypto2) / costBasisInCrypto2 * 100 : 0;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            // This represents the portion of profit that would be allocated to Crypto1\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 * config.incomeSplitCrypto1Percent / 100 / crypto1StablecoinPrice : 0;\n                            // Track intermediate values for better P/L analysis\n                            const intermediateStablecoinAmount = stablecoinFromC1Sell;\n                            const effectiveStablecoinPrice = crypto2StablecoinPrice;\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    tradingMode: 'StablecoinSwap',\n                                    type: 'SELL',\n                                    intermediateStablecoinAmount: intermediateStablecoinAmount,\n                                    stablecoinPrice: crypto1StablecoinPrice\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                    tradingMode: 'StablecoinSwap',\n                                    type: 'SELL',\n                                    costBasisCrypto2: costBasisInCrypto2,\n                                    percentageGain: percentageGain,\n                                    intermediateStablecoinAmount: intermediateStablecoinAmount,\n                                    stablecoinPrice: effectiveStablecoinPrice,\n                                    // Two-step process tracking\n                                    stepAAmount: amountCrypto1ToSell,\n                                    stepAPrice: crypto1StablecoinPrice,\n                                    stepBAmount: crypto2Reacquired,\n                                    stepBPrice: crypto2StablecoinPrice,\n                                    twoStepProcess: true // Flag to indicate this is a two-step transaction\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        // For StablecoinSwap mode, income should follow the sign of percentFromActualPrice\n                        if (state.config.tradingMode === \"StablecoinSwap\") {\n                            // If percentFromActualPrice is negative, income should be negative\n                            // If percentFromActualPrice is positive, income should be positive\n                            const incomeMultiplier = percentFromActualPrice >= 0 ? 1 : -1;\n                            const absUnrealizedProfit = Math.abs(totalUnrealizedProfitInCrypto2);\n                            incomeCrypto2 = incomeMultiplier * (absUnrealizedProfit * state.config.incomeSplitCrypto2Percent) / 100;\n                            if (currentPrice > 0) {\n                                incomeCrypto1 = incomeMultiplier * (absUnrealizedProfit * state.config.incomeSplitCrypto1Percent / 100) / currentPrice;\n                            }\n                        } else {\n                            // For SimpleSpot mode, use the original logic\n                            incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                            if (currentPrice > 0) {\n                                incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                            }\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot starts if none exists and handle runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot starts\n            if (state.botSystemStatus === 'WarmingUp' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading - only when bot is running\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            // Only auto-save and create new sessions if the bot is actually running\n            if (currentSessionId && state.botSystemStatus === 'Running') {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during active trading, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot();\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": (savedSessionId)=>{\n                                sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Create new session for the new crypto pair\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair during active trading:', state.config.crypto1, '/', state.config.crypto2);\n                                toast({\n                                    title: \"Crypto Pair Changed During Trading\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2,\n        state.botSystemStatus\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": (backupSessionId)=>{\n                                            sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Session restoration on app startup\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const savedSession = localStorage.getItem(SESSION_STORAGE_KEY);\n            if (savedSession) {\n                try {\n                    const sessionData = JSON.parse(savedSession);\n                    dispatch({\n                        type: 'RESTORE_SESSION',\n                        payload: sessionData\n                    });\n                    console.log('🔄 Session restored from localStorage - manual bot start required');\n                } catch (error) {\n                    console.error('Failed to restore session:', error);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    // Auto-save session data every 30 seconds when trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const interval = setInterval({\n                \"TradingProvider.useEffect.interval\": ()=>{\n                    if (state.isTrading) {\n                        saveSessionToStorage(state);\n                    }\n                }\n            }[\"TradingProvider.useEffect.interval\"], 30000);\n            return ({\n                \"TradingProvider.useEffect\": ()=>clearInterval(interval)\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.isTrading,\n        state\n    ]);\n    // Auto-save balances after each trade (both session and global)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveBalanceToStorage(state); // Session-specific balance storage\n            saveGlobalBalanceToStorage(state); // Global persistent balance storage\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        state.totalProfitLoss\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, create one first\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config).then({\n                            \"TradingProvider.useCallback[saveCurrentSession]\": (sessionId)=>{\n                                sessionManager.setCurrentSession(sessionId);\n                                sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                            }\n                        }[\"TradingProvider.useCallback[saveCurrentSession]\"]);\n                        return true;\n                    }\n                    return false;\n                }\n                // Save existing session\n                return sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Telegram notification callback\n    const sendTelegramNotificationCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotificationCallback]\": async (message, type)=>{\n            await sendTelegramNotification(message, type, state.telegramConfig);\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotificationCallback]\"], [\n        state.telegramConfig\n    ]);\n    // Reset global balances to defaults (admin function)\n    const resetGlobalBalances = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[resetGlobalBalances]\": ()=>{\n            const defaultBalances = {\n                crypto1Balance: 10,\n                crypto2Balance: 100000,\n                stablecoinBalance: 0,\n                totalProfitLoss: 0\n            };\n            // Update current state\n            dispatch({\n                type: 'UPDATE_BALANCES',\n                payload: {\n                    crypto1: defaultBalances.crypto1Balance,\n                    crypto2: defaultBalances.crypto2Balance,\n                    stablecoin: defaultBalances.stablecoinBalance\n                }\n            });\n            // Clear global storage\n            localStorage.removeItem(GLOBAL_BALANCE_STORAGE_KEY);\n            console.log('🔄 Global balances reset to defaults');\n            return defaultBalances;\n        }\n    }[\"TradingProvider.useCallback[resetGlobalBalances]\"], []);\n    // Session alarm callback\n    const playSessionAlarmCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSessionAlarmCallback]\": (type)=>{\n            playSessionAlarm(type, state.sessionAlarmConfig);\n        }\n    }[\"TradingProvider.useCallback[playSessionAlarmCallback]\"], [\n        state.sessionAlarmConfig\n    ]);\n    // StablecoinSwap price fetching callback\n    const fetchStablecoinSwapPriceCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchStablecoinSwapPriceCallback]\": async (crypto1, crypto2)=>{\n            return await fetchStablecoinSwapPrice(crypto1, crypto2);\n        }\n    }[\"TradingProvider.useCallback[fetchStablecoinSwapPriceCallback]\"], []);\n    // Enhanced calculate total P/L callback with improved StablecoinSwap logic\n    const calculateTotalPL = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[calculateTotalPL]\": ()=>{\n            if (state.config.tradingMode === \"StablecoinSwap\") {\n                // Calculate total realized P/L in crypto2 terms using enhanced tracking\n                const totalRealizedPL = state.orderHistory.filter({\n                    \"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\": (entry)=>(entry.type === 'SELL' || entry.orderType === 'SELL') && entry.tradingMode === 'StablecoinSwap' && entry.realizedProfitLossCrypto2 !== undefined\n                }[\"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\"]).reduce({\n                    \"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\": (sum, entry)=>sum + (entry.realizedProfitLossCrypto2 || 0)\n                }[\"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\"], 0);\n                return totalRealizedPL;\n            } else {\n                // Enhanced SimpleSpot logic with better filtering\n                const totalRealizedPL = state.orderHistory.filter({\n                    \"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\": (entry)=>(entry.type === 'SELL' || entry.orderType === 'SELL') && (entry.tradingMode === 'SimpleSpot' || !entry.tradingMode) && entry.realizedProfitLossCrypto2 !== undefined\n                }[\"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\"]).reduce({\n                    \"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\": (sum, entry)=>sum + (entry.realizedProfitLossCrypto2 || 0)\n                }[\"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\"], 0);\n                return totalRealizedPL;\n            }\n        }\n    }[\"TradingProvider.useCallback[calculateTotalPL]\"], [\n        state.orderHistory,\n        state.config.tradingMode\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        sendTelegramNotification: sendTelegramNotificationCallback,\n        playSessionAlarm: playSessionAlarmCallback,\n        fetchStablecoinSwapPrice: fetchStablecoinSwapPriceCallback,\n        calculateTotalPL,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 2020,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"L04PF0kGhKzBDSaJb0qU9iqy7rw=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});