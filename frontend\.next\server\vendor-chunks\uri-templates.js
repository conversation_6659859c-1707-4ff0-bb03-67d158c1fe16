/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/uri-templates";
exports.ids = ["vendor-chunks/uri-templates"];
exports.modules = {

/***/ "(action-browser)/./node_modules/uri-templates/uri-templates.js":
/*!*****************************************************!*\
  !*** ./node_modules/uri-templates/uri-templates.js ***!
  \*****************************************************/
/***/ (function(module, exports) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;(function (global, factory) {\n\tif (true) {\n\t\t!(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\t} else {}\n})(this, function () {\n\tvar uriTemplateGlobalModifiers = {\n\t\t\"+\": true,\n\t\t\"#\": true,\n\t\t\".\": true,\n\t\t\"/\": true,\n\t\t\";\": true,\n\t\t\"?\": true,\n\t\t\"&\": true\n\t};\n\tvar uriTemplateSuffices = {\n\t\t\"*\": true\n\t};\n\tvar urlEscapedChars = /[:/&?#]/;\n\n\tfunction notReallyPercentEncode(string) {\n\t\treturn encodeURI(string).replace(/%25[0-9][0-9]/g, function (doubleEncoded) {\n\t\t\treturn \"%\" + doubleEncoded.substring(3);\n\t\t});\n\t}\n\n\tfunction isPercentEncoded(string) {\n\t\tstring = string.replace(/%../g, '');\n\t\treturn encodeURIComponent(string) === string;\n\t}\n\n\tfunction uriTemplateSubstitution(spec) {\n\t\tvar modifier = \"\";\n\t\tif (uriTemplateGlobalModifiers[spec.charAt(0)]) {\n\t\t\tmodifier = spec.charAt(0);\n\t\t\tspec = spec.substring(1);\n\t\t}\n\t\tvar separator = \"\";\n\t\tvar prefix = \"\";\n\t\tvar shouldEscape = true;\n\t\tvar showVariables = false;\n\t\tvar trimEmptyString = false;\n\t\tif (modifier == '+') {\n\t\t\tshouldEscape = false;\n\t\t} else if (modifier == \".\") {\n\t\t\tprefix = \".\";\n\t\t\tseparator = \".\";\n\t\t} else if (modifier == \"/\") {\n\t\t\tprefix = \"/\";\n\t\t\tseparator = \"/\";\n\t\t} else if (modifier == '#') {\n\t\t\tprefix = \"#\";\n\t\t\tshouldEscape = false;\n\t\t} else if (modifier == ';') {\n\t\t\tprefix = \";\";\n\t\t\tseparator = \";\",\n\t\t\tshowVariables = true;\n\t\t\ttrimEmptyString = true;\n\t\t} else if (modifier == '?') {\n\t\t\tprefix = \"?\";\n\t\t\tseparator = \"&\",\n\t\t\tshowVariables = true;\n\t\t} else if (modifier == '&') {\n\t\t\tprefix = \"&\";\n\t\t\tseparator = \"&\",\n\t\t\tshowVariables = true;\n\t\t}\n\n\t\tvar varNames = [];\n\t\tvar varList = spec.split(\",\");\n\t\tvar varSpecs = [];\n\t\tvar varSpecMap = {};\n\t\tfor (var i = 0; i < varList.length; i++) {\n\t\t\tvar varName = varList[i];\n\t\t\tvar truncate = null;\n\t\t\tif (varName.indexOf(\":\") != -1) {\n\t\t\t\tvar parts = varName.split(\":\");\n\t\t\t\tvarName = parts[0];\n\t\t\t\ttruncate = parseInt(parts[1]);\n\t\t\t}\n\t\t\tvar suffices = {};\n\t\t\twhile (uriTemplateSuffices[varName.charAt(varName.length - 1)]) {\n\t\t\t\tsuffices[varName.charAt(varName.length - 1)] = true;\n\t\t\t\tvarName = varName.substring(0, varName.length - 1);\n\t\t\t}\n\t\t\tvar varSpec = {\n\t\t\t\ttruncate: truncate,\n\t\t\t\tname: varName,\n\t\t\t\tsuffices: suffices\n\t\t\t};\n\t\t\tvarSpecs.push(varSpec);\n\t\t\tvarSpecMap[varName] = varSpec;\n\t\t\tvarNames.push(varName);\n\t\t}\n\t\tvar subFunction = function (valueFunction) {\n\t\t\tvar result = \"\";\n\t\t\tvar startIndex = 0;\n\t\t\tfor (var i = 0; i < varSpecs.length; i++) {\n\t\t\t\tvar varSpec = varSpecs[i];\n\t\t\t\tvar value = valueFunction(varSpec.name);\n\t\t\t\tif (value == null || (Array.isArray(value) && value.length == 0) || (typeof value == 'object' && Object.keys(value).length == 0)) {\n\t\t\t\t\tstartIndex++;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tif (i == startIndex) {\n\t\t\t\t\tresult += prefix;\n\t\t\t\t} else {\n\t\t\t\t\tresult += (separator || \",\");\n\t\t\t\t}\n\t\t\t\tif (Array.isArray(value)) {\n\t\t\t\t\tif (showVariables) {\n\t\t\t\t\t\tresult += varSpec.name + \"=\";\n\t\t\t\t\t}\n\t\t\t\t\tfor (var j = 0; j < value.length; j++) {\n\t\t\t\t\t\tif (j > 0) {\n\t\t\t\t\t\t\tresult += varSpec.suffices['*'] ? (separator || \",\") : \",\";\n\t\t\t\t\t\t\tif (varSpec.suffices['*'] && showVariables) {\n\t\t\t\t\t\t\t\tresult += varSpec.name + \"=\";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresult += shouldEscape ? encodeURIComponent(value[j]).replace(/!/g, \"%21\") : notReallyPercentEncode(value[j]);\n\t\t\t\t\t}\n\t\t\t\t} else if (typeof value == \"object\") {\n\t\t\t\t\tif (showVariables && !varSpec.suffices['*']) {\n\t\t\t\t\t\tresult += varSpec.name + \"=\";\n\t\t\t\t\t}\n\t\t\t\t\tvar first = true;\n\t\t\t\t\tfor (var key in value) {\n\t\t\t\t\t\tif (!first) {\n\t\t\t\t\t\t\tresult += varSpec.suffices['*'] ? (separator || \",\") : \",\";\n\t\t\t\t\t\t}\n\t\t\t\t\t\tfirst = false;\n\t\t\t\t\t\tresult += shouldEscape ? encodeURIComponent(key).replace(/!/g, \"%21\") : notReallyPercentEncode(key);\n\t\t\t\t\t\tresult += varSpec.suffices['*'] ? '=' : \",\";\n\t\t\t\t\t\tresult += shouldEscape ? encodeURIComponent(value[key]).replace(/!/g, \"%21\") : notReallyPercentEncode(value[key]);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (showVariables) {\n\t\t\t\t\t\tresult += varSpec.name;\n\t\t\t\t\t\tif (!trimEmptyString || value != \"\") {\n\t\t\t\t\t\t\tresult += \"=\";\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (varSpec.truncate != null) {\n\t\t\t\t\t\tvalue = value.substring(0, varSpec.truncate);\n\t\t\t\t\t}\n\t\t\t\t\tresult += shouldEscape ? encodeURIComponent(value).replace(/!/g, \"%21\"): notReallyPercentEncode(value);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn result;\n\t\t};\n\t\tvar guessFunction = function (stringValue, resultObj, strict) {\n\t\t\tif (prefix) {\n\t\t\t\tstringValue = stringValue.substring(prefix.length);\n\t\t\t}\n\t\t\tif (varSpecs.length == 1 && varSpecs[0].suffices['*']) {\n\t\t\t\tvar varSpec = varSpecs[0];\n\t\t\t\tvar varName = varSpec.name;\n\t\t\t\tvar arrayValue = varSpec.suffices['*'] ? stringValue.split(separator || \",\") : [stringValue];\n\t\t\t\tvar hasEquals = (shouldEscape && stringValue.indexOf('=') != -1);\t// There's otherwise no way to distinguish between \"{value*}\" for arrays and objects\n\t\t\t\tfor (var i = 1; i < arrayValue.length; i++) {\n\t\t\t\t\tvar stringValue = arrayValue[i];\n\t\t\t\t\tif (hasEquals && stringValue.indexOf('=') == -1) {\n\t\t\t\t\t\t// Bit of a hack - if we're expecting \"=\" for key/value pairs, and values can't contain \"=\", then assume a value has been accidentally split\n\t\t\t\t\t\tarrayValue[i - 1] += (separator || \",\") + stringValue;\n\t\t\t\t\t\tarrayValue.splice(i, 1);\n\t\t\t\t\t\ti--;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tfor (var i = 0; i < arrayValue.length; i++) {\n\t\t\t\t\tvar stringValue = arrayValue[i];\n\t\t\t\t\tif (shouldEscape && stringValue.indexOf('=') != -1) {\n\t\t\t\t\t\thasEquals = true;\n\t\t\t\t\t}\n\t\t\t\t\tvar innerArrayValue = stringValue.split(\",\");\n\t\t\t\t\tif (innerArrayValue.length == 1) {\n\t\t\t\t\t\tarrayValue[i] = innerArrayValue[0];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tarrayValue[i] = innerArrayValue;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (showVariables || hasEquals) {\n\t\t\t\t\tvar objectValue = resultObj[varName] || {};\n\t\t\t\t\tfor (var j = 0; j < arrayValue.length; j++) {\n\t\t\t\t\t\tvar innerValue = stringValue;\n\t\t\t\t\t\tif (showVariables && !innerValue) {\n\t\t\t\t\t\t\t// The empty string isn't a valid variable, so if our value is zero-length we have nothing\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (typeof arrayValue[j] == \"string\") {\n\t\t\t\t\t\t\tvar stringValue = arrayValue[j];\n\t\t\t\t\t\t\tvar innerVarName = stringValue.split(\"=\", 1)[0];\n\t\t\t\t\t\t\tvar stringValue = stringValue.substring(innerVarName.length + 1);\n\t\t\t\t\t\t\tif (shouldEscape) {\n\t\t\t\t\t\t\t\tif (strict && !isPercentEncoded(stringValue)) {\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tstringValue = decodeURIComponent(stringValue);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tinnerValue = stringValue;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tvar stringValue = arrayValue[j][0];\n\t\t\t\t\t\t\tvar innerVarName = stringValue.split(\"=\", 1)[0];\n\t\t\t\t\t\t\tvar stringValue = stringValue.substring(innerVarName.length + 1);\n\t\t\t\t\t\t\tif (shouldEscape) {\n\t\t\t\t\t\t\t\tif (strict && !isPercentEncoded(stringValue)) {\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tstringValue = decodeURIComponent(stringValue);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tarrayValue[j][0] = stringValue;\n\t\t\t\t\t\t\tinnerValue = arrayValue[j];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (shouldEscape) {\n\t\t\t\t\t\t\tif (strict && !isPercentEncoded(innerVarName)) {\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tinnerVarName = decodeURIComponent(innerVarName);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (objectValue[innerVarName] !== undefined) {\n\t\t\t\t\t\t\tif (Array.isArray(objectValue[innerVarName])) {\n\t\t\t\t\t\t\t\tobjectValue[innerVarName].push(innerValue);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tobjectValue[innerVarName] = [objectValue[innerVarName], innerValue];\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tobjectValue[innerVarName] = innerValue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (Object.keys(objectValue).length == 1 && objectValue[varName] !== undefined) {\n\t\t\t\t\t\tresultObj[varName] = objectValue[varName];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tresultObj[varName] = objectValue;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (shouldEscape) {\n\t\t\t\t\t\tfor (var j = 0; j < arrayValue.length; j++) {\n\t\t\t\t\t\t\tvar innerArrayValue = arrayValue[j];\n\t\t\t\t\t\t\tif (Array.isArray(innerArrayValue)) {\n\t\t\t\t\t\t\t\tfor (var k = 0; k < innerArrayValue.length; k++) {\n\t\t\t\t\t\t\t\t\tif (strict && !isPercentEncoded(innerArrayValue[k])) {\n\t\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tinnerArrayValue[k] = decodeURIComponent(innerArrayValue[k]);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tif (strict && !isPercentEncoded(innerArrayValue)) {\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tarrayValue[j] = decodeURIComponent(innerArrayValue);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tif (resultObj[varName] !== undefined) {\n\t\t\t\t\t\tif (Array.isArray(resultObj[varName])) {\n\t\t\t\t\t\t\tresultObj[varName] = resultObj[varName].concat(arrayValue);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresultObj[varName] = [resultObj[varName]].concat(arrayValue);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (arrayValue.length == 1 && !varSpec.suffices['*']) {\n\t\t\t\t\t\t\tresultObj[varName] = arrayValue[0];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresultObj[varName] = arrayValue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tvar arrayValue = (varSpecs.length == 1) ? [stringValue] : stringValue.split(separator || \",\");\n\t\t\t\tvar specIndexMap = {};\n\t\t\t\tfor (var i = 0; i < arrayValue.length; i++) {\n\t\t\t\t\t// Try from beginning\n\t\t\t\t\tvar firstStarred = 0;\n\t\t\t\t\tfor (; firstStarred < varSpecs.length - 1 && firstStarred < i; firstStarred++) {\n\t\t\t\t\t\tif (varSpecs[firstStarred].suffices['*']) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (firstStarred == i) {\n\t\t\t\t\t\t// The first [i] of them have no \"*\" suffix\n\t\t\t\t\t\tspecIndexMap[i] = i;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// Try from the end\n\t\t\t\t\t\tfor (var lastStarred = varSpecs.length - 1; lastStarred > 0 && (varSpecs.length - lastStarred) < (arrayValue.length - i); lastStarred--) {\n\t\t\t\t\t\t\tif (varSpecs[lastStarred].suffices['*']) {\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif ((varSpecs.length - lastStarred) == (arrayValue.length - i)) {\n\t\t\t\t\t\t\t// The last [length - i] of them have no \"*\" suffix\n\t\t\t\t\t\t\tspecIndexMap[i] = lastStarred;\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t// Just give up and use the first one\n\t\t\t\t\tspecIndexMap[i] = firstStarred;\n\t\t\t\t}\n\t\t\t\tfor (var i = 0; i < arrayValue.length; i++) {\n\t\t\t\t\tvar stringValue = arrayValue[i];\n\t\t\t\t\tif (!stringValue && showVariables) {\n\t\t\t\t\t\t// The empty string isn't a valid variable, so if our value is zero-length we have nothing\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tvar innerArrayValue = stringValue.split(\",\");\n\t\t\t\t\tvar hasEquals = false;\n\n\t\t\t\t\tif (showVariables) {\n\t\t\t\t\t\tvar stringValue = innerArrayValue[0]; // using innerArrayValue\n\t\t\t\t\t\tvar varName = stringValue.split(\"=\", 1)[0];\n\t\t\t\t\t\tvar stringValue = stringValue.substring(varName.length + 1);\n\t\t\t\t\t\tinnerArrayValue[0] = stringValue;\n\t\t\t\t\t\tvar varSpec = varSpecMap[varName] || varSpecs[0];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvar varSpec = varSpecs[specIndexMap[i]];\n\t\t\t\t\t\tvar varName = varSpec.name;\n\t\t\t\t\t}\n\n\t\t\t\t\tfor (var j = 0; j < innerArrayValue.length; j++) {\n\t\t\t\t\t\tif (shouldEscape) {\n\t\t\t\t\t\t\tif (strict && !isPercentEncoded(innerArrayValue[j])) {\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tinnerArrayValue[j] = decodeURIComponent(innerArrayValue[j]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tif ((showVariables || varSpec.suffices['*'])&& resultObj[varName] !== undefined) {\n\t\t\t\t\t\tif (Array.isArray(resultObj[varName])) {\n\t\t\t\t\t\t\tresultObj[varName] = resultObj[varName].concat(innerArrayValue);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresultObj[varName] = [resultObj[varName]].concat(innerArrayValue);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (innerArrayValue.length == 1 && !varSpec.suffices['*']) {\n\t\t\t\t\t\t\tresultObj[varName] = innerArrayValue[0];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tresultObj[varName] = innerArrayValue;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn 1;\n\t\t};\n\t\treturn {\n\t\t\tvarNames: varNames,\n\t\t\tprefix: prefix,\n\t\t\tsubstitution: subFunction,\n\t\t\tunSubstitution: guessFunction\n\t\t};\n\t}\n\n\tfunction UriTemplate(template) {\n\t\tif (!(this instanceof UriTemplate)) {\n\t\t\treturn new UriTemplate(template);\n\t\t}\n\t\tvar parts = template.split(\"{\");\n\t\tvar textParts = [parts.shift()];\n\t\tvar prefixes = [];\n\t\tvar substitutions = [];\n\t\tvar unSubstitutions = [];\n\t\tvar varNames = [];\n\t\twhile (parts.length > 0) {\n\t\t\tvar part = parts.shift();\n\t\t\tvar spec = part.split(\"}\")[0];\n\t\t\tvar remainder = part.substring(spec.length + 1);\n\t\t\tvar funcs = uriTemplateSubstitution(spec);\n\t\t\tsubstitutions.push(funcs.substitution);\n\t\t\tunSubstitutions.push(funcs.unSubstitution);\n\t\t\tprefixes.push(funcs.prefix);\n\t\t\ttextParts.push(remainder);\n\t\t\tvarNames = varNames.concat(funcs.varNames);\n\t\t}\n\t\tthis.fill = function (valueFunction) {\n\t\t\tif (valueFunction && typeof valueFunction !== 'function') {\n\t\t\t\tvar value = valueFunction;\n\t\t\t\tvalueFunction = function (varName) {\n\t\t\t\t\treturn value[varName];\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tvar result = textParts[0];\n\t\t\tfor (var i = 0; i < substitutions.length; i++) {\n\t\t\t\tvar substitution = substitutions[i];\n\t\t\t\tresult += substitution(valueFunction);\n\t\t\t\tresult += textParts[i + 1];\n\t\t\t}\n\t\t\treturn result;\n\t\t};\n\t\tthis.fromUri = function (substituted, options) {\n\t\t\toptions = options || {};\n\t\t\tvar result = {};\n\t\t\tfor (var i = 0; i < textParts.length; i++) {\n\t\t\t\tvar part = textParts[i];\n\t\t\t\tif (substituted.substring(0, part.length) !== part) {\n\t\t\t\t\treturn /*undefined*/;\n\t\t\t\t}\n\t\t\t\tsubstituted = substituted.substring(part.length);\n\t\t\t\tif (i >= textParts.length - 1) {\n\t\t\t\t\t// We've run out of input - is there any template left?\n\t\t\t\t\tif (substituted == \"\") {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn /*undefined*/;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvar prefix = prefixes[i];\n\t\t\t\tif (prefix && substituted.substring(0, prefix.length) !== prefix) {\n\t\t\t\t\t// All values are optional - if we have a prefix and it doesn't match, move along\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Find the next part to un-substitute\n\t\t\t\tvar nextPart = textParts[i + 1];\n\t\t\t\tvar offset = i;\n\t\t\t\twhile (true) {\n\t\t\t\t\tif (offset == textParts.length - 2) {\n\t\t\t\t\t\tvar endPart = substituted.substring(substituted.length - nextPart.length);\n\t\t\t\t\t\tif (endPart !== nextPart) {\n\t\t\t\t\t\t\treturn /*undefined*/;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvar stringValue = substituted.substring(0, substituted.length - nextPart.length);\n\t\t\t\t\t\tsubstituted = endPart;\n\t\t\t\t\t} else if (nextPart) {\n\t\t\t\t\t\tvar nextPartPos = substituted.indexOf(nextPart);\n\t\t\t\t\t\tvar stringValue = substituted.substring(0, nextPartPos);\n\t\t\t\t\t\tsubstituted = substituted.substring(nextPartPos);\n\t\t\t\t\t} else if (prefixes[offset + 1]) {\n\t\t\t\t\t\tvar nextPartPos = substituted.indexOf(prefixes[offset + 1]);\n\t\t\t\t\t\tif (nextPartPos === -1) nextPartPos = substituted.length;\n\t\t\t\t\t\tvar stringValue = substituted.substring(0, nextPartPos);\n\t\t\t\t\t\tsubstituted = substituted.substring(nextPartPos);\n\t\t\t\t\t} else if (textParts.length > offset + 2) {\n\t\t\t\t\t\t// If the separator between this variable and the next is blank (with no prefix), continue onwards\n\t\t\t\t\t\toffset++;\n\t\t\t\t\t\tnextPart = textParts[offset + 1];\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvar stringValue = substituted;\n\t\t\t\t\t\tsubstituted = \"\";\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tif (!unSubstitutions[i](stringValue, result, options.strict)) {\n\t\t\t\t\treturn /*undefined*/;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn result;\n\t\t}\n\t\tthis.varNames = varNames;\n\t\tthis.template = template;\n\t}\n\tUriTemplate.prototype = {\n\t\ttoString: function () {\n\t\t\treturn this.template;\n\t\t},\n\t\tfillFromObject: function (obj) {\n\t\t\treturn this.fill(obj);\n\t\t},\n\t\ttest: function (uri, options) {\n\t\t\treturn !!this.fromUri(uri, options)\n\t\t}\n\t};\n\n\treturn UriTemplate;\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/uri-templates/uri-templates.js\n");

/***/ })

};
;