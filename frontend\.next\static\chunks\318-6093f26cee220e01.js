"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[318],{25731:(e,t,o)=>{o.d(t,{Rk:()=>s,ZQ:()=>a,oc:()=>i});let n="http://localhost:5000";async function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o="".concat(n).concat(e),r=localStorage.getItem("plutoAuthToken"),a={"Content-Type":"application/json",...r?{Authorization:"Bearer ".concat(r)}:{},...t.headers};try{let e;let n=new AbortController,r=setTimeout(()=>n.abort(),1e4),i=await fetch(o,{...t,headers:a,signal:n.signal}).finally(()=>clearTimeout(r));if(401===i.status)throw localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),window.location.href="/login",Error("Authentication expired. Please login again.");let s=i.headers.get("content-type");if(s&&s.includes("application/json"))e=await i.json();else{let t=await i.text();try{e=JSON.parse(t)}catch(o){e={message:t}}}if(!i.ok)throw console.error("API error response:",e),Error(e.error||e.message||"API error: ".concat(i.status));return e}catch(e){if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw console.error("Network error - Is the backend server running?:",e),Error("Cannot connect to server. Please check if the backend is running.");if("AbortError"===e.name)throw console.error("Request timeout:",e),Error("Request timed out. Server may be unavailable.");throw console.error("API request failed:",e),e}}console.log("API Base URL:",n);let a={login:async(e,t)=>{try{let o=await c(async()=>await r("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:t})}));if(o&&o.access_token)return localStorage.setItem("plutoAuthToken",o.access_token),localStorage.setItem("plutoAuth","true"),o.user&&localStorage.setItem("plutoUser",JSON.stringify(o.user)),!0;return!1}catch(e){return console.error("Login API error:",e),!1}},register:async(e,t,o)=>c(async()=>await r("/auth/register",{method:"POST",body:JSON.stringify({username:e,password:t,email:o})})),logout:async()=>(localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),!0)},i={getConfig:async e=>r(e?"/trading/config/".concat(e):"/trading/config"),saveConfig:async e=>r("/trading/config",{method:"POST",body:JSON.stringify(e)}),updateConfig:async(e,t)=>r("/trading/config/".concat(e),{method:"PUT",body:JSON.stringify(t)}),startBot:async e=>r("/trading/bot/start/".concat(e),{method:"POST"}),stopBot:async e=>r("/trading/bot/stop/".concat(e),{method:"POST"}),getBotStatus:async e=>r("/trading/bot/status/".concat(e)),getTradeHistory:async e=>r("/trading/history".concat(e?"?configId=".concat(e):"")),getBalances:async()=>r("/trading/balances"),getMarketPrice:async e=>r("/trading/market-data/".concat(e)),getTradingPairs:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return r("/trading/exchange/trading-pairs?exchange=".concat(e))},getCryptocurrencies:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return r("/trading/exchange/cryptocurrencies?exchange=".concat(e))}},s={getAllSessions:async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return r("/sessions/?include_inactive=".concat(e))},createSession:async e=>r("/sessions/",{method:"POST",body:JSON.stringify(e)}),getSession:async e=>r("/sessions/".concat(e)),updateSession:async(e,t)=>r("/sessions/".concat(e),{method:"PUT",body:JSON.stringify(t)}),deleteSession:async e=>r("/sessions/".concat(e),{method:"DELETE"}),activateSession:async e=>r("/sessions/".concat(e,"/activate"),{method:"POST"}),getSessionHistory:async e=>r("/sessions/".concat(e,"/history")),getActiveSession:async()=>r("/sessions/active")},c=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,o=0,n=async()=>{try{return await e()}catch(e){if((e instanceof TypeError&&e.message.includes("Failed to fetch")||"AbortError"===e.name)&&o<t){let e=500*Math.pow(2,o);return console.log("Retrying after ".concat(e,"ms (attempt ").concat(o+1,"/").concat(t,")...")),o++,await new Promise(t=>setTimeout(t,e)),n()}throw e}};return n()}},29348:(e,t,o)=>{o.d(t,{Oh:()=>n,Ql:()=>i,hg:()=>r,vA:()=>a});let n={soundAlertsEnabled:!0,alertOnOrderExecution:!0,alertOnError:!0,soundOrderExecution:"/sounds/order-executed.mp3",soundError:"/sounds/error.mp3",clearOrderHistoryOnStart:!1},r=["BTC","ETH","ADA","SOL","DOGE","LINK","MATIC","DOT","AVAX","XRP","LTC","BCH","BNB","SHIB"],a={BTC:["USDT","USDC","FDUSD","EUR"],ETH:["USDT","USDC","FDUSD","BTC","EUR"],ADA:["USDT","USDC","BTC","ETH"],SOL:["USDT","USDC","BTC","ETH"]},i=["USDT","USDC","FDUSD","DAI"]},59434:(e,t,o)=>{o.d(t,{cn:()=>a});var n=o(52596),r=o(39688);function a(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,r.QP)((0,n.$)(t))}},77213:(e,t,o)=>{o.d(t,{TradingProvider:()=>F,U:()=>N});var n=o(95155),r=o(12115),a=o(29348),i=o(79737),s=o(87481),c=o(25731),l=o(84553);class d{static getInstance(){return d.instance||(d.instance=new d),d.instance}setupEventListeners(){window.addEventListener("online",this.handleOnline.bind(this)),window.addEventListener("offline",this.handleOffline.bind(this)),document.addEventListener("visibilitychange",()=>{document.hidden||this.checkConnection()})}handleOnline(){console.log("\uD83C\uDF10 Network: Back online"),this.isOnline=!0,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.notifyListeners(!0,!this.hasInitialized)}handleOffline(){console.log("\uD83C\uDF10 Network: Gone offline"),this.isOnline=!1,this.notifyListeners(!1,!this.hasInitialized)}async checkConnection(){let e=navigator.onLine;return e!==this.isOnline&&(this.isOnline=e,this.notifyListeners(e,!this.hasInitialized),e&&(this.lastOnlineTime=Date.now(),this.reconnectAttempts=0)),e}startPeriodicCheck(){let e=setInterval(()=>{this.checkConnection()},6e4);this.periodicInterval=e}cleanup(){this.periodicInterval&&clearInterval(this.periodicInterval),this.listeners.clear()}notifyListeners(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.listeners.forEach(o=>{try{o(e,t)}catch(e){console.error("Error in network status listener:",e)}})}addListener(e){return this.listeners.add(e),()=>{this.listeners.delete(e)}}getStatus(){return{isOnline:this.isOnline,lastOnlineTime:this.lastOnlineTime,reconnectAttempts:this.reconnectAttempts}}async forceCheck(){return await this.checkConnection()}async attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return console.log("\uD83C\uDF10 Network: Max reconnect attempts reached"),!1;this.reconnectAttempts++;let e=Math.min(this.reconnectInterval*Math.pow(2,this.reconnectAttempts-1),3e4);console.log("\uD83C\uDF10 Network: Attempting reconnect ".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts," in ").concat(e,"ms")),await new Promise(t=>setTimeout(t,e));let t=await this.checkConnection();return!t&&this.reconnectAttempts<this.maxReconnectAttempts&&setTimeout(()=>this.attemptReconnect(),1e3),t}constructor(){this.isOnline=navigator.onLine,this.listeners=new Set,this.lastOnlineTime=Date.now(),this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=5e3,this.hasInitialized=!1,this.setupEventListeners(),this.startPeriodicCheck(),setTimeout(()=>{this.hasInitialized=!0},1e3)}}class u{static getInstance(){return u.instance||(u.instance=new u),u.instance}setupNetworkListener(){this.networkMonitor.addListener(e=>{e&&this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving on network reconnection"),this.saveFunction(),this.lastSaveTime=Date.now())})}setupBeforeUnloadListener(){window.addEventListener("beforeunload",()=>{this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving before page unload"),this.saveFunction())}),document.addEventListener("visibilitychange",()=>{document.hidden&&this.saveFunction&&(console.log("\uD83D\uDCBE Auto-save: Saving on tab switch"),this.saveFunction(),this.lastSaveTime=Date.now())})}enable(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3e4;this.saveFunction=e,this.intervalMs=t,this.isEnabled=!0,this.stop(),this.saveInterval=setInterval(()=>{this.isEnabled&&this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(console.log("\uD83D\uDCBE Auto-save: Periodic save"),this.saveFunction(),this.lastSaveTime=Date.now())},this.intervalMs),console.log("\uD83D\uDCBE Auto-save: Enabled with ".concat(t,"ms interval"))}disable(){this.isEnabled=!1,this.stop(),console.log("\uD83D\uDCBE Auto-save: Disabled")}stop(){this.saveInterval&&(clearInterval(this.saveInterval),this.saveInterval=null)}saveNow(){this.saveFunction&&this.networkMonitor.getStatus().isOnline&&(console.log("\uD83D\uDCBE Auto-save: Manual save triggered"),this.saveFunction(),this.lastSaveTime=Date.now())}getStatus(){return{isEnabled:this.isEnabled,lastSaveTime:this.lastSaveTime,intervalMs:this.intervalMs,isOnline:this.networkMonitor.getStatus().isOnline}}constructor(){this.saveInterval=null,this.saveFunction=null,this.intervalMs=3e4,this.isEnabled=!0,this.lastSaveTime=0,this.networkMonitor=d.getInstance(),this.setupNetworkListener(),this.setupBeforeUnloadListener()}}class p{static getInstance(){return p.instance||(p.instance=new p),p.instance}startMonitoring(){console.log("\uD83D\uDCCA Memory monitoring disabled to prevent frequent notifications")}checkMemoryUsage(){if("memory"in performance){let e=performance.memory,t=e.usedJSHeapSize;this.notifyListeners(e),t>this.criticalThreshold?(console.warn("\uD83E\uDDE0 Memory: Critical memory usage detected:",{used:"".concat((t/1024/1024).toFixed(2),"MB"),total:"".concat((e.totalJSHeapSize/1024/1024).toFixed(2),"MB"),limit:"".concat((e.jsHeapSizeLimit/1024/1024).toFixed(2),"MB")}),"gc"in window&&window.gc()):t>this.warningThreshold&&console.log("\uD83E\uDDE0 Memory: High memory usage:",{used:"".concat((t/1024/1024).toFixed(2),"MB"),total:"".concat((e.totalJSHeapSize/1024/1024).toFixed(2),"MB")})}}notifyListeners(e){this.listeners.forEach(t=>{try{t(e)}catch(e){console.error("Error in memory monitor listener:",e)}})}addListener(e){return this.listeners.add(e),()=>this.listeners.delete(e)}getMemoryUsage(){return"memory"in performance?performance.memory:null}stop(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null)}constructor(){this.checkInterval=null,this.warningThreshold=262144e3,this.criticalThreshold=0x19000000,this.listeners=new Set,this.startMonitoring()}}let g=e=>e.crypto1&&e.crypto2?f(e):0,y=async e=>{try{var t,o,n;if(!e.crypto1||!e.crypto2)return 0;if("StablecoinSwap"===e.tradingMode){let n=e.preferredStablecoin||"USDT";try{let r=S(e.crypto1),a=S(e.crypto2),i=S(n);if(r&&a&&i){let s=await fetch("https://api.coingecko.com/api/v3/simple/price?ids=".concat(r,",").concat(a,"&vs_currencies=").concat(i));if(s.ok){let c=await s.json(),l=null===(t=c[r])||void 0===t?void 0:t[i],d=null===(o=c[a])||void 0===o?void 0:o[i];if(l>0&&d>0){let t=l/d;return console.log("\uD83D\uDCCA StablecoinSwap price: ".concat(e.crypto1,"/").concat(e.crypto2," = ").concat(t," (").concat(e.crypto1,": ").concat(l," ").concat(n,", ").concat(e.crypto2,": ").concat(d," ").concat(n,")")),t}}}let s=await h(e.crypto1,n),c=await h(e.crypto2,n),l=s/c;return console.log("\uD83D\uDCCA StablecoinSwap price (fallback): ".concat(e.crypto1,"/").concat(e.crypto2," = ").concat(l," (via ").concat(n,")")),l}catch(n){console.error("Error fetching StablecoinSwap prices:",n);let t=m(e.crypto1),o=m(e.crypto2);return t/o}}let r="".concat(e.crypto1).concat(e.crypto2).toUpperCase();try{let t=await fetch("https://api.binance.com/api/v3/ticker/price?symbol=".concat(r));if(t.ok){let o=await t.json(),n=parseFloat(o.price);if(n>0)return console.log("✅ Price fetched from Binance: ".concat(e.crypto1,"/").concat(e.crypto2," = ").concat(n)),n}}catch(e){console.warn("Binance API failed, trying alternative...",e)}try{let t=S(e.crypto1),o=S(e.crypto2);if(t&&o){let r=await fetch("https://api.coingecko.com/api/v3/simple/price?ids=".concat(t,"&vs_currencies=").concat(o));if(r.ok){let a=await r.json(),i=null===(n=a[t])||void 0===n?void 0:n[o];if(i>0)return console.log("✅ Price fetched from CoinGecko: ".concat(e.crypto1,"/").concat(e.crypto2," = ").concat(i)),i}}}catch(e){console.warn("CoinGecko API failed, using mock price...",e)}let a=f(e);return console.log("⚠️ Using mock price: ".concat(e.crypto1,"/").concat(e.crypto2," = ").concat(a)),a}catch(t){return console.error("Error fetching market price:",t),f(e)}},S=e=>({BTC:"bitcoin",ETH:"ethereum",SOL:"solana",ADA:"cardano",DOT:"polkadot",MATIC:"matic-network",AVAX:"avalanche-2",LINK:"chainlink",UNI:"uniswap",USDT:"tether",USDC:"usd-coin",BUSD:"binance-usd",DAI:"dai"})[e.toUpperCase()]||null,h=async(e,t)=>{try{if(S(e)&&S(t)){let n=S(e),r=S(t);if(n===r)return 1;let a=await fetch("https://api.coingecko.com/api/v3/simple/price?ids=".concat(n,"&vs_currencies=").concat(r));if(a.ok){var o;let i=await a.json(),s=n&&r?null===(o=i[n])||void 0===o?void 0:o[r]:null;if(s>0)return console.log("\uD83D\uDCCA Stablecoin rate: ".concat(e,"/").concat(t," = ").concat(s)),s}}let n=m(e),r=m(t),a=n/r;return console.log("\uD83D\uDCCA Fallback stablecoin rate: ".concat(e,"/").concat(t," = ").concat(a," (via USD)")),a}catch(o){return console.error("Error fetching stablecoin exchange rate:",o),m(e)/m(t)}},m=e=>({BTC:109e3,ETH:4e3,SOL:240,ADA:1.2,DOGE:.4,LINK:25,MATIC:.5,DOT:8,AVAX:45,SHIB:3e-5,XRP:2.5,LTC:110,BCH:500,UNI:15,AAVE:180,MKR:1800,SNX:3.5,COMP:85,YFI:8500,SUSHI:2.1,"1INCH":.65,CRV:.85,UMA:3.2,ATOM:12,NEAR:6.5,ALGO:.35,ICP:14,HBAR:.28,APT:12.5,TON:5.8,FTM:.95,ONE:.025,FIL:8.5,TRX:.25,ETC:35,VET:.055,QNT:125,LDO:2.8,CRO:.18,LUNC:15e-5,MANA:.85,SAND:.75,AXS:8.5,ENJ:.45,CHZ:.12,THETA:2.1,FLOW:1.2,XTZ:1.8,EOS:1.1,GRT:.28,BAT:.35,ZEC:45,DASH:35,LRC:.45,ZRX:.65,KNC:.85,REN:.15,BAND:2.5,STORJ:.85,NMR:25,ANT:8.5,BNT:.95,MLN:35,REP:15,IOTX:.065,ZIL:.045,ICX:.35,QTUM:4.5,ONT:.45,WAVES:3.2,LSK:1.8,NANO:1.5,SC:.008,DGB:.025,RVN:.035,BTT:15e-7,WIN:15e-5,HOT:.0035,DENT:.0018,NPXS:85e-5,FUN:.0085,CELR:.025,USDT:1,USDC:1,FDUSD:1,BUSD:1,DAI:1})[e.toUpperCase()]||100,f=e=>{let t=m(e.crypto1),o=m(e.crypto2),n=t/o*(1+(Math.random()-.5)*.02);return console.log("\uD83D\uDCCA Fallback price calculation: ".concat(e.crypto1," ($").concat(t,") / ").concat(e.crypto2," ($").concat(o,") = ").concat(n.toFixed(6))),n},C="plutoTradingBot_globalBalances",v=()=>{try{let e=localStorage.getItem(C);if(e){let t=JSON.parse(e);return console.log("\uD83D\uDCE5 Global balances loaded:",t),{crypto1Balance:t.crypto1Balance||10,crypto2Balance:t.crypto2Balance||1e5,stablecoinBalance:t.stablecoinBalance||1e5,totalProfitLoss:t.totalProfitLoss||0}}}catch(e){console.error("Failed to load global balance from storage:",e)}return{crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:1e5,totalProfitLoss:0}},T=e=>{try{let t={crypto1Balance:e.crypto1Balance,crypto2Balance:e.crypto2Balance,stablecoinBalance:e.stablecoinBalance,totalProfitLoss:e.totalProfitLoss,lastUpdated:new Date().toISOString()};localStorage.setItem(C,JSON.stringify(t)),console.log("\uD83D\uDCBE Global balances saved:",t)}catch(e){console.error("Failed to save global balance to storage:",e)}},b={tradingMode:"SimpleSpot",crypto1:"",crypto2:"",baseBid:100,multiplier:1.005,numDigits:4,slippagePercent:.2,incomeSplitCrypto1Percent:50,incomeSplitCrypto2Percent:50,preferredStablecoin:a.Ql[0]},E=v(),w={config:b,targetPriceRows:[],orderHistory:[],appSettings:a.Oh,currentMarketPrice:g(b),botSystemStatus:"Stopped",crypto1Balance:E.crypto1Balance,crypto2Balance:E.crypto2Balance,stablecoinBalance:E.stablecoinBalance,backendStatus:"unknown",totalProfitLoss:E.totalProfitLoss,sessionAlarmConfig:{buyAlarmEnabled:!0,sellAlarmEnabled:!0,buyAlarmSound:"default.mp3",sellAlarmSound:"default.mp3",volume:50},telegramConfig:{enabled:!1,botToken:"",chatId:""},isTrading:!1},D=new Map,A="pluto_trading_state",P="tradingSession",B=e=>{try{let t={config:e.config,targetPriceRows:e.targetPriceRows,orderHistory:e.orderHistory,currentMarketPrice:e.currentMarketPrice,crypto1Balance:e.crypto1Balance,crypto2Balance:e.crypto2Balance,stablecoinBalance:e.stablecoinBalance,totalProfitLoss:e.totalProfitLoss,sessionAlarmConfig:e.sessionAlarmConfig,telegramConfig:e.telegramConfig,isTrading:e.isTrading,botSystemStatus:e.botSystemStatus,lastUpdated:new Date().toISOString()};localStorage.setItem(P,JSON.stringify(t))}catch(e){console.error("Failed to save session to storage:",e)}},I=e=>{try{let t={crypto1Balance:e.crypto1Balance,crypto2Balance:e.crypto2Balance,stablecoinBalance:e.stablecoinBalance,totalProfitLoss:e.totalProfitLoss,lastUpdated:new Date().toISOString()};localStorage.setItem("tradingBalances",JSON.stringify(t))}catch(e){console.error("Failed to save balance to storage:",e)}},R=e=>{try{{let t={config:e.config,targetPriceRows:e.targetPriceRows,orderHistory:e.orderHistory,appSettings:e.appSettings,currentMarketPrice:e.currentMarketPrice,crypto1Balance:e.crypto1Balance,crypto2Balance:e.crypto2Balance,stablecoinBalance:e.stablecoinBalance,botSystemStatus:e.botSystemStatus,timestamp:Date.now()};localStorage.setItem(A,JSON.stringify(t))}}catch(e){console.error("Failed to save state to localStorage:",e)}},k=()=>{try{{let e=localStorage.getItem(A);if(e){let t=JSON.parse(e);if(t.timestamp&&Date.now()-t.timestamp<864e5)return{...t,isTrading:!1,botSystemStatus:"Stopped"}}}}catch(e){console.error("Failed to load state from localStorage:",e)}return null},L=(e,t)=>{switch(t.type){case"SET_CONFIG":let o={...e.config,...t.payload};if(t.payload.crypto1||t.payload.crypto2)return{...e,config:o,currentMarketPrice:g(o)};return{...e,config:o};case"SET_TARGET_PRICE_ROWS":return{...e,targetPriceRows:t.payload.sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}))};case"ADD_TARGET_PRICE_ROW":{let o=[...e.targetPriceRows,t.payload].sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:o}}case"UPDATE_TARGET_PRICE_ROW":{let o=e.targetPriceRows.map(e=>e.id===t.payload.id?t.payload:e).sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:o}}case"REMOVE_TARGET_PRICE_ROW":{let o=e.targetPriceRows.filter(e=>e.id!==t.payload).sort((e,t)=>e.targetPrice-t.targetPrice).map((e,t)=>({...e,counter:t+1}));return{...e,targetPriceRows:o}}case"ADD_ORDER_HISTORY_ENTRY":return{...e,orderHistory:[t.payload,...e.orderHistory]};case"CLEAR_ORDER_HISTORY":return{...e,orderHistory:[]};case"SET_APP_SETTINGS":return{...e,appSettings:{...e.appSettings,...t.payload}};case"SET_MARKET_PRICE":return{...e,currentMarketPrice:t.payload};case"FLUCTUATE_MARKET_PRICE":{if(e.currentMarketPrice<=0)return e;let t=(Math.random()-.5)*.006,o=e.currentMarketPrice*(1+t);return{...e,currentMarketPrice:o>0?o:e.currentMarketPrice}}case"UPDATE_BALANCES":let n={...e,crypto1Balance:void 0!==t.payload.crypto1?t.payload.crypto1:e.crypto1Balance,crypto2Balance:void 0!==t.payload.crypto2?t.payload.crypto2:e.crypto2Balance,stablecoinBalance:void 0!==t.payload.stablecoin?t.payload.stablecoin:e.stablecoinBalance};return T(n),n;case"UPDATE_STABLECOIN_BALANCE":let r={...e,stablecoinBalance:t.payload};return T(r),r;case"RESET_SESSION":let a={...e.config},i=v();return{...w,config:a,appSettings:{...e.appSettings},currentMarketPrice:g(a),crypto1Balance:i.crypto1Balance,crypto2Balance:i.crypto2Balance,stablecoinBalance:i.stablecoinBalance,totalProfitLoss:i.totalProfitLoss};case"SET_BACKEND_STATUS":return{...e,backendStatus:t.payload};case"SYSTEM_START_BOT_INITIATE":return{...e,botSystemStatus:"WarmingUp"};case"SYSTEM_COMPLETE_WARMUP":return{...e,botSystemStatus:"Running"};case"SYSTEM_STOP_BOT":return{...e,botSystemStatus:"Stopped"};case"SYSTEM_RESET_BOT":return D.clear(),l.C.getInstance().clearCurrentSession(),{...e,botSystemStatus:"Stopped",targetPriceRows:[],orderHistory:[]};case"SET_TARGET_PRICE_ROWS":return{...e,targetPriceRows:t.payload};case"RESET_FOR_NEW_CRYPTO":let s=v();return{...w,config:e.config,backendStatus:e.backendStatus,botSystemStatus:"Stopped",currentMarketPrice:0,crypto1Balance:s.crypto1Balance,crypto2Balance:s.crypto2Balance,stablecoinBalance:s.stablecoinBalance,totalProfitLoss:s.totalProfitLoss};case"RESTORE_SESSION":return{...e,...t.payload,isTrading:!1,botSystemStatus:"Stopped"};case"SET_TELEGRAM_CONFIG":return{...e,telegramConfig:{...e.telegramConfig,...t.payload}};case"SET_SESSION_ALARM_CONFIG":return{...e,sessionAlarmConfig:{...e.sessionAlarmConfig,...t.payload}};default:return e}},O=(0,r.createContext)(void 0),_=(e,t)=>{try{let o="buy"===e?t.buyAlarmSound:t.sellAlarmSound,n="buy"===e?t.buyAlarmEnabled:t.sellAlarmEnabled;if(o&&n){let e=new Audio("/ringtones/".concat(o));e.volume=t.volume/100,e.play().catch(console.error)}}catch(e){console.error("Failed to play session alarm:",e)}},M=async(e,t)=>{try{var o;let n=await fetch("https://api.coingecko.com/api/v3/simple/price?ids=".concat(e.toLowerCase(),"&vs_currencies=").concat(t.toLowerCase())),r=await n.json(),a=null===(o=r[e.toLowerCase()])||void 0===o?void 0:o[t.toLowerCase()];if(a)return a;throw Error("Price not found")}catch(e){return console.error("Error fetching stablecoin swap price:",e),null}},F=e=>{let{children:t}=e,[o,a]=(0,r.useReducer)(L,(()=>{if("true"===new URLSearchParams(window.location.search).get("newSession")){console.log("\uD83C\uDD95 New session requested - starting with completely fresh state");let e=window.location.pathname;return window.history.replaceState({},"",e),w}if(!l.C.getInstance().getCurrentSessionId())return console.log("\uD83C\uDD95 New window detected - starting with fresh state"),w;let e=k();return e?{...w,...e}:w})()),{toast:g}=(0,s.dj)(),S=(0,r.useRef)(null),h=(0,r.useCallback)(async()=>{try{if(!o.config.crypto1||!o.config.crypto2){a({type:"SET_MARKET_PRICE",payload:0});return}let e=await y(o.config);a({type:"SET_MARKET_PRICE",payload:e})}catch(e){console.error("Failed to fetch market price:",e)}},[o.config,a]);(0,r.useEffect)(()=>{h();let e=setInterval(()=>{d.getInstance().getStatus().isOnline&&a({type:"FLUCTUATE_MARKET_PRICE"})},2e3);return()=>{clearInterval(e)}},[h,a]),(0,r.useEffect)(()=>{S.current=new Audio},[]);let f=(0,r.useCallback)(e=>{if(o.appSettings.soundAlertsEnabled&&S.current){let t;"soundOrderExecution"===e&&o.appSettings.alertOnOrderExecution?t=o.appSettings.soundOrderExecution:"soundError"===e&&o.appSettings.alertOnError&&(t=o.appSettings.soundError),t&&(S.current.src=t,S.current.currentTime=0,S.current.play().then(()=>{setTimeout(()=>{S.current&&(S.current.pause(),S.current.currentTime=0)},2e3)}).catch(e=>console.error("Error playing sound:",e)))}},[o.appSettings]),v=(0,r.useCallback)(async e=>{try{let t=localStorage.getItem("telegram_bot_token"),o=localStorage.getItem("telegram_chat_id");if(!t||!o){console.log("Telegram not configured - skipping notification");return}let n=await fetch("https://api.telegram.org/bot".concat(t,"/sendMessage"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:o,text:e,parse_mode:"HTML"})});n.ok||console.error("Failed to send Telegram notification:",n.statusText)}catch(e){console.error("Error sending Telegram notification:",e)}},[]);(0,r.useEffect)(()=>{},[o.config.crypto1,o.config.crypto2]);let b=(0,r.useCallback)(e=>{e&&Array.isArray(e)&&a({type:"SET_TARGET_PRICE_ROWS",payload:[...e].filter(e=>!isNaN(e)&&e>0).sort((e,t)=>e-t).map((e,t)=>{let n=o.targetPriceRows.find(t=>t.targetPrice===e);return n?{...n,counter:t+1}:{id:(0,i.A)(),counter:t+1,status:"Free",orderLevel:0,valueLevel:o.config.baseBid,targetPrice:e}})})},[o.targetPriceRows,o.config.baseBid,a]);(0,r.useEffect)(()=>{let e=d.getInstance().getStatus().isOnline;if("Running"!==o.botSystemStatus||0===o.targetPriceRows.length||o.currentMarketPrice<=0||!e){e||"Running"!==o.botSystemStatus||console.log("\uD83D\uDD34 Trading paused - network offline");return}let{config:t,currentMarketPrice:n,targetPriceRows:r,crypto1Balance:s,crypto2Balance:c}=o,l=[...r].sort((e,t)=>e.targetPrice-t.targetPrice),u=s,p=c,y=0;console.log("\uD83D\uDE80 CONTINUOUS TRADING: Price $".concat(n.toFixed(2)," | Targets: ").concat(l.length," | Balance: $").concat(p," ").concat(t.crypto2));let S=l.filter(e=>Math.abs(n-e.targetPrice)/n*100<=t.slippagePercent);S.length>0&&console.log("\uD83C\uDFAF TARGETS IN RANGE (\xb1".concat(t.slippagePercent,"%):"),S.map(e=>"Counter ".concat(e.counter," (").concat(e.status,")")));for(let e=0;e<l.length;e++){let o=l[e];if(Math.abs(n-o.targetPrice)/n*100<=t.slippagePercent){if("SimpleSpot"===t.tradingMode){if("Free"===o.status){let e=o.valueLevel;if(p>=e){let r=e/n;a({type:"UPDATE_TARGET_PRICE_ROW",payload:{...o,status:"Full",orderLevel:o.orderLevel+1,valueLevel:t.baseBid*Math.pow(t.multiplier,o.orderLevel+1),crypto1AmountHeld:r,originalCostCrypto2:e,crypto1Var:r,crypto2Var:-e}}),a({type:"UPDATE_BALANCES",payload:{crypto1:u+r,crypto2:p-e}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:"".concat(t.crypto1,"/").concat(t.crypto2),crypto1:t.crypto1,orderType:"BUY",amountCrypto1:r,avgPrice:n,valueCrypto2:e,price1:n,crypto1Symbol:t.crypto1||"",crypto2Symbol:t.crypto2||"",tradingMode:"SimpleSpot",type:"BUY",costBasisCrypto2:e}}),console.log("✅ BUY: Counter ".concat(o.counter," bought ").concat(r.toFixed(6)," ").concat(t.crypto1," at $").concat(n.toFixed(2))),g({title:"BUY Executed",description:"Counter ".concat(o.counter,": ").concat(r.toFixed(6)," ").concat(t.crypto1),duration:2e3}),f("soundOrderExecution"),v("\uD83D\uDFE2 <b>BUY EXECUTED</b>\n"+"\uD83D\uDCCA Counter: ".concat(o.counter,"\n")+"\uD83D\uDCB0 Amount: ".concat(r.toFixed(6)," ").concat(t.crypto1,"\n")+"\uD83D\uDCB5 Price: $".concat(n.toFixed(2),"\n")+"\uD83D\uDCB8 Cost: $".concat(e.toFixed(2)," ").concat(t.crypto2,"\n")+"\uD83D\uDCC8 Mode: Simple Spot"),y++,p-=e,u+=r}}let e=o.counter,r=l.find(t=>t.counter===e-1);if(r&&"Full"===r.status&&r.crypto1AmountHeld&&r.originalCostCrypto2){let o=r.crypto1AmountHeld,s=o*n,c=s-r.originalCostCrypto2,l=n>0?c*t.incomeSplitCrypto1Percent/100/n:0;a({type:"UPDATE_TARGET_PRICE_ROW",payload:{...r,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:t.baseBid*Math.pow(t.multiplier,r.orderLevel),crypto1Var:-o,crypto2Var:s}}),a({type:"UPDATE_BALANCES",payload:{crypto1:u-o,crypto2:p+s}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:"".concat(t.crypto1,"/").concat(t.crypto2),crypto1:t.crypto1,orderType:"SELL",amountCrypto1:o,avgPrice:n,valueCrypto2:s,price1:n,crypto1Symbol:t.crypto1||"",crypto2Symbol:t.crypto2||"",realizedProfitLossCrypto2:c,realizedProfitLossCrypto1:l,tradingMode:"SimpleSpot",type:"SELL",costBasisCrypto2:r.originalCostCrypto2,percentageGain:r.originalCostCrypto2>0?(s-r.originalCostCrypto2)/r.originalCostCrypto2*100:0}}),console.log("✅ SELL: Counter ".concat(e-1," sold ").concat(o.toFixed(6)," ").concat(t.crypto1,". Profit: $").concat(c.toFixed(2))),g({title:"SELL Executed",description:"Counter ".concat(e-1,": Profit $").concat(c.toFixed(2)),duration:2e3}),f("soundOrderExecution");let d=c>0?"\uD83D\uDCC8":c<0?"\uD83D\uDCC9":"➖";v("\uD83D\uDD34 <b>SELL EXECUTED</b>\n"+"\uD83D\uDCCA Counter: ".concat(e-1,"\n")+"\uD83D\uDCB0 Amount: ".concat(o.toFixed(6)," ").concat(t.crypto1,"\n")+"\uD83D\uDCB5 Price: $".concat(n.toFixed(2),"\n")+"\uD83D\uDCB8 Received: $".concat(s.toFixed(2)," ").concat(t.crypto2,"\n")+"".concat(d," Profit: $").concat(c.toFixed(2)," ").concat(t.crypto2,"\n")+"\uD83D\uDCC8 Mode: Simple Spot"),y++,u-=o,p+=s}}else if("StablecoinSwap"===t.tradingMode){if("Free"===o.status){let e=o.valueLevel;if(p>=e){let r=m(t.crypto2||"USDT")/m(t.preferredStablecoin||"USDT"),s=e*r,c=m(t.crypto1||"BTC")/m(t.preferredStablecoin||"USDT"),l=s/c,d=o.orderLevel+1,S=t.baseBid*Math.pow(t.multiplier,d);a({type:"UPDATE_TARGET_PRICE_ROW",payload:{...o,status:"Full",orderLevel:d,valueLevel:S,crypto1AmountHeld:l,originalCostCrypto2:e,crypto1Var:l,crypto2Var:-e}}),a({type:"UPDATE_BALANCES",payload:{crypto1:u+l,crypto2:p-e}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:"".concat(t.crypto2,"/").concat(t.preferredStablecoin),crypto1:t.crypto2,orderType:"SELL",amountCrypto1:e,avgPrice:r,valueCrypto2:s,price1:r,crypto1Symbol:t.crypto2||"",crypto2Symbol:t.preferredStablecoin||"",tradingMode:"StablecoinSwap",type:"BUY",intermediateStablecoinAmount:s,stablecoinPrice:r}});let h=l*(n||c),C=h-e,T=c>0?C*t.incomeSplitCrypto1Percent/100/c:0;a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:"".concat(t.crypto1,"/").concat(t.preferredStablecoin),crypto1:t.crypto1,orderType:"BUY",amountCrypto1:l,avgPrice:c,valueCrypto2:s,price1:c,crypto1Symbol:t.crypto1||"",crypto2Symbol:t.preferredStablecoin||"",tradingMode:"StablecoinSwap",type:"BUY",costBasisCrypto2:e,intermediateStablecoinAmount:s,stablecoinPrice:c,potentialProfitLossCrypto2:C,potentialProfitLossCrypto1:T,currentMarketValueCrypto2:h}}),console.log("✅ STABLECOIN BUY: Counter ".concat(o.counter," | Step 1: Sold ").concat(e," ").concat(t.crypto2," → ").concat(s.toFixed(2)," ").concat(t.preferredStablecoin," | Step 2: Bought ").concat(l.toFixed(6)," ").concat(t.crypto1," | Level: ").concat(o.orderLevel," → ").concat(d)),g({title:"BUY Executed (Stablecoin)",description:"Counter ".concat(o.counter,": ").concat(l.toFixed(6)," ").concat(t.crypto1," via ").concat(t.preferredStablecoin),duration:2e3}),f("soundOrderExecution"),v("\uD83D\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\n"+"\uD83D\uDCCA Counter: ".concat(o.counter,"\n")+"\uD83D\uDD04 Step 1: Sold ".concat(e.toFixed(2)," ").concat(t.crypto2," → ").concat(s.toFixed(2)," ").concat(t.preferredStablecoin,"\n")+"\uD83D\uDD04 Step 2: Bought ".concat(l.toFixed(6)," ").concat(t.crypto1,"\n")+"\uD83D\uDCCA Level: ".concat(o.orderLevel," → ").concat(d,"\n")+"\uD83D\uDCC8 Mode: Stablecoin Swap"),y++,p-=e,u+=l}}let e=o.counter,r=l.find(t=>t.counter===e-1);if(r&&"Full"===r.status&&r.crypto1AmountHeld&&r.originalCostCrypto2){let o=r.crypto1AmountHeld,n=m(t.crypto1||"BTC")/m(t.preferredStablecoin||"USDT"),s=o*n,c=m(t.crypto2||"USDT")/m(t.preferredStablecoin||"USDT"),l=s/c,d=r.originalCostCrypto2||0,S=l-d,h=d>0?(l-d)/d*100:0,C=n>0?S*t.incomeSplitCrypto1Percent/100/n:0;a({type:"UPDATE_TARGET_PRICE_ROW",payload:{...r,status:"Free",crypto1AmountHeld:void 0,originalCostCrypto2:void 0,valueLevel:t.baseBid*Math.pow(t.multiplier,r.orderLevel),crypto1Var:0,crypto2Var:0}}),a({type:"UPDATE_BALANCES",payload:{crypto1:u-o,crypto2:p+l}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:"".concat(t.crypto1,"/").concat(t.preferredStablecoin),crypto1:t.crypto1,orderType:"SELL",amountCrypto1:o,avgPrice:n,valueCrypto2:s,price1:n,crypto1Symbol:t.crypto1||"",crypto2Symbol:t.preferredStablecoin||"",tradingMode:"StablecoinSwap",type:"SELL",intermediateStablecoinAmount:s,stablecoinPrice:n}}),a({type:"ADD_ORDER_HISTORY_ENTRY",payload:{id:(0,i.A)(),timestamp:Date.now(),pair:"".concat(t.crypto2,"/").concat(t.preferredStablecoin),crypto1:t.crypto2,orderType:"BUY",amountCrypto1:l,avgPrice:c,valueCrypto2:s,price1:c,crypto1Symbol:t.crypto2||"",crypto2Symbol:t.preferredStablecoin||"",realizedProfitLossCrypto2:S,realizedProfitLossCrypto1:C,tradingMode:"StablecoinSwap",type:"SELL",costBasisCrypto2:d,percentageGain:h,intermediateStablecoinAmount:s,stablecoinPrice:c,stepAAmount:o,stepAPrice:n,stepBAmount:l,stepBPrice:c,twoStepProcess:!0}}),console.log("✅ STABLECOIN SELL: Counter ".concat(e-1," | Step A: Sold ").concat(o.toFixed(6)," ").concat(t.crypto1," → ").concat(s.toFixed(2)," ").concat(t.preferredStablecoin," | Step B: Bought ").concat(l.toFixed(2)," ").concat(t.crypto2," | Profit: ").concat(S.toFixed(2)," ").concat(t.crypto2," | Level: ").concat(r.orderLevel," (unchanged)")),g({title:"SELL Executed (Stablecoin)",description:"Counter ".concat(e-1,": Profit ").concat(S.toFixed(2)," ").concat(t.crypto2," via ").concat(t.preferredStablecoin),duration:2e3}),f("soundOrderExecution");let T=S>0?"\uD83D\uDCC8":S<0?"\uD83D\uDCC9":"➖";v("\uD83D\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\n"+"\uD83D\uDCCA Counter: ".concat(e-1,"\n")+"\uD83D\uDD04 Step A: Sold ".concat(o.toFixed(6)," ").concat(t.crypto1," → ").concat(s.toFixed(2)," ").concat(t.preferredStablecoin,"\n")+"\uD83D\uDD04 Step B: Bought ".concat(l.toFixed(2)," ").concat(t.crypto2,"\n")+"".concat(T," Profit: ").concat(S.toFixed(2)," ").concat(t.crypto2,"\n")+"\uD83D\uDCCA Level: ".concat(r.orderLevel," (unchanged)\n")+"\uD83D\uDCC8 Mode: Stablecoin Swap"),y++,u-=o,p+=l}}}}y>0&&console.log("\uD83C\uDFAF CYCLE COMPLETE: ".concat(y," actions taken at price $").concat(n.toFixed(2)))},[o.botSystemStatus,o.currentMarketPrice,o.targetPriceRows,o.config,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,a,g,f,v]);let E=(0,r.useCallback)(()=>o.targetPriceRows&&Array.isArray(o.targetPriceRows)?o.targetPriceRows.map(e=>{let t,n;let r=o.currentMarketPrice||0,a=e.targetPrice||0,i=r&&a?(r/a-1)*100:0;if("Full"===e.status&&e.crypto1AmountHeld&&e.originalCostCrypto2){let a=r*e.crypto1AmountHeld-e.originalCostCrypto2;if("StablecoinSwap"===o.config.tradingMode){let e=i>=0?1:-1,s=Math.abs(a);n=e*(s*o.config.incomeSplitCrypto2Percent)/100,r>0&&(t=e*(s*o.config.incomeSplitCrypto1Percent/100)/r)}else n=a*o.config.incomeSplitCrypto2Percent/100,r>0&&(t=a*o.config.incomeSplitCrypto1Percent/100/r)}return{...e,currentPrice:r,priceDifference:a-r,priceDifferencePercent:r>0?(a-r)/r*100:0,potentialProfitCrypto1:o.config.incomeSplitCrypto1Percent/100*e.valueLevel/(a||1),potentialProfitCrypto2:o.config.incomeSplitCrypto2Percent/100*e.valueLevel,percentFromActualPrice:i,incomeCrypto1:t,incomeCrypto2:n}}).sort((e,t)=>t.targetPrice-e.targetPrice):[],[o.targetPriceRows,o.currentMarketPrice,o.config.incomeSplitCrypto1Percent,o.config.incomeSplitCrypto2Percent,o.config.baseBid,o.config.multiplier]),D=(0,r.useCallback)(async e=>{try{var t;let n={name:"".concat(e.crypto1,"/").concat(e.crypto2," ").concat(e.tradingMode),tradingMode:e.tradingMode,crypto1:e.crypto1,crypto2:e.crypto2,baseBid:e.baseBid,multiplier:e.multiplier,numDigits:e.numDigits,slippagePercent:e.slippagePercent,incomeSplitCrypto1Percent:e.incomeSplitCrypto1Percent,incomeSplitCrypto2Percent:e.incomeSplitCrypto2Percent,preferredStablecoin:e.preferredStablecoin,targetPrices:o.targetPriceRows.map(e=>e.targetPrice)},r=await c.oc.saveConfig(n);return console.log("✅ Config saved to backend:",r),(null===(t=r.config)||void 0===t?void 0:t.id)||null}catch(e){return console.error("❌ Failed to save config to backend:",e),g({title:"Backend Error",description:"Failed to save configuration to backend",variant:"destructive",duration:3e3}),null}},[o.targetPriceRows,g]),A=(0,r.useCallback)(async e=>{try{let t=await c.oc.startBot(e);return console.log("✅ Bot started on backend:",t),g({title:"Bot Started",description:"Trading bot started successfully on backend",duration:3e3}),!0}catch(e){return console.error("❌ Failed to start bot on backend:",e),g({title:"Backend Error",description:"Failed to start bot on backend",variant:"destructive",duration:3e3}),!1}},[g]),F=(0,r.useCallback)(async e=>{try{let t=await c.oc.stopBot(e);return console.log("✅ Bot stopped on backend:",t),g({title:"Bot Stopped",description:"Trading bot stopped successfully on backend",duration:3e3}),!0}catch(e){return console.error("❌ Failed to stop bot on backend:",e),g({title:"Backend Error",description:"Failed to stop bot on backend",variant:"destructive",duration:3e3}),!1}},[g]),N=(0,r.useCallback)(async()=>{let e="http://localhost:5000";if(!e){console.error("Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed."),a({type:"SET_BACKEND_STATUS",payload:"offline"});return}try{let t=await fetch("".concat(e,"/health/"));if(!t.ok){console.error("Backend health check failed with status: ".concat(t.status," ").concat(t.statusText));let e=await t.text().catch(()=>"Could not read response text.");console.error("Backend health check response body:",e)}a({type:"SET_BACKEND_STATUS",payload:t.ok?"online":"offline"})}catch(t){a({type:"SET_BACKEND_STATUS",payload:"offline"}),console.error("Backend connectivity check failed. Error details:",t),t.cause&&console.error("Fetch error cause:",t.cause),console.error("Attempted to fetch API URL:","".concat(e,"/health/"))}},[a]);(0,r.useEffect)(()=>{N()},[N]),(0,r.useEffect)(()=>{R(o)},[o]),(0,r.useEffect)(()=>{"WarmingUp"===o.botSystemStatus&&(console.log("Bot is Warming Up... Immediate execution enabled."),a({type:"SYSTEM_COMPLETE_WARMUP"}),console.log("Bot is now Running immediately."))},[o.botSystemStatus,a]),(0,r.useEffect)(()=>{let e=l.C.getInstance(),t=e.getCurrentSessionId();t&&("Running"===o.botSystemStatus?(e.startSessionRuntime(t),console.log("✅ Started runtime tracking for session:",t)):"Stopped"===o.botSystemStatus&&(e.stopSessionRuntime(t),console.log("⏹️ Stopped runtime tracking for session:",t)))},[o.botSystemStatus]),(0,r.useEffect)(()=>{let e=l.C.getInstance();"WarmingUp"===o.botSystemStatus&&!e.getCurrentSessionId()&&o.config.crypto1&&o.config.crypto2&&o.targetPriceRows.length>0&&e.createNewSessionWithAutoName(o.config).then(t=>{e.setCurrentSession(t),console.log("✅ Auto-created session:",t)}).catch(e=>{console.error("❌ Failed to auto-create session:",e)});let t=e.getCurrentSessionId();t&&("Running"===o.botSystemStatus?(e.startSessionRuntime(t),console.log("⏱️ Started runtime tracking for session:",t)):"Stopped"===o.botSystemStatus&&(e.stopSessionRuntime(t),console.log("⏹️ Stopped runtime tracking for session:",t)))},[o.botSystemStatus,o.config.crypto1,o.config.crypto2]),(0,r.useEffect)(()=>{let e=l.C.getInstance(),t=e.getCurrentSessionId();if(t&&"Running"===o.botSystemStatus){let n=e.loadSession(t);if(n&&(n.config.crypto1!==o.config.crypto1||n.config.crypto2!==o.config.crypto2)){if(console.log("\uD83D\uDD04 Crypto pair changed during active trading, auto-saving and resetting..."),"Running"===o.botSystemStatus||o.targetPriceRows.length>0||o.orderHistory.length>0){"Running"===o.botSystemStatus&&F("default");let t=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),r="".concat(n.name," (AutoSaved ").concat(t,")");e.createNewSession(r,n.config).then(t=>{e.saveSession(t,n.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!1),console.log("\uD83D\uDCBE AutoSaved session:",r)})}a({type:"RESET_FOR_NEW_CRYPTO"}),o.config.crypto1&&o.config.crypto2&&e.createNewSessionWithAutoName(o.config).then(t=>{e.setCurrentSession(t),console.log("\uD83C\uDD95 Created new session for crypto pair during active trading:",o.config.crypto1,"/",o.config.crypto2),g({title:"Crypto Pair Changed During Trading",description:"Previous session AutoSaved. New session created for ".concat(o.config.crypto1,"/").concat(o.config.crypto2),duration:5e3})})}}},[o.config.crypto1,o.config.crypto2,o.botSystemStatus]),(0,r.useEffect)(()=>{let e=d.getInstance(),t=u.getInstance(),n=p.getInstance(),r=l.C.getInstance(),i=e.addListener((e,t)=>{if(console.log("\uD83C\uDF10 Network status changed: ".concat(e?"Online":"Offline")),e||t)e&&!t&&g({title:"Network Reconnected",description:"Connection restored. You can resume trading.",duration:3e3});else{if("Running"===o.botSystemStatus){console.log("\uD83D\uDD34 Internet lost - stopping bot and saving session"),a({type:"SYSTEM_STOP_BOT"});let e=l.C.getInstance(),t=e.getCurrentSessionId();if(t){let n=e.loadSession(t);if(n){let t=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1}),r="".concat(n.name," (Offline Backup ").concat(t,")");e.createNewSession(r,n.config).then(t=>{e.saveSession(t,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,!1),console.log("\uD83D\uDCBE Created offline backup session:",r)})}}}g({title:"Network Disconnected",description:"Bot stopped and session saved. Trading paused until connection restored.",variant:"destructive",duration:8e3})}}),s=n.addListener(e=>{let t=e.usedJSHeapSize/1024/1024;t>150&&(console.warn("\uD83E\uDDE0 High memory usage: ".concat(t.toFixed(2),"MB")),g({title:"High Memory Usage",description:"Memory usage is high (".concat(t.toFixed(0),"MB). Consider refreshing the page."),variant:"destructive",duration:5e3}))}),c=()=>{try{let e=r.getCurrentSessionId();e&&r.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,"Running"===o.botSystemStatus),R(o)}catch(e){console.error("Auto-save failed:",e)}};t.enable(c,3e4);let y=e=>{if(c(),"Running"===o.botSystemStatus){let t="Trading bot is currently running. Are you sure you want to leave?";return e.returnValue=t,t}};return window.addEventListener("beforeunload",y),()=>{i(),s(),t.disable(),window.removeEventListener("beforeunload",y)}},[o,g]),(0,r.useEffect)(()=>{u.getInstance().saveNow()},[o.botSystemStatus]),(0,r.useEffect)(()=>{let e=localStorage.getItem(P);if(e)try{let t=JSON.parse(e);a({type:"RESTORE_SESSION",payload:t}),console.log("\uD83D\uDD04 Session restored from localStorage - manual bot start required")}catch(e){console.error("Failed to restore session:",e)}},[]),(0,r.useEffect)(()=>{let e=setInterval(()=>{o.isTrading&&B(o)},3e4);return()=>clearInterval(e)},[o.isTrading,o]),(0,r.useEffect)(()=>{I(o),T(o)},[o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,o.totalProfitLoss]);let U=(0,r.useCallback)(()=>{try{let e=l.C.getInstance(),t=e.getCurrentSessionId();if(!t){if(o.config.crypto1&&o.config.crypto2)return e.createNewSessionWithAutoName(o.config).then(t=>{e.setCurrentSession(t),e.saveSession(t,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,"Running"===o.botSystemStatus)}),!0;return!1}return e.saveSession(t,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,"Running"===o.botSystemStatus)}catch(e){return console.error("Failed to save current session:",e),!1}},[o]),x=(0,r.useCallback)(async(e,t)=>{await v(e,t,o.telegramConfig)},[o.telegramConfig]),H=(0,r.useCallback)(()=>{let e={crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0,totalProfitLoss:0};return a({type:"UPDATE_BALANCES",payload:{crypto1:e.crypto1Balance,crypto2:e.crypto2Balance,stablecoin:e.stablecoinBalance}}),localStorage.removeItem(C),console.log("\uD83D\uDD04 Global balances reset to defaults"),e},[]),Y=(0,r.useCallback)(e=>{_(e,o.sessionAlarmConfig)},[o.sessionAlarmConfig]),G=(0,r.useCallback)(async(e,t)=>await M(e,t),[]),W=(0,r.useCallback)(()=>"StablecoinSwap"===o.config.tradingMode?o.orderHistory.filter(e=>("SELL"===e.type||"SELL"===e.orderType)&&"StablecoinSwap"===e.tradingMode&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0):o.orderHistory.filter(e=>("SELL"===e.type||"SELL"===e.orderType)&&("SimpleSpot"===e.tradingMode||!e.tradingMode)&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0),[o.orderHistory,o.config.tradingMode]),J={...o,dispatch:a,setTargetPrices:b,getDisplayOrders:E,checkBackendStatus:N,fetchMarketPrice:h,startBackendBot:A,stopBackendBot:F,saveConfigToBackend:D,saveCurrentSession:U,sendTelegramNotification:x,playSessionAlarm:Y,fetchStablecoinSwapPrice:G,calculateTotalPL:W,resetGlobalBalances:H,backendStatus:o.backendStatus,botSystemStatus:o.botSystemStatus,isBotActive:"Running"===o.botSystemStatus};return(0,n.jsx)(O.Provider,{value:J,children:t})},N=()=>{let e=(0,r.useContext)(O);if(void 0===e)throw Error("useTradingContext must be used within a TradingProvider");return e}},84553:(e,t,o)=>{o.d(t,{C:()=>l});var n=o(79737),r=o(25731);let a="pluto_trading_sessions",i="pluto_current_session",s=()=>"window_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),c=()=>{let e=sessionStorage.getItem("pluto_window_id");return e||(e=s(),sessionStorage.setItem("pluto_window_id",e)),e};class l{static getInstance(){return l.instance||(l.instance=new l),l.instance}generateSessionName(e){let t=e.crypto1||"Crypto1",o=e.crypto2||"Crypto2",n=e.tradingMode||"SimpleSpot",r="".concat(t,"/").concat(o," ").concat(n),a=Array.from(this.sessions.values()).filter(e=>e.name.startsWith(r));if(0===a.length)return r;let i=0;return a.forEach(e=>{let t=e.name.match(new RegExp("^".concat(r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")," Session (\\d+)$")));if(t){let e=parseInt(t[1],10);e>i&&(i=e)}else e.name===r&&(i=Math.max(i,1))}),"".concat(r," Session ").concat(i+1)}async checkBackendConnection(){try{let e=new AbortController,t=setTimeout(()=>e.abort(),1500),o=await fetch("http://localhost:5000/",{method:"GET",signal:e.signal});if(clearTimeout(t),o.status<500)this.useBackend=!0,console.log("✅ Session Manager: Backend connection established");else throw Error("Backend returned server error")}catch(e){this.useBackend=!1,console.warn("⚠️ Session Manager: Backend unavailable, using localStorage fallback"),console.warn("\uD83D\uDCA1 To enable backend features, start the backend server: python run.py")}}getWindowSpecificKey(e){return"".concat(e,"_").concat(this.windowId)}setupStorageListener(){window.addEventListener("storage",e=>{if(e.key===a&&e.newValue)try{let t=JSON.parse(e.newValue);this.sessions=new Map(Object.entries(t)),console.log("\uD83D\uDD04 Sessions synced from another window (".concat(this.sessions.size," sessions)"))}catch(e){console.error("Failed to sync sessions from storage event:",e)}})}loadSessionsFromStorage(){try{let e=localStorage.getItem(a),t=this.getWindowSpecificKey(i),o=localStorage.getItem(t);if(e){let t=JSON.parse(e);this.sessions=new Map(Object.entries(t))}this.currentSessionId=o,console.log("\uD83D\uDCC2 Loaded ".concat(this.sessions.size," shared sessions for window ").concat(this.windowId))}catch(e){console.error("Failed to load sessions from storage:",e)}}saveSessionsToStorage(){try{let e=Object.fromEntries(this.sessions);localStorage.setItem(a,JSON.stringify(e));let t=this.getWindowSpecificKey(i);this.currentSessionId&&localStorage.setItem(t,this.currentSessionId)}catch(e){console.error("Failed to save sessions to storage:",e)}}async createNewSessionWithAutoName(e,t){let o=t||this.generateSessionName(e);return this.createNewSession(o,e)}async createNewSession(e,t){if(this.useBackend)try{let o=(await r.Rk.createSession({name:e,config:t,targetPriceRows:[],currentMarketPrice:0,crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0})).session.id;return console.log("✅ Session created on backend:",o),o}catch(e){console.error("❌ Failed to create session on backend, falling back to localStorage:",e),this.useBackend=!1}let o=(0,n.A)(),a=Date.now();return this.sessions.set(o,{id:o,name:e,config:t,targetPriceRows:[],orderHistory:[],currentMarketPrice:0,crypto1Balance:10,crypto2Balance:1e5,stablecoinBalance:0,createdAt:a,lastModified:a,isActive:!1,runtime:0}),this.saveSessionsToStorage(),o}saveSession(e,t,o,n,r,a,i,s){let c=arguments.length>8&&void 0!==arguments[8]&&arguments[8],l=arguments.length>9?arguments[9]:void 0;try{let d;let u=this.sessions.get(e);if(!u)return console.error("Session not found:",e),!1;if(void 0!==l)d=l,console.log("\uD83D\uDCCA Using override runtime: ".concat(d,"ms for session ").concat(e));else{d=u.runtime;let t=this.sessionStartTimes.get(e);t&&c?(d=u.runtime+(Date.now()-t),this.sessionStartTimes.set(e,Date.now())):!c&&t?(d=u.runtime+(Date.now()-t),this.sessionStartTimes.delete(e)):c&&!t&&this.sessionStartTimes.set(e,Date.now())}let p={...u,config:t,targetPriceRows:[...o],orderHistory:[...n],currentMarketPrice:r,crypto1Balance:a,crypto2Balance:i,stablecoinBalance:s,isActive:c,lastModified:Date.now(),runtime:d};return this.sessions.set(e,p),this.saveSessionsToStorage(),!0}catch(e){return console.error("Failed to save session:",e),!1}}loadSession(e){return this.sessions.get(e)||null}deleteSession(e){let t=this.sessions.delete(e);if(t){if(this.currentSessionId===e){this.currentSessionId=null;let e=this.getWindowSpecificKey(i);localStorage.removeItem(e)}this.saveSessionsToStorage()}return t}getAllSessions(){return Array.from(this.sessions.values()).map(e=>({id:e.id,name:e.name,pair:"".concat(e.config.crypto1,"/").concat(e.config.crypto2),createdAt:e.createdAt,lastModified:e.lastModified,isActive:e.isActive,runtime:this.getCurrentRuntime(e.id),totalTrades:e.orderHistory.length,totalProfitLoss:e.orderHistory.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0)}))}getFilteredSessions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5e3;return this.getAllSessions().filter(t=>!!t.isActive||t.runtime>=e)}setCurrentSession(e){if(this.sessions.has(e)){this.currentSessionId=e;let t=this.getWindowSpecificKey(i);localStorage.setItem(t,e);let o=this.sessions.get(e);o&&!o.isActive&&(o.isActive=!0,o.lastModified=Date.now(),this.sessions.set(e,o),this.saveSessionsToStorage(),console.log("✅ Session ".concat(e," marked as active for window ").concat(this.windowId)))}}getCurrentSessionId(){return this.currentSessionId}clearCurrentSession(){if(this.currentSessionId){let e=this.sessions.get(this.currentSessionId);e&&e.isActive&&(e.isActive=!1,e.lastModified=Date.now(),this.sessions.set(this.currentSessionId,e),this.saveSessionsToStorage(),console.log("⏹️ Session ".concat(this.currentSessionId," marked as inactive for window ").concat(this.windowId)))}this.currentSessionId=null;{let e=this.getWindowSpecificKey(i);localStorage.removeItem(e)}console.log("\uD83D\uDDD1️ Cleared current session for window ".concat(this.windowId))}startSessionRuntime(e){this.sessionStartTimes.set(e,Date.now())}stopSessionRuntime(e){let t=this.sessionStartTimes.get(e);if(t){let o=this.sessions.get(e);if(o){let n=Date.now()-t;o.runtime+=n,o.lastModified=Date.now(),this.sessions.set(e,o),this.saveSessionsToStorage()}this.sessionStartTimes.delete(e)}}deactivateSession(e){let t=this.sessions.get(e);t&&t.isActive&&(t.isActive=!1,t.lastModified=Date.now(),this.sessions.set(e,t),this.saveSessionsToStorage(),console.log("⏹️ Session ".concat(e," deactivated")))}getCurrentRuntime(e){let t=this.sessions.get(e);if(!t)return 0;let o=this.sessionStartTimes.get(e);return o?t.runtime+(Date.now()-o):t.runtime}exportSessionToJSON(e){let t=this.sessions.get(e);return t?JSON.stringify(t,null,2):null}importSessionFromJSON(e){try{let t=JSON.parse(e),o=(0,n.A)(),r={...t,id:o,isActive:!1,lastModified:Date.now()};return this.sessions.set(o,r),this.saveSessionsToStorage(),o}catch(e){return console.error("Failed to import session:",e),null}}renameSession(e,t){let o=this.sessions.get(e);return!!o&&(o.name=t,o.lastModified=Date.now(),this.sessions.set(e,o),this.saveSessionsToStorage(),!0)}getSessionHistory(e){let t=this.sessions.get(e);return t?[...t.orderHistory]:[]}exportSessionToCSV(e){let t=this.sessions.get(e);return t?["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...t.orderHistory.map(e=>{var o,n,r,a,i,s,c;return[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,(null===(o=e.amountCrypto1)||void 0===o?void 0:o.toFixed(t.config.numDigits))||"",(null===(n=e.avgPrice)||void 0===n?void 0:n.toFixed(t.config.numDigits))||"",(null===(r=e.valueCrypto2)||void 0===r?void 0:r.toFixed(t.config.numDigits))||"",(null===(a=e.price1)||void 0===a?void 0:a.toFixed(t.config.numDigits))||"",e.crypto1Symbol,(null===(i=e.price2)||void 0===i?void 0:i.toFixed(t.config.numDigits))||"",e.crypto2Symbol,(null===(s=e.realizedProfitLossCrypto1)||void 0===s?void 0:s.toFixed(t.config.numDigits))||"",(null===(c=e.realizedProfitLossCrypto2)||void 0===c?void 0:c.toFixed(t.config.numDigits))||""].join(",")})].join("\n"):null}clearAllSessions(){this.sessions.clear(),this.currentSessionId=null,localStorage.removeItem(a);let e=this.getWindowSpecificKey(i);localStorage.removeItem(e)}enableAutoSave(e,t){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e4,n=setInterval(()=>{let o=t();this.saveSession(e,o.config,o.targetPriceRows,o.orderHistory,o.currentMarketPrice,o.crypto1Balance,o.crypto2Balance,o.stablecoinBalance,o.isActive)},o);return()=>clearInterval(n)}constructor(){this.sessions=new Map,this.currentSessionId=null,this.useBackend=!0,this.sessionStartTimes=new Map,this.windowId=c(),console.log("\uD83E\uDE9F SessionManager initialized for window: ".concat(this.windowId)),this.loadSessionsFromStorage(),this.sessionStartTimes.clear(),this.useBackend=!1,this.loadSessionsFromStorage(),this.setupStorageListener(),setTimeout(()=>{this.checkBackendConnection().catch(()=>{})},1e3),console.log("\uD83E\uDE9F SessionManager initialized for window ".concat(this.windowId))}}},87481:(e,t,o)=>{o.d(t,{dj:()=>p});var n=o(12115);let r=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},s=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:o}=t;return o?i(o):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===o||void 0===o?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],l={toasts:[]};function d(e){l=s(l,e),c.forEach(e=>{e(l)})}function u(e){let{...t}=e,o=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>d({type:"DISMISS_TOAST",toastId:o});return d({type:"ADD_TOAST",toast:{...t,id:o,open:!0,onOpenChange:e=>{e||n()}}}),{id:o,dismiss:n,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:o}})}}function p(){let[e,t]=n.useState(l);return n.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}}}]);