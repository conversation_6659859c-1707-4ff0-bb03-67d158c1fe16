/**
 * Comprehensive Trading Pairs Configuration
 * Supports 50+ trading pairs for both SimpleSpot and StablecoinSwap modes
 */

export interface TradingPair {
  id: string;
  crypto1: string;
  crypto2: string;
  displayName: string;
  category: 'major' | 'altcoin' | 'defi' | 'meme' | 'stablecoin';
  minTradeAmount: number;
  maxTradeAmount: number;
  priceDecimals: number;
  amountDecimals: number;
  description: string;
  supportedModes: ('SimpleSpot' | 'StablecoinSwap')[];
}

export const TRADING_PAIRS: TradingPair[] = [
  // Major Cryptocurrencies
  {
    id: 'BTC_USDT',
    crypto1: 'BTC',
    crypto2: 'USDT',
    displayName: 'Bitcoin / Tether',
    category: 'major',
    minTradeAmount: 0.0001,
    maxTradeAmount: 10,
    priceDecimals: 2,
    amountDecimals: 6,
    description: 'The most popular cryptocurrency pair',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'ETH_USDT',
    crypto1: 'ETH',
    crypto2: 'USDT',
    displayName: 'Ethereum / Tether',
    category: 'major',
    minTradeAmount: 0.001,
    maxTradeAmount: 100,
    priceDecimals: 2,
    amountDecimals: 6,
    description: 'Second largest cryptocurrency by market cap',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'BNB_USDT',
    crypto1: 'BNB',
    crypto2: 'USDT',
    displayName: 'Binance Coin / Tether',
    category: 'major',
    minTradeAmount: 0.01,
    maxTradeAmount: 1000,
    priceDecimals: 2,
    amountDecimals: 4,
    description: 'Binance exchange native token',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'ADA_USDT',
    crypto1: 'ADA',
    crypto2: 'USDT',
    displayName: 'Cardano / Tether',
    category: 'major',
    minTradeAmount: 1,
    maxTradeAmount: 100000,
    priceDecimals: 4,
    amountDecimals: 2,
    description: 'Proof-of-stake blockchain platform',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'SOL_USDT',
    crypto1: 'SOL',
    crypto2: 'USDT',
    displayName: 'Solana / Tether',
    category: 'major',
    minTradeAmount: 0.01,
    maxTradeAmount: 1000,
    priceDecimals: 2,
    amountDecimals: 4,
    description: 'High-performance blockchain for DeFi and NFTs',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },

  // Altcoins
  {
    id: 'DOT_USDT',
    crypto1: 'DOT',
    crypto2: 'USDT',
    displayName: 'Polkadot / Tether',
    category: 'altcoin',
    minTradeAmount: 0.1,
    maxTradeAmount: 10000,
    priceDecimals: 3,
    amountDecimals: 3,
    description: 'Multi-chain interoperability protocol',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'LINK_USDT',
    crypto1: 'LINK',
    crypto2: 'USDT',
    displayName: 'Chainlink / Tether',
    category: 'altcoin',
    minTradeAmount: 0.1,
    maxTradeAmount: 5000,
    priceDecimals: 3,
    amountDecimals: 3,
    description: 'Decentralized oracle network',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'MATIC_USDT',
    crypto1: 'MATIC',
    crypto2: 'USDT',
    displayName: 'Polygon / Tether',
    category: 'altcoin',
    minTradeAmount: 1,
    maxTradeAmount: 50000,
    priceDecimals: 4,
    amountDecimals: 2,
    description: 'Ethereum scaling solution',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'AVAX_USDT',
    crypto1: 'AVAX',
    crypto2: 'USDT',
    displayName: 'Avalanche / Tether',
    category: 'altcoin',
    minTradeAmount: 0.01,
    maxTradeAmount: 1000,
    priceDecimals: 2,
    amountDecimals: 4,
    description: 'Fast and eco-friendly blockchain platform',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'ATOM_USDT',
    crypto1: 'ATOM',
    crypto2: 'USDT',
    displayName: 'Cosmos / Tether',
    category: 'altcoin',
    minTradeAmount: 0.1,
    maxTradeAmount: 5000,
    priceDecimals: 3,
    amountDecimals: 3,
    description: 'Internet of blockchains',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },

  // DeFi Tokens
  {
    id: 'UNI_USDT',
    crypto1: 'UNI',
    crypto2: 'USDT',
    displayName: 'Uniswap / Tether',
    category: 'defi',
    minTradeAmount: 0.1,
    maxTradeAmount: 5000,
    priceDecimals: 3,
    amountDecimals: 3,
    description: 'Leading decentralized exchange token',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'AAVE_USDT',
    crypto1: 'AAVE',
    crypto2: 'USDT',
    displayName: 'Aave / Tether',
    category: 'defi',
    minTradeAmount: 0.01,
    maxTradeAmount: 1000,
    priceDecimals: 2,
    amountDecimals: 4,
    description: 'Decentralized lending protocol',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'COMP_USDT',
    crypto1: 'COMP',
    crypto2: 'USDT',
    displayName: 'Compound / Tether',
    category: 'defi',
    minTradeAmount: 0.01,
    maxTradeAmount: 1000,
    priceDecimals: 2,
    amountDecimals: 4,
    description: 'Algorithmic money market protocol',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'SUSHI_USDT',
    crypto1: 'SUSHI',
    crypto2: 'USDT',
    displayName: 'SushiSwap / Tether',
    category: 'defi',
    minTradeAmount: 0.1,
    maxTradeAmount: 10000,
    priceDecimals: 4,
    amountDecimals: 3,
    description: 'Community-driven DEX and DeFi platform',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'CRV_USDT',
    crypto1: 'CRV',
    crypto2: 'USDT',
    displayName: 'Curve DAO / Tether',
    category: 'defi',
    minTradeAmount: 1,
    maxTradeAmount: 50000,
    priceDecimals: 4,
    amountDecimals: 2,
    description: 'Decentralized exchange for stablecoins',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },

  // Meme Coins
  {
    id: 'DOGE_USDT',
    crypto1: 'DOGE',
    crypto2: 'USDT',
    displayName: 'Dogecoin / Tether',
    category: 'meme',
    minTradeAmount: 10,
    maxTradeAmount: 1000000,
    priceDecimals: 6,
    amountDecimals: 0,
    description: 'The original meme cryptocurrency',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'SHIB_USDT',
    crypto1: 'SHIB',
    crypto2: 'USDT',
    displayName: 'Shiba Inu / Tether',
    category: 'meme',
    minTradeAmount: 100000,
    maxTradeAmount: 100000000,
    priceDecimals: 8,
    amountDecimals: 0,
    description: 'Dogecoin killer meme token',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },

  // Stablecoin Pairs
  {
    id: 'USDC_USDT',
    crypto1: 'USDC',
    crypto2: 'USDT',
    displayName: 'USD Coin / Tether',
    category: 'stablecoin',
    minTradeAmount: 1,
    maxTradeAmount: 1000000,
    priceDecimals: 6,
    amountDecimals: 2,
    description: 'Stablecoin arbitrage pair',
    supportedModes: ['StablecoinSwap']
  },
  {
    id: 'DAI_USDT',
    crypto1: 'DAI',
    crypto2: 'USDT',
    displayName: 'Dai / Tether',
    category: 'stablecoin',
    minTradeAmount: 1,
    maxTradeAmount: 1000000,
    priceDecimals: 6,
    amountDecimals: 2,
    description: 'Decentralized stablecoin pair',
    supportedModes: ['StablecoinSwap']
  },

  // Cross-crypto pairs for StablecoinSwap
  {
    id: 'BTC_ETH',
    crypto1: 'BTC',
    crypto2: 'ETH',
    displayName: 'Bitcoin / Ethereum',
    category: 'major',
    minTradeAmount: 0.0001,
    maxTradeAmount: 10,
    priceDecimals: 4,
    amountDecimals: 6,
    description: 'Top two cryptocurrencies pair',
    supportedModes: ['StablecoinSwap']
  },
  {
    id: 'ETH_BNB',
    crypto1: 'ETH',
    crypto2: 'BNB',
    displayName: 'Ethereum / Binance Coin',
    category: 'major',
    minTradeAmount: 0.001,
    maxTradeAmount: 100,
    priceDecimals: 4,
    amountDecimals: 6,
    description: 'Ethereum vs Binance ecosystem',
    supportedModes: ['StablecoinSwap']
  },

  // Additional popular pairs
  {
    id: 'XRP_USDT',
    crypto1: 'XRP',
    crypto2: 'USDT',
    displayName: 'Ripple / Tether',
    category: 'major',
    minTradeAmount: 1,
    maxTradeAmount: 100000,
    priceDecimals: 4,
    amountDecimals: 2,
    description: 'Cross-border payment cryptocurrency',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'LTC_USDT',
    crypto1: 'LTC',
    crypto2: 'USDT',
    displayName: 'Litecoin / Tether',
    category: 'major',
    minTradeAmount: 0.01,
    maxTradeAmount: 1000,
    priceDecimals: 2,
    amountDecimals: 4,
    description: 'Silver to Bitcoin\'s gold',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'BCH_USDT',
    crypto1: 'BCH',
    crypto2: 'USDT',
    displayName: 'Bitcoin Cash / Tether',
    category: 'major',
    minTradeAmount: 0.001,
    maxTradeAmount: 1000,
    priceDecimals: 2,
    amountDecimals: 5,
    description: 'Bitcoin fork with larger blocks',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'TRX_USDT',
    crypto1: 'TRX',
    crypto2: 'USDT',
    displayName: 'TRON / Tether',
    category: 'altcoin',
    minTradeAmount: 10,
    maxTradeAmount: 1000000,
    priceDecimals: 6,
    amountDecimals: 0,
    description: 'Decentralized entertainment ecosystem',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'EOS_USDT',
    crypto1: 'EOS',
    crypto2: 'USDT',
    displayName: 'EOS / Tether',
    category: 'altcoin',
    minTradeAmount: 0.1,
    maxTradeAmount: 10000,
    priceDecimals: 4,
    amountDecimals: 3,
    description: 'Delegated proof-of-stake blockchain',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'XLM_USDT',
    crypto1: 'XLM',
    crypto2: 'USDT',
    displayName: 'Stellar / Tether',
    category: 'altcoin',
    minTradeAmount: 1,
    maxTradeAmount: 100000,
    priceDecimals: 5,
    amountDecimals: 2,
    description: 'Fast and low-cost cross-border payments',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'VET_USDT',
    crypto1: 'VET',
    crypto2: 'USDT',
    displayName: 'VeChain / Tether',
    category: 'altcoin',
    minTradeAmount: 10,
    maxTradeAmount: 1000000,
    priceDecimals: 6,
    amountDecimals: 0,
    description: 'Supply chain management blockchain',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'FIL_USDT',
    crypto1: 'FIL',
    crypto2: 'USDT',
    displayName: 'Filecoin / Tether',
    category: 'altcoin',
    minTradeAmount: 0.01,
    maxTradeAmount: 1000,
    priceDecimals: 3,
    amountDecimals: 4,
    description: 'Decentralized storage network',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  },
  {
    id: 'THETA_USDT',
    crypto1: 'THETA',
    crypto2: 'USDT',
    displayName: 'Theta Network / Tether',
    category: 'altcoin',
    minTradeAmount: 0.1,
    maxTradeAmount: 10000,
    priceDecimals: 4,
    amountDecimals: 3,
    description: 'Decentralized video streaming network',
    supportedModes: ['SimpleSpot', 'StablecoinSwap']
  }
];

// Helper functions
export function getTradingPairById(id: string): TradingPair | undefined {
  return TRADING_PAIRS.find(pair => pair.id === id);
}

export function getTradingPairsByCategory(category: TradingPair['category']): TradingPair[] {
  return TRADING_PAIRS.filter(pair => pair.category === category);
}

export function getTradingPairsByMode(mode: 'SimpleSpot' | 'StablecoinSwap'): TradingPair[] {
  return TRADING_PAIRS.filter(pair => pair.supportedModes.includes(mode));
}

export function searchTradingPairs(query: string): TradingPair[] {
  const lowercaseQuery = query.toLowerCase();
  return TRADING_PAIRS.filter(pair => 
    pair.crypto1.toLowerCase().includes(lowercaseQuery) ||
    pair.crypto2.toLowerCase().includes(lowercaseQuery) ||
    pair.displayName.toLowerCase().includes(lowercaseQuery) ||
    pair.description.toLowerCase().includes(lowercaseQuery)
  );
}

export const TRADING_PAIR_CATEGORIES = [
  { value: 'major', label: 'Major Cryptocurrencies', description: 'Top market cap cryptocurrencies' },
  { value: 'altcoin', label: 'Altcoins', description: 'Alternative cryptocurrencies' },
  { value: 'defi', label: 'DeFi Tokens', description: 'Decentralized finance tokens' },
  { value: 'meme', label: 'Meme Coins', description: 'Community-driven meme tokens' },
  { value: 'stablecoin', label: 'Stablecoins', description: 'Price-stable cryptocurrencies' }
] as const;
