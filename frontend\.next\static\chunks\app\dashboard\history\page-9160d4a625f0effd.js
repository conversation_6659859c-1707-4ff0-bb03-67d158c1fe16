(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[610],{22346:(e,t,s)=>{"use strict";s.d(t,{w:()=>o});var r=s(95155),a=s(12115),i=s(87489),l=s(59434);let o=a.forwardRef((e,t)=>{let{className:s,orientation:a="horizontal",decorative:o=!0,...d}=e;return(0,r.jsx)(i.b,{ref:t,decorative:o,orientation:a,className:(0,l.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",s),...d})});o.displayName=i.b.displayName},25325:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(95155),a=s(12115),i=s(3033),l=s(54530),o=s(66695),d=s(30285),n=s(59409),c=s(26126),x=s(22346),p=s(77213),m=s(84553),y=s(87481),u=s(77223);let h=(0,s(40157).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var f=s(37648),j=s(28184);function v(){let{dispatch:e,orderHistory:t,config:s}=(0,p.U)(),{toast:i}=(0,y.dj)(),[l,f]=(0,a.useState)([]),[v,N]=(0,a.useState)("current"),[g,w]=(0,a.useState)([]),C=m.C.getInstance();(0,a.useEffect)(()=>{S()},[]),(0,a.useEffect)(()=>{"current"===v?w(t):w(C.getSessionHistory(v))},[v,t]);let S=()=>{let e=C.getFilteredSessions(5e3),t=C.getCurrentSessionId();f(e.filter(e=>e.id!==t).sort((e,t)=>t.lastModified-e.lastModified))},k="current"===v?{name:"Current Session",pair:"".concat(s.crypto1,"/").concat(s.crypto2),totalTrades:t.length,totalProfitLoss:t.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0),lastModified:Date.now(),isActive:!0}:l.find(e=>e.id===v);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsx)(o.ZB,{className:"text-xl font-bold text-primary",children:"Session History"}),(0,r.jsx)(o.BT,{children:"View trading history for current and past sessions."})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Select Session:"}),(0,r.jsxs)(n.l6,{value:v,onValueChange:N,children:[(0,r.jsx)(n.bq,{className:"w-full sm:w-[300px]",children:(0,r.jsx)(n.yv,{placeholder:"Select a session"})}),(0,r.jsxs)(n.gC,{children:[(0,r.jsx)(n.eb,{value:"current",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(c.E,{variant:"default",className:"text-xs",children:"Current"}),(0,r.jsxs)("span",{children:["Current Session (",s.crypto1&&s.crypto2?"".concat(s.crypto1,"/").concat(s.crypto2):"Crypto 1/Crypto 2 = 0",")"]})]})}),l.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.w,{className:"my-1"}),l.map(e=>(0,r.jsx)(n.eb,{value:e.id,children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(c.E,{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Current":"Past"}),(0,r.jsx)("span",{children:e.name})]})},e.id))]})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>{"current"===v?(e({type:"CLEAR_ORDER_HISTORY"}),i({title:"History Cleared",description:"Current session trade history has been cleared."})):i({title:"Cannot Clear",description:"Cannot clear history for past sessions. Use current session to clear history.",variant:"destructive"})},className:"btn-outline-neo",disabled:"current"!==v,children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Clear History"]}),(0,r.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>{let e,t;if(0===g.length){i({title:"No Data to Export",description:"There is no trade history to export for the selected session.",variant:"destructive"});return}if("current"===v)e=["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...g.map(e=>{var t,r,a,i,l,o,d;return[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,(null===(t=e.amountCrypto1)||void 0===t?void 0:t.toFixed(s.numDigits))||"",(null===(r=e.avgPrice)||void 0===r?void 0:r.toFixed(s.numDigits))||"",(null===(a=e.valueCrypto2)||void 0===a?void 0:a.toFixed(s.numDigits))||"",(null===(i=e.price1)||void 0===i?void 0:i.toFixed(s.numDigits))||"",e.crypto1Symbol,(null===(l=e.price2)||void 0===l?void 0:l.toFixed(s.numDigits))||"",e.crypto2Symbol,(null===(o=e.realizedProfitLossCrypto1)||void 0===o?void 0:o.toFixed(s.numDigits))||"",(null===(d=e.realizedProfitLossCrypto2)||void 0===d?void 0:d.toFixed(s.numDigits))||""].join(",")})].join("\n"),t="current_session_history_".concat(new Date().toISOString().split("T")[0],".csv");else{e=C.exportSessionToCSV(v);let s=C.loadSession(v);t="".concat((null==s?void 0:s.name)||"session","_").concat(new Date().toISOString().split("T")[0],".csv")}if(!e){i({title:"Export Failed",description:"Failed to generate CSV content.",variant:"destructive"});return}let r=new Blob([e],{type:"text/csv;charset=utf-8;"}),a=document.createElement("a"),l=URL.createObjectURL(r);a.setAttribute("href",l),a.setAttribute("download",t),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a),i({title:"Export Complete",description:"Trade history has been exported to CSV file."})},className:"btn-outline-neo",children:[(0,r.jsx)(h,{className:"mr-2 h-4 w-4"}),"Export"]})]})]}),k&&(0,r.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Session:"}),(0,r.jsx)("div",{className:"font-medium",children:k.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Pair:"}),(0,r.jsx)("div",{className:"font-medium",children:k.pair})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Total Trades:"}),(0,r.jsx)("div",{className:"font-medium",children:k.totalTrades})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Total P/L:"}),(0,r.jsx)("div",{className:"font-medium ".concat(k.totalProfitLoss>=0?"text-green-600":"text-red-600"),children:k.totalProfitLoss.toFixed(4)})]})]}),"current"!==v&&(0,r.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:["Last modified: ",(0,j.GP)(new Date(k.lastModified),"MMM dd, yyyy HH:mm")]})]})]})]}),(0,r.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"text-lg font-bold text-primary",children:["Trade History - ",(null==k?void 0:k.name)||"Unknown Session"]}),(0,r.jsx)(o.BT,{children:0===g.length?"No trades recorded for this session yet.":"Showing ".concat(g.length," trades for the selected session.")})]}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)(b,{history:g,config:s})})]})]})}function b(e){let{history:t,config:s}=e,a=e=>{var t;return null!==(t=null==e?void 0:e.toFixed(s.numDigits))&&void 0!==t?t:"-"},i=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if("StablecoinSwap"===e.tradingMode){if(("SELL"===e.type||"SELL"===e.orderType)&&void 0!==e.realizedProfitLossCrypto2){let s=t?e.realizedProfitLossCrypto1||0:e.realizedProfitLossCrypto2,i=s>=0?"text-green-600":"text-red-600",l=e.percentageGain||0;return(0,r.jsxs)("div",{className:"".concat(i," font-semibold"),children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{children:s>=0?"\uD83D\uDCC8":"\uD83D\uDCC9"}),(0,r.jsx)("span",{children:a(s)})]}),0!==l&&(0,r.jsxs)("div",{className:"text-xs opacity-75",children:["(",l>0?"+":"",l.toFixed(2),"%)"]})]})}return(0,r.jsx)("span",{className:"text-muted-foreground",children:"-"})}{let s=t?e.realizedProfitLossCrypto1:e.realizedProfitLossCrypto2;if(void 0!==s){let t=s>=0?"text-green-600":"text-red-600",i=e.percentageGain||0;return(0,r.jsxs)("div",{className:"".concat(t," font-semibold"),children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{children:s>=0?"\uD83D\uDCC8":"\uD83D\uDCC9"}),(0,r.jsx)("span",{children:a(s)})]}),0!==i&&(0,r.jsxs)("div",{className:"text-xs opacity-75",children:["(",i>0?"+":"",i.toFixed(2),"%)"]})]})}return(0,r.jsx)("span",{className:"text-muted-foreground",children:"-"})}},l=[{key:"date",label:"Date"},{key:"hour",label:"Hour"},{key:"pair",label:"Couple"},{key:"crypto",label:"Crypto (".concat(s.crypto1,")")},{key:"orderType",label:"Order Type"},{key:"amount",label:"Amount"},{key:"avgPrice",label:"Avg Price"},{key:"value",label:"Value (".concat(s.crypto2,")")},{key:"price1",label:"Price 1"},{key:"crypto1Symbol",label:"Crypto (".concat(s.crypto1,")")},{key:"price2",label:"Price 2"},{key:"crypto2Symbol",label:"Crypto (".concat(s.crypto2,")")},{key:"profitCrypto1",label:"Profit/Loss (".concat(s.crypto1,")")},{key:"profitCrypto2",label:"Profit/Loss (".concat(s.crypto2,")")}];return 0===t.length?(0,r.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,r.jsx)(f.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{children:"No trading history for this session yet."})]}):(0,r.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsx)("tr",{className:"bg-card hover:bg-card border-b",children:l.map(e=>(0,r.jsx)("th",{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm text-left",children:e.label},e.key))})}),(0,r.jsx)("tbody",{children:t.map(e=>{var t;return(0,r.jsxs)("tr",{className:"hover:bg-card/80 border-b",children:[(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:(0,j.GP)(new Date(e.timestamp),"yyyy-MM-dd")}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:(0,j.GP)(new Date(e.timestamp),"HH:mm:ss")}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:e.pair}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs font-semibold ".concat("BUY"===e.orderType?"text-green-400":"text-destructive"),children:e.orderType}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:a(e.amountCrypto1)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:a(e.avgPrice)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:a(e.valueCrypto2)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:a(e.price1)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:null!==(t=a(e.price2))&&void 0!==t?t:"-"}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto2Symbol}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:i(e,!0)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:i(e,!1)})]},e.id)})})]})})})}function N(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(i.A,{}),(0,r.jsx)(l.A,{}),(0,r.jsx)(v,{})]})}},30205:(e,t,s)=>{Promise.resolve().then(s.bind(s,25325))},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>p,eb:()=>h,gC:()=>u,l6:()=>c,yv:()=>x});var r=s(95155),a=s(12115),i=s(50663),l=s(79556),o=s(77381),d=s(10518),n=s(59434);let c=i.bL;i.YJ;let x=i.WT,p=a.forwardRef((e,t)=>{let{className:s,children:a,...o}=e;return(0,r.jsxs)(i.l9,{ref:t,className:(0,n.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...o,children:[a,(0,r.jsx)(i.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]})});p.displayName=i.l9.displayName;let m=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(i.PP,{ref:t,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})});m.displayName=i.PP.displayName;let y=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(i.wn,{ref:t,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",s),...a,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})});y.displayName=i.wn.displayName;let u=a.forwardRef((e,t)=>{let{className:s,children:a,position:l="popper",...o}=e;return(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{ref:t,className:(0,n.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:l,...o,children:[(0,r.jsx)(m,{}),(0,r.jsx)(i.LM,{className:(0,n.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,r.jsx)(y,{})]})})});u.displayName=i.UC.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(i.JU,{ref:t,className:(0,n.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...a})}).displayName=i.JU.displayName;let h=a.forwardRef((e,t)=>{let{className:s,children:a,...l}=e;return(0,r.jsxs)(i.q7,{ref:t,className:(0,n.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...l,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})}),(0,r.jsx)(i.p4,{children:a})]})});h.displayName=i.q7.displayName,a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(i.wv,{ref:t,className:(0,n.cn)("-mx-1 my-1 h-px bg-muted",s),...a})}).displayName=i.wv.displayName},77223:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(40157).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[823,98,842,377,150,318,303,441,684,358],()=>t(30205)),_N_E=e.O()}]);