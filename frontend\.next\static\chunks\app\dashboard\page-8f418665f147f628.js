(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{9366:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var a=r(95155),s=r(12115),l=r(77213),c=r(59434);let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,c.cn)("w-full caption-bottom text-sm",r),...s})})});o.displayName="Table";let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("thead",{ref:t,className:(0,c.cn)("[&_tr]:border-b",r),...s})});n.displayName="TableHeader";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,c.cn)("[&_tr:last-child]:border-0",r),...s})});d.displayName="TableBody",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,c.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...s})}).displayName="TableFooter";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("tr",{ref:t,className:(0,c.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...s})});i.displayName="TableRow";let x=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("th",{ref:t,className:(0,c.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...s})});x.displayName="TableHead";let p=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("td",{ref:t,className:(0,c.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...s})});p.displayName="TableCell",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("caption",{ref:t,className:(0,c.cn)("mt-4 text-sm text-muted-foreground",r),...s})}).displayName="TableCaption";var m=r(66424),y=r(26126);function u(){let{getDisplayOrders:e,config:t,currentMarketPrice:r}=(0,l.U)(),s=e(),u=function(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(null==e||isNaN(e))return"-";let a=e.toFixed(t.numDigits);return r&&e>0?"+".concat(a):a},f=e=>null==e||isNaN(e)?"-":"".concat(e.toFixed(2),"%"),h=[{key:"#",label:"#"},{key:"status",label:"Status"},{key:"orderLevel",label:"Level"},{key:"valueLevel",label:"Value"},{key:"crypto2Var",label:"".concat(t.crypto2||"Crypto 2"," Var.")},{key:"crypto1Var",label:"".concat(t.crypto1||"Crypto 1"," Var.")},{key:"targetPrice",label:"Target Price"},{key:"percentFromActualPrice",label:"% from Actual"},{key:"incomeCrypto1",label:"Income ".concat(t.crypto1||"Crypto 1")},{key:"incomeCrypto2",label:"Income ".concat(t.crypto2||"Crypto 2")},{key:"originalCostCrypto2",label:"Original Cost ".concat(t.crypto2||"Crypto 2")}];return(0,a.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,a.jsxs)(m.F,{className:"w-full whitespace-nowrap",children:[(0,a.jsxs)(o,{className:"min-w-full",children:[(0,a.jsx)(n,{children:(0,a.jsx)(i,{className:"bg-card hover:bg-card",children:h.map(e=>(0,a.jsx)(x,{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm",children:e.label},e.key))})}),(0,a.jsx)(d,{children:0===s.length?(0,a.jsx)(i,{children:(0,a.jsx)(p,{colSpan:h.length,className:"h-24 text-center text-muted-foreground",children:'No target prices set. Use "Set Target Prices" in the sidebar.'})}):s.map(e=>(0,a.jsxs)(i,{className:"hover:bg-card/80",children:[(0,a.jsx)(p,{className:"px-3 py-2 text-xs",children:e.counter}),(0,a.jsx)(p,{className:"px-3 py-2 text-xs",children:(0,a.jsx)(y.E,{variant:"Full"===e.status?"default":"secondary",className:(0,c.cn)("Full"===e.status?"bg-green-600 text-white":"bg-yellow-500 text-black","font-bold"),children:e.status})}),(0,a.jsx)(p,{className:"px-3 py-2 text-xs",children:e.orderLevel}),(0,a.jsx)(p,{className:"px-3 py-2 text-xs",children:u(e.valueLevel)}),(0,a.jsx)(p,{className:(0,c.cn)("px-3 py-2 text-xs",e.crypto2Var&&e.crypto2Var<0?"text-destructive":"text-green-400"),children:u(e.crypto2Var,!0)}),(0,a.jsx)(p,{className:(0,c.cn)("px-3 py-2 text-xs",e.crypto1Var&&e.crypto1Var<0?"text-destructive":"text-green-400"),children:u(e.crypto1Var,!0)}),(0,a.jsx)(p,{className:"px-3 py-2 text-xs font-semibold text-primary",children:u(e.targetPrice)}),(0,a.jsx)(p,{className:(0,c.cn)("px-3 py-2 text-xs",e.percentFromActualPrice<0?"text-destructive":"text-green-400"),children:f(e.percentFromActualPrice)}),(0,a.jsx)(p,{className:(0,c.cn)("px-3 py-2 text-xs",e.incomeCrypto1&&e.incomeCrypto1<0?"text-destructive":"text-green-400"),children:u(e.incomeCrypto1)}),(0,a.jsx)(p,{className:(0,c.cn)("px-3 py-2 text-xs",e.incomeCrypto2&&e.incomeCrypto2<0?"text-destructive":"text-green-400"),children:u(e.incomeCrypto2)}),(0,a.jsx)(p,{className:"px-3 py-2 text-xs",children:u(e.originalCostCrypto2)})]},e.id))})]}),(0,a.jsx)(m.$,{orientation:"horizontal"})]})})}var f=r(3033),h=r(66695),b=r(54530),N=r(80659);function j(){let{config:e,currentMarketPrice:t}=(0,l.U)(),r=e.crypto1&&e.crypto2,s=r?"".concat(e.crypto1,"/").concat(e.crypto2):"Crypto 1/Crypto 2",c=r?t.toFixed(e.numDigits):"0";return(0,a.jsx)("div",{className:"mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md",children:(0,a.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 text-green-500"}),(0,a.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Current Market Price"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"text-lg font-semibold text-foreground",children:[s,":"]}),(0,a.jsxs)("span",{className:"text-2xl font-bold text-primary",children:["$",c]})]})]})})}var g=r(84553),v=r(87481);function C(){let{config:e,saveCurrentSession:t,targetPriceRows:r,orderHistory:c}=(0,l.U)(),{toast:o}=(0,v.dj)(),[n,d]=(0,s.useState)(""),i=g.C.getInstance();(0,s.useEffect)(()=>{(()=>{let t=i.getCurrentSessionId();if(t){let e=i.loadSession(t);if(e){d(e.name);return}}e.crypto1&&e.crypto2?d("".concat(e.crypto1,"/").concat(e.crypto2," ").concat(e.tradingMode||"SimpleSpot")):d("Crypto 1/Crypto 2 = 0")})()},[e.crypto1,e.crypto2,e.tradingMode,i,r.length,c.length]);let x=n||(e.crypto1&&e.crypto2?"".concat(e.crypto1,"/").concat(e.crypto2," ").concat(e.tradingMode||"SimpleSpot"):"Crypto 1/Crypto 2 = 0");return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(f.A,{}),(0,a.jsx)(b.A,{}),(0,a.jsxs)(h.Zp,{className:"border-2 border-border",children:[(0,a.jsxs)(h.aR,{children:[(0,a.jsxs)(h.ZB,{className:"text-2xl font-bold text-primary",children:["Active Orders (",x,")"]}),(0,a.jsx)(h.BT,{children:"Current state of your target price levels. Prices update in real-time."})]}),(0,a.jsxs)(h.Wu,{children:[(0,a.jsx)(j,{}),(0,a.jsx)(u,{})]})]})]})}},15791:(e,t,r)=>{Promise.resolve().then(r.bind(r,9366))},66424:(e,t,r)=>{"use strict";r.d(t,{$:()=>n,F:()=>o});var a=r(95155),s=r(12115),l=r(47655),c=r(59434);let o=s.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,a.jsxs)(l.bL,{ref:t,className:(0,c.cn)("relative overflow-hidden",r),...o,children:[(0,a.jsx)(l.LM,{className:"h-full w-full rounded-[inherit]",children:s}),(0,a.jsx)(n,{}),(0,a.jsx)(l.OK,{})]})});o.displayName=l.bL.displayName;let n=s.forwardRef((e,t)=>{let{className:r,orientation:s="vertical",...o}=e;return(0,a.jsx)(l.VM,{ref:t,orientation:s,className:(0,c.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",r),...o,children:(0,a.jsx)(l.lr,{className:"relative flex-1 rounded-full bg-border"})})});n.displayName=l.VM.displayName}},e=>{var t=t=>e(e.s=t);e.O(0,[823,297,655,287,318,303,441,684,358],()=>t(15791)),_N_E=e.O()}]);