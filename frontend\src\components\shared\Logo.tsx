
import React, { useState } from 'react';
import { Rocket } from 'lucide-react';

interface LogoProps {
  className?: string;
  useFullName?: boolean; // Added prop to control text
}

const Logo: React.FC<LogoProps> = ({ className, useFullName = true }) => {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className={`flex items-center text-2xl font-bold text-primary ${className}`}>
      {!imageError ? (
        <img
          src="https://i.imgur.com/Q0HDcMH.png"
          alt="Pluto Trading Bot Logo"
          className="mr-2 h-7 w-7 rounded-full object-cover"
          onError={handleImageError}
          onLoad={() => console.log('Pluto logo loaded successfully')}
        />
      ) : (
        <Rocket className="mr-2 h-7 w-7" />
      )}
      <span>Pluto{useFullName ? " Trading Bot" : ""}</span>
    </div>
  );
};

export default Logo;
