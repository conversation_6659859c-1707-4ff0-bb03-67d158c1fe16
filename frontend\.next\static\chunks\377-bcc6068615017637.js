"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[377],{2564:(e,t,n)=>{n.d(t,{Qg:()=>l,s6:()=>a});var r=n(12115),o=n(63655),i=n(95155),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden"},11275:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(12115),o=n(52712);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},19178:(e,t,n)=>{n.d(t,{lg:()=>g,qW:()=>f,bL:()=>m});var r,o=n(12115),i=n(85185),l=n(63655),a=n(6101),u=n(39033),c=n(95155),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:m,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:b,...x}=e,E=o.useContext(d),[S,C]=o.useState(null),R=null!==(f=null==S?void 0:S.ownerDocument)&&void 0!==f?f:null===(n=globalThis)||void 0===n?void 0:n.document,[,T]=o.useState({}),A=(0,a.s)(t,e=>C(e)),L=Array.from(E.layers),[P]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),k=L.indexOf(P),N=S?L.indexOf(S):-1,j=E.layersWithOutsidePointerEventsDisabled.size>0,D=N>=k,O=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!D||n||(null==g||g(e),null==w||w(e),e.defaultPrevented||null==b||b())},R),M=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},R);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===E.layers.size-1&&(null==m||m(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},R),o.useEffect(()=>{if(S)return p&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),v(),()=>{p&&1===E.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[S,R,p,E]),o.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),v())},[S,E]),o.useEffect(()=>{let e=()=>T({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(l.sG.div,{...x,ref:A,style:{pointerEvents:j?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,M.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,O.onPointerDownCapture)})});f.displayName="DismissableLayer";var p=o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(l.sG.div,{...e,ref:i})});function v(){let e=new CustomEvent(s);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}p.displayName="DismissableLayerBranch";var m=f,g=p},25519:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(12115),o=n(6101),i=n(63655),l=n(39033),a=n(95155),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:m,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,l.c)(m),E=(0,l.c)(g),S=r.useRef(null),C=(0,o.s)(t,e=>b(e)),R=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?S.current=t:v(S.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||v(S.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,R.paused]),r.useEffect(()=>{if(w){h.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(v(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||v(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,E),h.remove(R)},0)}}},[w,x,E,R]);let T=r.useCallback(e=>{if(!n&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&v(i,{select:!0})):(e.preventDefault(),n&&v(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,R.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:T})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function v(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var h=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=m(e,t)).unshift(t)},remove(t){var n;null===(n=(e=m(e,t))[0])||void 0===n||n.resume()}}}();function m(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},34378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(12115),o=n(47650),i=n(63655),l=n(52712),a=n(95155),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[d,f]=r.useState(!1);(0,l.N)(()=>f(!0),[]);let p=c||d&&(null===(u=globalThis)||void 0===u?void 0:null===(n=u.document)||void 0===n?void 0:n.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"},37648:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},38168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[n]||(l[n]=new WeakMap);var s=l[n],d=[],f=new Set,p=new Set(c),v=function(e){!(!e||f.has(e))&&(f.add(e),v(e.parentNode))};c.forEach(v);var h=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,a),s.set(e,u),d.push(e),1===a&&l&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,l=s.get(e)-1;o.set(e,t),s.set(e,l),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),l||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},45503:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(12115);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},50663:(e,t,n)=>{n.d(t,{UC:()=>nf,YJ:()=>nv,In:()=>ns,q7:()=>nm,VF:()=>ny,p4:()=>ng,JU:()=>nh,ZL:()=>nd,bL:()=>na,wn:()=>nb,PP:()=>nw,wv:()=>nx,l9:()=>nu,WT:()=>nc,LM:()=>np});var r=n(12115),o=n(47650),i=n(89367),l=n(85185),a=n(37328),u=n(6101),c=n(46081),s=n(94315),d=n(19178),f=n(92293),p=n(25519),v=n(61285);let h=["top","right","bottom","left"],m=Math.min,g=Math.max,y=Math.round,w=Math.floor,b=e=>({x:e,y:e}),x={left:"right",right:"left",bottom:"top",top:"bottom"},E={start:"end",end:"start"};function S(e,t){return"function"==typeof e?e(t):e}function C(e){return e.split("-")[0]}function R(e){return e.split("-")[1]}function T(e){return"x"===e?"y":"x"}function A(e){return"y"===e?"height":"width"}let L=new Set(["top","bottom"]);function P(e){return L.has(C(e))?"y":"x"}function k(e){return e.replace(/start|end/g,e=>E[e])}let N=["left","right"],j=["right","left"],D=["top","bottom"],O=["bottom","top"];function M(e){return e.replace(/left|right|bottom|top/g,e=>x[e])}function I(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function W(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function F(e,t,n){let r,{reference:o,floating:i}=e,l=P(t),a=T(P(t)),u=A(a),c=C(t),s="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(R(t)){case"start":r[a]-=p*(n&&s?-1:1);break;case"end":r[a]+=p*(n&&s?-1:1)}return r}let H=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=F(c,r,u),f=r,p={},v=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:m,y:g,data:y,reset:w}=await h({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});s=null!=m?m:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&v<=50&&(v++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=F(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function B(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=S(t,e),v=I(p),h=a[f?"floating"===d?"reference":"floating":d],m=W(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:s,strategy:u})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=W(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:u}):g);return{top:(m.top-b.top+v.top)/w.y,bottom:(b.bottom-m.bottom+v.bottom)/w.y,left:(m.left-b.left+v.left)/w.x,right:(b.right-m.right+v.right)/w.x}}function _(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function z(e){return h.some(t=>e[t]>=0)}let V=new Set(["left","top"]);async function G(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=C(n),a=R(n),u="y"===P(n),c=V.has(l)?-1:1,s=i&&u?-1:1,d=S(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:v}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof v&&(p="end"===a?-1*v:v),u?{x:p*s,y:f*c}:{x:f*c,y:p*s}}function K(){return"undefined"!=typeof window}function q(e){return Y(e)?(e.nodeName||"").toLowerCase():"#document"}function U(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function X(e){var t;return null==(t=(Y(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Y(e){return!!K()&&(e instanceof Node||e instanceof U(e).Node)}function Z(e){return!!K()&&(e instanceof Element||e instanceof U(e).Element)}function $(e){return!!K()&&(e instanceof HTMLElement||e instanceof U(e).HTMLElement)}function J(e){return!!K()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof U(e).ShadowRoot)}let Q=new Set(["inline","contents"]);function ee(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ed(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Q.has(o)}let et=new Set(["table","td","th"]),en=[":popover-open",":modal"];function er(e){return en.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eo=["transform","translate","scale","rotate","perspective"],ei=["transform","translate","scale","rotate","perspective","filter"],el=["paint","layout","strict","content"];function ea(e){let t=eu(),n=Z(e)?ed(e):e;return eo.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||ei.some(e=>(n.willChange||"").includes(e))||el.some(e=>(n.contain||"").includes(e))}function eu(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ec=new Set(["html","body","#document"]);function es(e){return ec.has(q(e))}function ed(e){return U(e).getComputedStyle(e)}function ef(e){return Z(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ep(e){if("html"===q(e))return e;let t=e.assignedSlot||e.parentNode||J(e)&&e.host||X(e);return J(t)?t.host:t}function ev(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ep(t);return es(n)?t.ownerDocument?t.ownerDocument.body:t.body:$(n)&&ee(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=U(o);if(i){let e=eh(l);return t.concat(l,l.visualViewport||[],ee(o)?o:[],e&&n?ev(e):[])}return t.concat(o,ev(o,[],n))}function eh(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function em(e){let t=ed(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=$(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=y(n)!==i||y(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eg(e){return Z(e)?e:e.contextElement}function ey(e){let t=eg(e);if(!$(t))return b(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=em(t),l=(i?y(n.width):n.width)/r,a=(i?y(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let ew=b(0);function eb(e){let t=U(e);return eu()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ew}function ex(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eg(e),a=b(1);t&&(r?Z(r)&&(a=ey(r)):a=ey(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===U(l))&&o)?eb(l):b(0),c=(i.left+u.x)/a.x,s=(i.top+u.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=U(l),t=r&&Z(r)?U(r):r,n=e,o=eh(n);for(;o&&r&&t!==n;){let e=ey(o),t=o.getBoundingClientRect(),r=ed(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,f*=e.y,c+=i,s+=l,o=eh(n=U(o))}}return W({width:d,height:f,x:c,y:s})}function eE(e,t){let n=ef(e).scrollLeft;return t?t.left+n:ex(X(e)).left+n}function eS(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eE(e,r)),y:r.top+t.scrollTop}}let eC=new Set(["absolute","fixed"]);function eR(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=U(e),r=X(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,u=0;if(o){i=o.width,l=o.height;let e=eu();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,u=o.offsetTop)}return{width:i,height:l,x:a,y:u}}(e,n);else if("document"===t)r=function(e){let t=X(e),n=ef(e),r=e.ownerDocument.body,o=g(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=g(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eE(e),a=-n.scrollTop;return"rtl"===ed(r).direction&&(l+=g(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(X(e));else if(Z(t))r=function(e,t){let n=ex(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=$(e)?ey(e):b(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}}(t,n);else{let n=eb(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return W(r)}function eT(e){return"static"===ed(e).position}function eA(e,t){if(!$(e)||"fixed"===ed(e).position)return null;if(t)return t(e);let n=e.offsetParent;return X(e)===n&&(n=n.ownerDocument.body),n}function eL(e,t){var n;let r=U(e);if(er(e))return r;if(!$(e)){let t=ep(e);for(;t&&!es(t);){if(Z(t)&&!eT(t))return t;t=ep(t)}return r}let o=eA(e,t);for(;o&&(n=o,et.has(q(n)))&&eT(o);)o=eA(o,t);return o&&es(o)&&eT(o)&&!ea(o)?r:o||function(e){let t=ep(e);for(;$(t)&&!es(t);){if(ea(t))return t;if(er(t))break;t=ep(t)}return null}(e)||r}let eP=async function(e){let t=this.getOffsetParent||eL,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=$(t),o=X(t),i="fixed"===n,l=ex(e,!0,i,t),a={scrollLeft:0,scrollTop:0},u=b(0);if(r||!r&&!i){if(("body"!==q(t)||ee(o))&&(a=ef(t)),r){let e=ex(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=eE(o))}i&&!r&&o&&(u.x=eE(o));let c=!o||r||i?b(0):eS(o,a);return{x:l.left+a.scrollLeft-u.x-c.x,y:l.top+a.scrollTop-u.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ek={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=X(r),a=!!t&&er(t.floating);if(r===l||a&&i)return n;let u={scrollLeft:0,scrollTop:0},c=b(1),s=b(0),d=$(r);if((d||!d&&!i)&&(("body"!==q(r)||ee(l))&&(u=ef(r)),$(r))){let e=ex(r);c=ey(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let f=!l||d||i?b(0):eS(l,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+s.x+f.x,y:n.y*c.y-u.scrollTop*c.y+s.y+f.y}},getDocumentElement:X,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?er(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=ev(e,[],!1).filter(e=>Z(e)&&"body"!==q(e)),o=null,i="fixed"===ed(e).position,l=i?ep(e):e;for(;Z(l)&&!es(l);){let t=ed(l),n=ea(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&eC.has(o.position)||ee(l)&&!n&&function e(t,n){let r=ep(t);return!(r===n||!Z(r)||es(r))&&("fixed"===ed(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=ep(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eR(t,n,o);return e.top=g(r.top,e.top),e.right=m(r.right,e.right),e.bottom=m(r.bottom,e.bottom),e.left=g(r.left,e.left),e},eR(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eL,getElementRects:eP,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=em(e);return{width:t,height:n}},getScale:ey,isElement:Z,isRTL:function(e){return"rtl"===ed(e).direction}};function eN(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ej=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:u}=t,{element:c,padding:s=0}=S(e,t)||{};if(null==c)return{};let d=I(s),f={x:n,y:r},p=T(P(o)),v=A(p),h=await l.getDimensions(c),y="y"===p,w=y?"clientHeight":"clientWidth",b=i.reference[v]+i.reference[p]-f[p]-i.floating[v],x=f[p]-i.reference[p],E=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),C=E?E[w]:0;C&&await (null==l.isElement?void 0:l.isElement(E))||(C=a.floating[w]||i.floating[v]);let L=C/2-h[v]/2-1,k=m(d[y?"top":"left"],L),N=m(d[y?"bottom":"right"],L),j=C-h[v]-N,D=C/2-h[v]/2+(b/2-x/2),O=g(k,m(D,j)),M=!u.arrow&&null!=R(o)&&D!==O&&i.reference[v]/2-(D<k?k:N)-h[v]/2<0,W=M?D<k?D-k:D-j:0;return{[p]:f[p]+W,data:{[p]:O,centerOffset:D-O-W,...M&&{alignmentOffset:W}},reset:M}}}),eD=(e,t,n)=>{let r=new Map,o={platform:ek,...n},i={...o.platform,_c:r};return H(e,t,{...o,platform:i})};var eO="undefined"!=typeof document?r.useLayoutEffect:function(){};function eM(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eM(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eM(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eI(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eW(e,t){let n=eI(e);return Math.round(t*n)/n}function eF(e){let t=r.useRef(e);return eO(()=>{t.current=e}),t}let eH=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ej({element:n.current,padding:r}).fn(t):{}:n?ej({element:n,padding:r}).fn(t):{}}}),eB=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,u=await G(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:l}}}}}(e),options:[e,t]}),e_=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=S(e,t),c={x:n,y:r},s=await B(t,u),d=P(C(o)),f=T(d),p=c[f],v=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+s[e],r=p-s[t];p=g(n,m(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=v+s[e],r=v-s[t];v=g(n,m(v,r))}let h=a.fn({...t,[f]:p,[d]:v});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),ez=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:u=!0,crossAxis:c=!0}=S(e,t),s={x:n,y:r},d=P(o),f=T(d),p=s[f],v=s[d],h=S(a,t),m="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(u){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+m.mainAxis,n=i.reference[f]+i.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(c){var g,y;let e="y"===f?"width":"height",t=V.has(C(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:m.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?m.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[f]:p,[d]:v}}}}(e),options:[e,t]}),eV=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:v=!0,fallbackPlacements:h,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=S(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let b=C(a),x=P(s),E=C(s)===s,L=await (null==d.isRTL?void 0:d.isRTL(f.floating)),I=h||(E||!y?[M(s)]:function(e){let t=M(e);return[k(e),t,k(t)]}(s)),W="none"!==g;!h&&W&&I.push(...function(e,t,n,r){let o=R(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?j:N;return t?N:j;case"left":case"right":return t?D:O;default:return[]}}(C(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(k)))),i}(s,y,g,L));let F=[s,...I],H=await B(t,w),_=[],z=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&_.push(H[b]),v){let e=function(e,t,n){void 0===n&&(n=!1);let r=R(e),o=T(P(e)),i=A(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=M(l)),[l,M(l)]}(a,c,L);_.push(H[e[0]],H[e[1]])}if(z=[...z,{placement:a,overflows:_}],!_.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=F[e];if(t&&("alignment"!==v||x===P(t)||z.every(e=>e.overflows[0]>0&&P(e.placement)===x)))return{data:{index:e,overflows:z},reset:{placement:t}};let n=null==(i=z.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(m){case"bestFit":{let e=null==(l=z.filter(e=>{if(W){let t=P(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=s}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eG=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i;let{placement:l,rects:a,platform:u,elements:c}=t,{apply:s=()=>{},...d}=S(e,t),f=await B(t,d),p=C(l),v=R(l),h="y"===P(l),{width:y,height:w}=a.floating;"top"===p||"bottom"===p?(o=p,i=v===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===v?"top":"bottom");let b=w-f.top-f.bottom,x=y-f.left-f.right,E=m(w-f[o],b),T=m(y-f[i],x),A=!t.middlewareData.shift,L=E,k=T;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=x),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(L=b),A&&!v){let e=g(f.left,0),t=g(f.right,0),n=g(f.top,0),r=g(f.bottom,0);h?k=y-2*(0!==e||0!==t?e+t:g(f.left,f.right)):L=w-2*(0!==n||0!==r?n+r:g(f.top,f.bottom))}await s({...t,availableWidth:k,availableHeight:L});let N=await u.getDimensions(c.floating);return y!==N.width||w!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eK=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=S(e,t);switch(r){case"referenceHidden":{let e=_(await B(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:z(e)}}}case"escaped":{let e=_(await B(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:z(e)}}}default:return{}}}}}(e),options:[e,t]}),eq=(e,t)=>({...eH(e),options:[e,t]});var eU=n(63655),eX=n(95155),eY=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eX.jsx)(eU.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eX.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eY.displayName="Arrow";var eZ=n(39033),e$=n(52712),eJ=n(11275),eQ="Popper",[e0,e1]=(0,c.A)(eQ),[e5,e2]=e0(eQ),e6=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eX.jsx)(e5,{scope:t,anchor:o,onAnchorChange:i,children:n})};e6.displayName=eQ;var e3="PopperAnchor",e9=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,l=e2(e3,n),a=r.useRef(null),c=(0,u.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eX.jsx)(eU.sG.div,{...i,ref:c})});e9.displayName=e3;var e7="PopperContent",[e8,e4]=e0(e7),te=r.forwardRef((e,t)=>{var n,i,l,a,c,s,d,f;let{__scopePopper:p,side:v="bottom",sideOffset:h=0,align:y="center",alignOffset:b=0,arrowPadding:x=0,avoidCollisions:E=!0,collisionBoundary:S=[],collisionPadding:C=0,sticky:R="partial",hideWhenDetached:T=!1,updatePositionStrategy:A="optimized",onPlaced:L,...P}=e,k=e2(e7,p),[N,j]=r.useState(null),D=(0,u.s)(t,e=>j(e)),[O,M]=r.useState(null),I=(0,eJ.X)(O),W=null!==(d=null==I?void 0:I.width)&&void 0!==d?d:0,F=null!==(f=null==I?void 0:I.height)&&void 0!==f?f:0,H="number"==typeof C?C:{top:0,right:0,bottom:0,left:0,...C},B=Array.isArray(S)?S:[S],_=B.length>0,z={padding:H,boundary:B.filter(to),altBoundary:_},{refs:V,floatingStyles:G,placement:K,isPositioned:q,middlewareData:U}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:u}={},transform:c=!0,whileElementsMounted:s,open:d}=e,[f,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,h]=r.useState(i);eM(v,i)||h(i);let[m,g]=r.useState(null),[y,w]=r.useState(null),b=r.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=a||m,S=u||y,C=r.useRef(null),R=r.useRef(null),T=r.useRef(f),A=null!=s,L=eF(s),P=eF(l),k=eF(d),N=r.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:n,middleware:v};P.current&&(e.platform=P.current),eD(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==k.current};j.current&&!eM(T.current,t)&&(T.current=t,o.flushSync(()=>{p(t)}))})},[v,t,n,P,k]);eO(()=>{!1===d&&T.current.isPositioned&&(T.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let j=r.useRef(!1);eO(()=>(j.current=!0,()=>{j.current=!1}),[]),eO(()=>{if(E&&(C.current=E),S&&(R.current=S),E&&S){if(L.current)return L.current(E,S,N);N()}},[E,S,N,L,A]);let D=r.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:x}),[b,x]),O=r.useMemo(()=>({reference:E,floating:S}),[E,S]),M=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!O.floating)return e;let t=eW(O.floating,f.x),r=eW(O.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+r+"px)",...eI(O.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,c,O.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:N,refs:D,elements:O,floatingStyles:M}),[f,N,D,O,M])}({strategy:"fixed",placement:v+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,s=eg(e),d=i||l?[...s?ev(s):[],...ev(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=s&&u?function(e,t){let n,r=null,o=X(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function l(a,u){void 0===a&&(a=!1),void 0===u&&(u=1),i();let c=e.getBoundingClientRect(),{left:s,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let v=w(d),h=w(o.clientWidth-(s+f)),y={rootMargin:-v+"px "+-h+"px "+-w(o.clientHeight-(d+p))+"px "+-w(s)+"px",threshold:g(0,m(1,u))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==u){if(!b)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||eN(c,e.getBoundingClientRect())||l(),b=!1}try{r=new IntersectionObserver(x,{...y,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,y)}r.observe(e)}(!0),i}(s,n):null,p=-1,v=null;a&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===s&&v&&(v.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),s&&!c&&v.observe(s),v.observe(t));let h=c?ex(e):null;return c&&function t(){let r=ex(e);h&&!eN(h,r)&&n(),h=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=v)||e.disconnect(),v=null,c&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:k.anchor},middleware:[eB({mainAxis:h+F,alignmentAxis:b}),E&&e_({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?ez():void 0,...z}),E&&eV({...z}),eG({...z,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),O&&eq({element:O,padding:x}),ti({arrowWidth:W,arrowHeight:F}),T&&eK({strategy:"referenceHidden",...z})]}),[Y,Z]=tl(K),$=(0,eZ.c)(L);(0,e$.N)(()=>{q&&(null==$||$())},[q,$]);let J=null===(n=U.arrow)||void 0===n?void 0:n.x,Q=null===(i=U.arrow)||void 0===i?void 0:i.y,ee=(null===(l=U.arrow)||void 0===l?void 0:l.centerOffset)!==0,[et,en]=r.useState();return(0,e$.N)(()=>{N&&en(window.getComputedStyle(N).zIndex)},[N]),(0,eX.jsx)("div",{ref:V.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:q?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:et,"--radix-popper-transform-origin":[null===(a=U.transformOrigin)||void 0===a?void 0:a.x,null===(c=U.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(s=U.hide)||void 0===s?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eX.jsx)(e8,{scope:p,placedSide:Y,onArrowChange:M,arrowX:J,arrowY:Q,shouldHideArrow:ee,children:(0,eX.jsx)(eU.sG.div,{"data-side":Y,"data-align":Z,...P,ref:D,style:{...P.style,animation:q?void 0:"none"}})})})});te.displayName=e7;var tt="PopperArrow",tn={top:"bottom",right:"left",bottom:"top",left:"right"},tr=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e4(tt,n),i=tn[o.placedSide];return(0,eX.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eX.jsx)(eY,{...r,ref:t,style:{...r.style,display:"block"}})})});function to(e){return null!==e}tr.displayName=tt;var ti=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:c}=t,s=(null===(n=c.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,v]=tl(a),h={start:"0%",center:"50%",end:"100%"}[v],m=(null!==(i=null===(r=c.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+d/2,g=(null!==(l=null===(o=c.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,y="",w="";return"bottom"===p?(y=s?h:"".concat(m,"px"),w="".concat(-f,"px")):"top"===p?(y=s?h:"".concat(m,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?h:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?h:"".concat(g,"px")),{data:{x:y,y:w}}}});function tl(e){let[t,n="center"]=e.split("-");return[t,n]}var ta=n(34378),tu=n(99708),tc=n(5845),ts=n(45503),td=n(2564),tf=n(38168),tp=n(93795),tv=[" ","Enter","ArrowUp","ArrowDown"],th=[" ","Enter"],tm="Select",[tg,ty,tw]=(0,a.N)(tm),[tb,tx]=(0,c.A)(tm,[tw,e1]),tE=e1(),[tS,tC]=tb(tm),[tR,tT]=tb(tm),tA=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:u,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:h,required:m,form:g}=e,y=tE(t),[w,b]=r.useState(null),[x,E]=r.useState(null),[S,C]=r.useState(!1),R=(0,s.jH)(d),[T,A]=(0,tc.i)({prop:o,defaultProp:null!=i&&i,onChange:l,caller:tm}),[L,P]=(0,tc.i)({prop:a,defaultProp:u,onChange:c,caller:tm}),k=r.useRef(null),N=!w||g||!!w.closest("form"),[j,D]=r.useState(new Set),O=Array.from(j).map(e=>e.props.value).join(";");return(0,eX.jsx)(e6,{...y,children:(0,eX.jsxs)(tS,{required:m,scope:t,trigger:w,onTriggerChange:b,valueNode:x,onValueNodeChange:E,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:(0,v.B)(),value:L,onValueChange:P,open:T,onOpenChange:A,dir:R,triggerPointerDownPosRef:k,disabled:h,children:[(0,eX.jsx)(tg.Provider,{scope:t,children:(0,eX.jsx)(tR,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{D(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{D(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),N?(0,eX.jsxs)(nr,{"aria-hidden":!0,required:m,tabIndex:-1,name:f,autoComplete:p,value:L,onChange:e=>P(e.target.value),disabled:h,form:g,children:[void 0===L?(0,eX.jsx)("option",{value:""}):null,Array.from(j)]},O):null]})})};tA.displayName=tm;var tL="SelectTrigger",tP=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=tE(n),c=tC(tL,n),s=c.disabled||o,d=(0,u.s)(t,c.onTriggerChange),f=ty(n),p=r.useRef("touch"),[v,h,m]=ni(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===c.value),r=nl(t,e,n);void 0!==r&&c.onValueChange(r.value)}),g=e=>{s||(c.onOpenChange(!0),m()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,eX.jsx)(e9,{asChild:!0,...a,children:(0,eX.jsx)(eU.sG.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":no(c.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&tv.includes(e.key)&&(g(),e.preventDefault())})})})});tP.displayName=tL;var tk="SelectValue",tN=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,c=tC(tk,n),{onValueNodeHasChildrenChange:s}=c,d=void 0!==i,f=(0,u.s)(t,c.onValueNodeChange);return(0,e$.N)(()=>{s(d)},[s,d]),(0,eX.jsx)(eU.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:no(c.value)?(0,eX.jsx)(eX.Fragment,{children:l}):i})});tN.displayName=tk;var tj=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,eX.jsx)(eU.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tj.displayName="SelectIcon";var tD=e=>(0,eX.jsx)(ta.Z,{asChild:!0,...e});tD.displayName="SelectPortal";var tO="SelectContent",tM=r.forwardRef((e,t)=>{let n=tC(tO,e.__scopeSelect),[i,l]=r.useState();return((0,e$.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,eX.jsx)(tH,{...e,ref:t}):i?o.createPortal((0,eX.jsx)(tI,{scope:e.__scopeSelect,children:(0,eX.jsx)(tg.Slot,{scope:e.__scopeSelect,children:(0,eX.jsx)("div",{children:e.children})})}),i):null});tM.displayName=tO;var[tI,tW]=tb(tO),tF=(0,tu.TL)("SelectContent.RemoveScroll"),tH=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:c,side:s,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:E,...S}=e,C=tC(tO,n),[R,T]=r.useState(null),[A,L]=r.useState(null),P=(0,u.s)(t,e=>T(e)),[k,N]=r.useState(null),[j,D]=r.useState(null),O=ty(n),[M,I]=r.useState(!1),W=r.useRef(!1);r.useEffect(()=>{if(R)return(0,tf.Eq)(R)},[R]),(0,f.Oh)();let F=r.useCallback(e=>{let[t,...n]=O().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&A&&(A.scrollTop=0),n===r&&A&&(A.scrollTop=A.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[O,A]),H=r.useCallback(()=>F([k,R]),[F,k,R]);r.useEffect(()=>{M&&H()},[M,H]);let{onOpenChange:B,triggerPointerDownPosRef:_}=C;r.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(n=_.current)||void 0===n?void 0:n.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(r=_.current)||void 0===r?void 0:r.y)&&void 0!==i?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():R.contains(n.target)||B(!1),document.removeEventListener("pointermove",t),_.current=null};return null!==_.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[R,B,_]),r.useEffect(()=>{let e=()=>B(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[B]);let[z,V]=ni(e=>{let t=O().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=nl(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),G=r.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==C.value&&C.value===t||r)&&(N(e),r&&(W.current=!0))},[C.value]),K=r.useCallback(()=>null==R?void 0:R.focus(),[R]),q=r.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==C.value&&C.value===t||r)&&D(e)},[C.value]),U="popper"===o?t_:tB,X=U===t_?{side:s,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:E}:{};return(0,eX.jsx)(tI,{scope:n,content:R,viewport:A,onViewportChange:L,itemRefCallback:G,selectedItem:k,onItemLeave:K,itemTextRefCallback:q,focusSelectedItem:H,selectedItemText:j,position:o,isPositioned:M,searchRef:z,children:(0,eX.jsx)(tp.A,{as:tF,allowPinchZoom:!0,children:(0,eX.jsx)(p.n,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{var t;null===(t=C.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,eX.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,eX.jsx)(U,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...S,...X,onPlaced:()=>I(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...S.style},onKeyDown:(0,l.m)(S.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||V(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});tH.displayName="SelectContentImpl";var tB=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...l}=e,a=tC(tO,n),c=tW(tO,n),[s,d]=r.useState(null),[f,p]=r.useState(null),v=(0,u.s)(t,e=>p(e)),h=ty(n),m=r.useRef(!1),g=r.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:b,focusSelectedItem:x}=c,E=r.useCallback(()=>{if(a.trigger&&a.valueNode&&s&&f&&y&&w&&b){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=b.getBoundingClientRect();if("rtl"!==a.dir){let o=r.left-t.left,l=n.left-o,a=e.left-l,u=e.width+a,c=Math.max(u,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-c)]);s.style.minWidth=u+"px",s.style.left=f+"px"}else{let o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,u=e.width+a,c=Math.max(u,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-c)]);s.style.minWidth=u+"px",s.style.right=f+"px"}let l=h(),u=window.innerHeight-20,c=y.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),v=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),x=p+v+c+parseInt(d.paddingBottom,10)+g,E=Math.min(5*w.offsetHeight,x),S=window.getComputedStyle(y),C=parseInt(S.paddingTop,10),R=parseInt(S.paddingBottom,10),T=e.top+e.height/2-10,A=w.offsetHeight/2,L=p+v+(w.offsetTop+A);if(L<=T){let e=l.length>0&&w===l[l.length-1].ref.current;s.style.bottom="0px";let t=Math.max(u-T,A+(e?R:0)+(f.clientHeight-y.offsetTop-y.offsetHeight)+g);s.style.height=L+t+"px"}else{let e=l.length>0&&w===l[0].ref.current;s.style.top="0px";let t=Math.max(T,p+y.offsetTop+(e?C:0)+A);s.style.height=t+(x-L)+"px",y.scrollTop=L-T+y.offsetTop}s.style.margin="".concat(10,"px 0"),s.style.minHeight=E+"px",s.style.maxHeight=u+"px",null==o||o(),requestAnimationFrame(()=>m.current=!0)}},[h,a.trigger,a.valueNode,s,f,y,w,b,a.dir,o]);(0,e$.N)(()=>E(),[E]);let[S,C]=r.useState();(0,e$.N)(()=>{f&&C(window.getComputedStyle(f).zIndex)},[f]);let R=r.useCallback(e=>{e&&!0===g.current&&(E(),null==x||x(),g.current=!1)},[E,x]);return(0,eX.jsx)(tz,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,eX.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:(0,eX.jsx)(eU.sG.div,{...l,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tB.displayName="SelectItemAlignedPosition";var t_=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=tE(n);return(0,eX.jsx)(te,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});t_.displayName="SelectPopperPosition";var[tz,tV]=tb(tO,{}),tG="SelectViewport",tK=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=tW(tG,n),c=tV(tG,n),s=(0,u.s)(t,a.onViewportChange),d=r.useRef(0);return(0,eX.jsxs)(eX.Fragment,{children:[(0,eX.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,eX.jsx)(tg.Slot,{scope:n,children:(0,eX.jsx)(eU.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:s,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=c;if((null==r?void 0:r.current)&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});tK.displayName=tG;var tq="SelectGroup",[tU,tX]=tb(tq),tY=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,v.B)();return(0,eX.jsx)(tU,{scope:n,id:o,children:(0,eX.jsx)(eU.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});tY.displayName=tq;var tZ="SelectLabel",t$=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tX(tZ,n);return(0,eX.jsx)(eU.sG.div,{id:o.id,...r,ref:t})});t$.displayName=tZ;var tJ="SelectItem",[tQ,t0]=tb(tJ),t1=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...c}=e,s=tC(tJ,n),d=tW(tJ,n),f=s.value===o,[p,h]=r.useState(null!=a?a:""),[m,g]=r.useState(!1),y=(0,u.s)(t,e=>{var t;return null===(t=d.itemRefCallback)||void 0===t?void 0:t.call(d,e,o,i)}),w=(0,v.B)(),b=r.useRef("touch"),x=()=>{i||(s.onValueChange(o),s.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,eX.jsx)(tQ,{scope:n,value:o,disabled:i,textId:w,isSelected:f,onItemTextChange:r.useCallback(e=>{h(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,eX.jsx)(tg.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,eX.jsx)(eU.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":m?"":void 0,"aria-selected":f&&m,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:y,onFocus:(0,l.m)(c.onFocus,()=>g(!0)),onBlur:(0,l.m)(c.onBlur,()=>g(!1)),onClick:(0,l.m)(c.onClick,()=>{"mouse"!==b.current&&x()}),onPointerUp:(0,l.m)(c.onPointerUp,()=>{"mouse"===b.current&&x()}),onPointerDown:(0,l.m)(c.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.m)(c.onPointerMove,e=>{if(b.current=e.pointerType,i){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(c.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}}),onKeyDown:(0,l.m)(c.onKeyDown,e=>{var t;((null===(t=d.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(th.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});t1.displayName=tJ;var t5="SelectItemText",t2=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:l,...a}=e,c=tC(t5,n),s=tW(t5,n),d=t0(t5,n),f=tT(t5,n),[p,v]=r.useState(null),h=(0,u.s)(t,e=>v(e),d.onItemTextChange,e=>{var t;return null===(t=s.itemTextRefCallback)||void 0===t?void 0:t.call(s,e,d.value,d.disabled)}),m=null==p?void 0:p.textContent,g=r.useMemo(()=>(0,eX.jsx)("option",{value:d.value,disabled:d.disabled,children:m},d.value),[d.disabled,d.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=f;return(0,e$.N)(()=>(y(g),()=>w(g)),[y,w,g]),(0,eX.jsxs)(eX.Fragment,{children:[(0,eX.jsx)(eU.sG.span,{id:d.textId,...a,ref:h}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?o.createPortal(a.children,c.valueNode):null]})});t2.displayName=t5;var t6="SelectItemIndicator",t3=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return t0(t6,n).isSelected?(0,eX.jsx)(eU.sG.span,{"aria-hidden":!0,...r,ref:t}):null});t3.displayName=t6;var t9="SelectScrollUpButton",t7=r.forwardRef((e,t)=>{let n=tW(t9,e.__scopeSelect),o=tV(t9,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,e$.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eX.jsx)(ne,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});t7.displayName=t9;var t8="SelectScrollDownButton",t4=r.forwardRef((e,t)=>{let n=tW(t8,e.__scopeSelect),o=tV(t8,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,u.s)(t,o.onScrollButtonChange);return(0,e$.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eX.jsx)(ne,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});t4.displayName=t8;var ne=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=tW("SelectScrollButton",n),u=r.useRef(null),c=ty(n),s=r.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return r.useEffect(()=>()=>s(),[s]),(0,e$.N)(()=>{var e;let t=c().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[c]),(0,eX.jsx)(eU.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{var e;null===(e=a.onItemLeave)||void 0===e||e.call(a),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{s()})})}),nt=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,eX.jsx)(eU.sG.div,{"aria-hidden":!0,...r,ref:t})});nt.displayName="SelectSeparator";var nn="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tE(n),i=tC(nn,n),l=tW(nn,n);return i.open&&"popper"===l.position?(0,eX.jsx)(tr,{...o,...r,ref:t}):null}).displayName=nn;var nr=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,...i}=e,l=r.useRef(null),a=(0,u.s)(t,l),c=(0,ts.Z)(o);return r.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(c!==o&&t){let n=new Event("change",{bubbles:!0});t.call(e,o),e.dispatchEvent(n)}},[c,o]),(0,eX.jsx)(eU.sG.select,{...i,style:{...td.Qg,...i.style},ref:a,defaultValue:o})});function no(e){return""===e||void 0===e}function ni(e){let t=(0,eZ.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function nl(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return u!==n?u:void 0}nr.displayName="SelectBubbleInput";var na=tA,nu=tP,nc=tN,ns=tj,nd=tD,nf=tM,np=tK,nv=tY,nh=t$,nm=t1,ng=t2,ny=t3,nw=t7,nb=t4,nx=nt},77381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},79556:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(40157).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},87489:(e,t,n)=>{n.d(t,{b:()=>c});var r=n(12115),o=n(63655),i=n(95155),l="horizontal",a=["horizontal","vertical"],u=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:u=l,...c}=e,s=(n=u,a.includes(n))?u:l;return(0,i.jsx)(o.sG.div,{"data-orientation":s,...r?{role:"none"}:{"aria-orientation":"vertical"===s?s:void 0,role:"separator"},...c,ref:t})});u.displayName="Separator";var c=u},92293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(12115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:l()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},93795:(e,t,n)=>{n.d(t,{A:()=>K});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(12115)),a="right-scroll-bar-position",u="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i,l=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(o)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return l.options=o({async:!0,ssr:!1},e),l}(),v=function(){},h=l.forwardRef(function(e,t){var n,r,a,u,f=l.useRef(null),h=l.useState({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:v}),m=h[0],g=h[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,S=e.shards,C=e.sideCar,R=e.noRelative,T=e.noIsolation,A=e.inert,L=e.allowPinchZoom,P=e.as,k=e.gapMode,N=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),j=(n=[f,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(a=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=r,u=a.facade,s(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}d.set(u,n)},[n]),u),D=o(o({},N),m);return l.createElement(l.Fragment,null,E&&l.createElement(C,{sideCar:p,removeScrollBar:x,shards:S,noRelative:R,noIsolation:T,inert:A,setCallbacks:g,allowPinchZoom:!!L,lockRef:f,gapMode:k}),y?l.cloneElement(l.Children.only(w),o(o({},D),{ref:j})):l.createElement(void 0===P?"div":P,o({},D,{className:b,ref:j}),w))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:u,zeroRight:a};var m=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,o({},n))};m.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},E=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(n),x(r),x(o)]},S=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=E(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},C=w(),R="data-scroll-locked",T=function(e,t,n,r){var o=e.left,i=e.top,l=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(u," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},A=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},L=function(){l.useEffect(function(){return document.body.setAttribute(R,(A()+1).toString()),function(){var e=A()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;L();var i=l.useMemo(function(){return S(o)},[o]);return l.createElement(C,{styles:T(i,!t,o,n?"":"!important")})},k=!1;if("undefined"!=typeof window)try{var N=Object.defineProperty({},"passive",{get:function(){return k=!0,!0}});window.addEventListener("test",N,N),window.removeEventListener("test",N,N)}catch(e){k=!1}var j=!!k&&{passive:!1},D=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},O=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),M(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},M=function(e,t){return"v"===e?D(t,"overflowY"):D(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},W=function(e,t,n,r,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*r,u=n.target,c=t.contains(u),s=!1,d=a>0,f=0,p=0;do{if(!u)break;var v=I(e,u),h=v[0],m=v[1]-v[2]-l*h;(h||m)&&M(e,u)&&(f+=m,p+=h);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&a>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(s=!0),s},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},_=0,z=[];let V=(p.useMedium(function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(_++)[0],i=l.useState(w)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=F(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=O(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=O(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return W(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(z.length&&z[z.length-1]===i){var n="deltaY"in e?H(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=F(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,H(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,F(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return z.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,j),document.addEventListener("touchmove",c,j),document.addEventListener("touchstart",d,j),function(){z=z.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,j),document.removeEventListener("touchmove",c,j),document.removeEventListener("touchstart",d,j)}},[]);var v=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,v?l.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),m);var G=l.forwardRef(function(e,t){return l.createElement(h,o({},e,{ref:t,sideCar:V}))});G.classNames=h.classNames;let K=G}}]);