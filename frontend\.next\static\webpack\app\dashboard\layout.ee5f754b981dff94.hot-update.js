"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/AlarmSettings.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/AlarmSettings.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AlarmSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_slider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/slider */ \"(app-pages-browser)/./src/components/ui/slider.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Settings,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Settings,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Settings,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Available ringtone options from frontend/ringtones folder\nconst RINGTONE_OPTIONS = [\n    {\n        value: 'G_hades_curse.wav',\n        label: 'Hades Curse'\n    },\n    {\n        value: 'G_hades_demat.wav',\n        label: 'Hades Demat'\n    },\n    {\n        value: 'G_hades_mat.wav',\n        label: 'Hades Mat'\n    },\n    {\n        value: 'G_hades_sanctify.wav',\n        label: 'Hades Sanctify'\n    },\n    {\n        value: 'S_mon1.mp3',\n        label: 'Monster 1'\n    },\n    {\n        value: 'S_mon2.mp3',\n        label: 'Monster 2'\n    },\n    {\n        value: 'Satyr_atk4.wav',\n        label: 'Satyr Attack'\n    },\n    {\n        value: 'bells.wav',\n        label: 'Bells'\n    },\n    {\n        value: 'bird1.wav',\n        label: 'Bird 1'\n    },\n    {\n        value: 'bird7.wav',\n        label: 'Bird 7'\n    },\n    {\n        value: 'cheer.wav',\n        label: 'Cheer'\n    },\n    {\n        value: 'chest1.wav',\n        label: 'Chest'\n    },\n    {\n        value: 'chime2.wav',\n        label: 'Chime'\n    },\n    {\n        value: 'dark2.wav',\n        label: 'Dark'\n    },\n    {\n        value: 'foundry2.wav',\n        label: 'Foundry'\n    },\n    {\n        value: 'goatherd1.wav',\n        label: 'Goatherd'\n    },\n    {\n        value: 'marble1.wav',\n        label: 'Marble'\n    },\n    {\n        value: 'sanctuary1.wav',\n        label: 'Sanctuary'\n    },\n    {\n        value: 'space_bells4a.wav',\n        label: 'Space Bells'\n    },\n    {\n        value: 'sparrow1.wav',\n        label: 'Sparrow'\n    },\n    {\n        value: 'tax3.wav',\n        label: 'Tax'\n    },\n    {\n        value: 'wolf4.wav',\n        label: 'Wolf'\n    }\n];\nfunction AlarmSettings(param) {\n    let { isOpen, onClose } = param;\n    _s();\n    const { sessionAlarmConfig, dispatch, playSessionAlarm } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const [localConfig, setLocalConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(sessionAlarmConfig);\n    const handleSave = ()=>{\n        dispatch({\n            type: 'SET_SESSION_ALARM_CONFIG',\n            payload: localConfig\n        });\n        toast({\n            title: \"Alarm Settings Saved\",\n            description: \"Your session-specific alarm settings have been updated.\",\n            duration: 3000\n        });\n        onClose();\n    };\n    const handleTestSound = (soundFile)=>{\n        try {\n            const audio = new Audio(\"/ringtones/\".concat(soundFile));\n            audio.volume = localConfig.volume / 100;\n            audio.play().catch(console.error);\n        } catch (error) {\n            console.error('Failed to play test sound:', error);\n            toast({\n                title: \"Sound Test Failed\",\n                description: \"Could not play the selected sound file.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n            className: \"sm:max-w-[500px] max-h-[80vh]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                \"Session Alarm Settings\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                            children: \"Configure custom alarm sounds and settings for this trading session.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6 overflow-y-auto max-h-[60vh] pr-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Volume Control\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                children: [\n                                                    \"Volume: \",\n                                                    localConfig.volume,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_8__.Slider, {\n                                                value: [\n                                                    localConfig.volume\n                                                ],\n                                                onValueChange: (value)=>setLocalConfig({\n                                                        ...localConfig,\n                                                        volume: value[0]\n                                                    }),\n                                                max: 100,\n                                                min: 0,\n                                                step: 5,\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"Buy Order Alarms\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                    id: \"buyAlarmEnabled\",\n                                                    checked: localConfig.buyAlarmEnabled,\n                                                    onCheckedChange: (checked)=>setLocalConfig({\n                                                            ...localConfig,\n                                                            buyAlarmEnabled: checked\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"buyAlarmEnabled\",\n                                                    children: \"Enable buy order alarms\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    children: \"Buy Alarm Sound\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                            value: localConfig.buyAlarmSound,\n                                                            onValueChange: (value)=>setLocalConfig({\n                                                                    ...localConfig,\n                                                                    buyAlarmSound: value\n                                                                }),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                        lineNumber: 140,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                    children: RINGTONE_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: option.value,\n                                                                            children: option.label\n                                                                        }, option.value, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                            lineNumber: 144,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                    lineNumber: 142,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>handleTestSound(localConfig.buyAlarmSound),\n                                                            disabled: !localConfig.buyAlarmEnabled,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm text-red-600\",\n                                        children: \"Sell Order Alarms\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                    id: \"sellAlarmEnabled\",\n                                                    checked: localConfig.sellAlarmEnabled,\n                                                    onCheckedChange: (checked)=>setLocalConfig({\n                                                            ...localConfig,\n                                                            sellAlarmEnabled: checked\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"sellAlarmEnabled\",\n                                                    children: \"Enable sell order alarms\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    children: \"Sell Alarm Sound\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                            value: localConfig.sellAlarmSound,\n                                                            onValueChange: (value)=>setLocalConfig({\n                                                                    ...localConfig,\n                                                                    sellAlarmSound: value\n                                                                }),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                        lineNumber: 188,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                    children: RINGTONE_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: option.value,\n                                                                            children: option.label\n                                                                        }, option.value, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>handleTestSound(localConfig.sellAlarmSound),\n                                                            disabled: !localConfig.sellAlarmEnabled,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleSave,\n                                    className: \"btn-neo\",\n                                    children: \"Save Settings\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(AlarmSettings, \"LTcPcqDkqCuWerGDBcVvA00BCAE=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = AlarmSettings;\nvar _c;\n$RefreshReg$(_c, \"AlarmSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/AlarmSettings.tsx\n"));

/***/ })

});