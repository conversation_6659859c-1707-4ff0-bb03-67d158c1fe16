"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useTradingContext } from '@/contexts/TradingContext';
import { useToast } from '@/hooks/use-toast';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Settings, BellRing, KeyRound, Bot, FileText, Home, Eye, EyeOff } from 'lucide-react';
import { SessionManager } from '@/components/admin/SessionManager';

export default function AdminPanelPage() {
  const { botSystemStatus } = useTradingContext();
  const [apiKey, setApiKey] = useState('iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA');
  const [apiSecret, setApiSecret] = useState('jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp');
  const [showApiKey, setShowApiKey] = useState(false);
  const [showApiSecret, setShowApiSecret] = useState(false);
  const [telegramToken, setTelegramToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');
  const { toast } = useToast();
  const router = useRouter();

  const handleSaveApiKeys = async () => {
    try {
      localStorage.setItem('binance_api_key', apiKey);
      localStorage.setItem('binance_api_secret', apiSecret);
      console.log("API Keys Saved:", { apiKey: apiKey.substring(0, 10) + '...', apiSecret: apiSecret.substring(0, 10) + '...' });
      toast({ title: "API Keys Saved", description: "Binance API keys have been saved securely." });
    } catch (error) {
      toast({ title: "Error", description: "Failed to save API keys.", variant: "destructive" });
    }
  };

  const handleTestApiConnection = async () => {
    try {
      const response = await fetch('https://api.binance.com/api/v3/ping');
      if (response.ok) {
        toast({ title: "API Connection Test", description: "Successfully connected to Binance API!" });
      } else {
        toast({ title: "Connection Failed", description: "Unable to connect to Binance API.", variant: "destructive" });
      }
    } catch (error) {
      toast({ title: "Connection Error", description: "Network error while testing API connection.", variant: "destructive" });
    }
  };

  const handleSaveTelegramConfig = () => {
    try {
      localStorage.setItem('telegram_bot_token', telegramToken);
      localStorage.setItem('telegram_chat_id', telegramChatId);
      console.log("Telegram Config Saved:", { telegramToken: telegramToken.substring(0, 10) + '...', telegramChatId });
      toast({ title: "Telegram Config Saved", description: "Telegram settings have been saved successfully." });
    } catch (error) {
      toast({ title: "Error", description: "Failed to save Telegram configuration.", variant: "destructive" });
    }
  };

  const handleTestTelegram = async () => {
    if (!telegramToken || !telegramChatId) {
      toast({ title: "Missing Configuration", description: "Please enter both Telegram bot token and chat ID.", variant: "destructive" });
      return;
    }

    try {
      const response = await fetch(`https://api.telegram.org/bot${telegramToken}/sendMessage`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chat_id: telegramChatId,
          text: '🤖 Test message from Pluto Trading Bot! Your Telegram integration is working correctly.'
        })
      });

      if (response.ok) {
        toast({ title: "Telegram Test Successful", description: "Test message sent successfully!" });
      } else {
        toast({ title: "Telegram Test Failed", description: "Failed to send test message. Check your token and chat ID.", variant: "destructive" });
      }
    } catch (error) {
      toast({ title: "Telegram Error", description: "Network error while testing Telegram integration.", variant: "destructive" });
    }
  };

  const adminTabs = [
    { value: "systemTools", label: "System Tools", icon: <Settings className="mr-2 h-4 w-4" /> },
    { value: "apiKeys", label: "Exchange API Keys", icon: <KeyRound className="mr-2 h-4 w-4" /> },
    { value: "telegram", label: "Telegram Integration", icon: <Bot className="mr-2 h-4 w-4" /> },
    { value: "sessionManager", label: "Session Manager", icon: <FileText className="mr-2 h-4 w-4" /> }
  ];

  return (
    <div className="container mx-auto py-8 px-4">
      <Card className="border-2 border-border">
        <CardHeader className="flex flex-row justify-between items-center">
          <div>
            <CardTitle className="text-3xl font-bold text-primary">Admin Panel</CardTitle>
            <CardDescription>Manage global settings and tools for Pluto Trading Bot.</CardDescription>
          </div>
          <Button variant="outline" onClick={() => router.push('/dashboard')} className="btn-outline-neo">
            <Home className="mr-2 h-4 w-4" />
            Return to Dashboard
          </Button>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="systemTools" className="w-full">
            <ScrollArea className="pb-2">
              <TabsList className="grid w-full grid-cols-4 mb-6">
                {adminTabs.map(tab => (
                   <TabsTrigger key={tab.value} value={tab.value} className="px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                     <div className="flex items-center">{tab.icon} {tab.label}</div>
                   </TabsTrigger>
                ))}
              </TabsList>
            </ScrollArea>

            <TabsContent value="systemTools" className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-primary">System Management Tools</h3>
                <div className="grid grid-cols-2 gap-4">
                  <Button variant="outline" className="btn-outline-neo" onClick={() => toast({title: "DB Editor Clicked"})}>View Database (Read-Only)</Button>
                  <Button variant="outline" className="btn-outline-neo" onClick={() => toast({title: "Export Orders Clicked"})}>Export Orders to Excel</Button>
                  <Button variant="outline" className="btn-outline-neo" onClick={() => toast({title: "Export History Clicked"})}>Export History to Excel</Button>
                  <Button variant="outline" className="btn-outline-neo" onClick={() => toast({title: "Backup DB Clicked"})}>Backup Database</Button>
                  <Button variant="outline" className="btn-outline-neo" onClick={() => toast({title: "Restore DB Clicked"})} disabled>Restore Database</Button>
                  <Button variant="outline" className="btn-outline-neo" onClick={() => toast({title: "Diagnostics Clicked"})}>Run System Diagnostics</Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="apiKeys" className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-primary">Exchange API Configuration</h3>
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="apiKey">Binance API Key</Label>
                    <div className="relative">
                      <Input
                        id="apiKey"
                        placeholder="Enter your Binance API Key"
                        type={showApiKey ? "text" : "password"}
                        value={apiKey}
                        onChange={(e) => setApiKey(e.target.value)}
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowApiKey(!showApiKey)}
                      >
                        {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="apiSecret">Binance API Secret</Label>
                    <div className="relative">
                      <Input
                        id="apiSecret"
                        placeholder="Enter your Binance API Secret"
                        type={showApiSecret ? "text" : "password"}
                        value={apiSecret}
                        onChange={(e) => setApiSecret(e.target.value)}
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowApiSecret(!showApiSecret)}
                      >
                        {showApiSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleSaveApiKeys} className="btn-neo">Save API Keys</Button>
                    <Button onClick={handleTestApiConnection} variant="outline" className="btn-outline-neo">Test Connection</Button>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="telegram" className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-primary">Telegram Bot Integration</h3>
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="telegramToken">Telegram Bot Token</Label>
                    <Input
                      id="telegramToken"
                      placeholder="Enter your Telegram Bot Token"
                      type="password"
                      value={telegramToken}
                      onChange={(e) => setTelegramToken(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="telegramChatId">Telegram Chat ID</Label>
                    <Input
                      id="telegramChatId"
                      placeholder="Enter your Telegram Chat ID"
                      type="text"
                      value={telegramChatId}
                      onChange={(e) => setTelegramChatId(e.target.value)}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={handleSaveTelegramConfig} className="btn-neo flex-1">Save Telegram Config</Button>
                    <Button onClick={handleTestTelegram} variant="outline" className="btn-outline-neo flex-1">Test Telegram</Button>
                  </div>
                </div>

                <div className="bg-muted p-4 rounded-lg">
                  <h4 className="font-semibold mb-2">Setup Guide:</h4>
                  <ol className="list-decimal list-inside space-y-1 text-sm">
                    <li>Create a new bot by messaging @BotFather on Telegram</li>
                    <li>Use the command /newbot and follow the instructions</li>
                    <li>Copy the bot token provided by BotFather</li>
                    <li>Start a chat with your bot and send any message</li>
                    <li>Get your chat ID by visiting: https://api.telegram.org/bot[YOUR_BOT_TOKEN]/getUpdates</li>
                    <li>Enter both the bot token and chat ID above, then test the connection</li>
                  </ol>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="sessionManager" className="space-y-6">
              <SessionManager />
            </TabsContent>

          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
