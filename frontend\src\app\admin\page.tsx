'use client';

import React from 'react';
import { SessionManager } from '@/components/admin/SessionManager';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useTradingContext } from '@/contexts/TradingContext';
import { useToast } from '@/hooks/use-toast';
import { 
  Settings, 
  Database, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  Activity
} from 'lucide-react';

export default function AdminPanel() {
  const { 
    backendStatus, 
    botSystemStatus, 
    resetGlobalBalances,
    crypto1Balance,
    crypto2Balance,
    stablecoinBalance,
    totalProfitLoss
  } = useTradingContext();
  const { toast } = useToast();

  const handleResetBalances = () => {
    const result = resetGlobalBalances();
    toast({
      title: "Balances Reset",
      description: `Global balances have been reset to defaults: ${result.crypto1Balance} BTC, ${result.crypto2Balance} USDT`,
      variant: "default"
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
      case 'Running':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'offline':
      case 'Stopped':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'Running':
        return 'bg-green-500/10 text-green-500 border-green-500/20';
      case 'offline':
      case 'Stopped':
        return 'bg-red-500/10 text-red-500 border-red-500/20';
      default:
        return 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-3 mb-6">
        <Settings className="h-8 w-8" />
        <div>
          <h1 className="text-3xl font-bold">Admin Panel</h1>
          <p className="text-muted-foreground">System management and configuration</p>
        </div>
      </div>

      {/* System Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              System Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Backend Connection</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(backendStatus)}
                <Badge className={getStatusColor(backendStatus)}>
                  {backendStatus}
                </Badge>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Trading Bot</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(botSystemStatus)}
                <Badge className={getStatusColor(botSystemStatus)}>
                  {botSystemStatus}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Global Balance Management
            </CardTitle>
            <CardDescription>
              Reset global balances to default values
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Crypto1:</span>
                <span className="ml-2 font-mono">{crypto1Balance}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Crypto2:</span>
                <span className="ml-2 font-mono">{crypto2Balance}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Stablecoin:</span>
                <span className="ml-2 font-mono">{stablecoinBalance}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Total P/L:</span>
                <span className={`ml-2 font-mono ${totalProfitLoss >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                  {totalProfitLoss >= 0 ? '+' : ''}{totalProfitLoss.toFixed(2)}
                </span>
              </div>
            </div>
            <Button 
              onClick={handleResetBalances}
              variant="outline"
              className="w-full"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Reset Global Balances
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Session Manager */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Session Manager
          </CardTitle>
          <CardDescription>
            Manage trading sessions, save/load configurations, and export data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SessionManager />
        </CardContent>
      </Card>
    </div>
  );
}
