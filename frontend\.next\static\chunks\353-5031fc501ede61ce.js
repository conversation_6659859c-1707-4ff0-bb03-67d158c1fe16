(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[353],{2564:(e,t,r)=>{"use strict";r.d(t,{Qg:()=>i,s6:()=>a});var n=r(12115),o=r(63655),s=r(95155),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=n.forwardRef((e,t)=>(0,s.jsx)(o.sG.span,{...e,ref:t,style:{...i,...e.style}}));a.displayName="VisuallyHidden"},19178:(e,t,r)=>{"use strict";r.d(t,{lg:()=>y,qW:()=>v,bL:()=>w});var n,o=r(12115),s=r(85185),i=r(63655),a=r(6101),l=r(39033),u=r(95155),d="dismissableLayer.update",c=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),v=o.forwardRef((e,t)=>{var r,v;let{disableOutsidePointerEvents:f=!1,onEscapeKeyDown:w,onPointerDownOutside:y,onFocusOutside:E,onInteractOutside:h,onDismiss:b,...x}=e,g=o.useContext(c),[T,C]=o.useState(null),P=null!==(v=null==T?void 0:T.ownerDocument)&&void 0!==v?v:null===(r=globalThis)||void 0===r?void 0:r.document,[,L]=o.useState({}),R=(0,a.s)(t,e=>C(e)),D=Array.from(g.layers),[S]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),N=D.indexOf(S),j=T?D.indexOf(T):-1,k=g.layersWithOutsidePointerEventsDisabled.size>0,F=j>=N,A=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,l.c)(e),s=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!s.current){let t=function(){m("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",i.current),i.current=t,r.addEventListener("click",i.current,{once:!0})):t()}else r.removeEventListener("click",i.current);s.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",i.current)}},[r,n]),{onPointerDownCapture:()=>s.current=!0}}(e=>{let t=e.target,r=[...g.branches].some(e=>e.contains(t));!F||r||(null==y||y(e),null==h||h(e),e.defaultPrevented||null==b||b())},P),O=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,l.c)(e),s=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!s.current&&m("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>s.current=!0,onBlurCapture:()=>s.current=!1}}(e=>{let t=e.target;[...g.branches].some(e=>e.contains(t))||(null==E||E(e),null==h||h(e),e.defaultPrevented||null==b||b())},P);return!function(e,t=globalThis?.document){let r=(0,l.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{j===g.layers.size-1&&(null==w||w(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},P),o.useEffect(()=>{if(T)return f&&(0===g.layersWithOutsidePointerEventsDisabled.size&&(n=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(T)),g.layers.add(T),p(),()=>{f&&1===g.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=n)}},[T,P,f,g]),o.useEffect(()=>()=>{T&&(g.layers.delete(T),g.layersWithOutsidePointerEventsDisabled.delete(T),p())},[T,g]),o.useEffect(()=>{let e=()=>L({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,u.jsx)(i.sG.div,{...x,ref:R,style:{pointerEvents:k?F?"auto":"none":void 0,...e.style},onFocusCapture:(0,s.m)(e.onFocusCapture,O.onFocusCapture),onBlurCapture:(0,s.m)(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:(0,s.m)(e.onPointerDownCapture,A.onPointerDownCapture)})});v.displayName="DismissableLayer";var f=o.forwardRef((e,t)=>{let r=o.useContext(c),n=o.useRef(null),s=(0,a.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(i.sG.div,{...e,ref:s})});function p(){let e=new CustomEvent(d);document.dispatchEvent(e)}function m(e,t,r,n){let{discrete:o}=n,s=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),o?(0,i.hO)(s,a):s.dispatchEvent(a)}f.displayName="DismissableLayerBranch";var w=v,y=f},25318:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},26621:(e,t,r)=>{"use strict";r.d(t,{Kq:()=>Z,LM:()=>Q,VY:()=>ee,bL:()=>J,bm:()=>er,hE:()=>$,rc:()=>et});var n=r(12115),o=r(47650),s=r(85185),i=r(6101),a=r(37328),l=r(46081),u=r(19178),d=r(34378),c=r(28905),v=r(63655),f=r(39033),p=r(5845),m=r(52712),w=r(2564),y=r(95155),E="ToastProvider",[h,b,x]=(0,a.N)("Toast"),[g,T]=(0,l.A)("Toast",[x]),[C,P]=g(E),L=e=>{let{__scopeToast:t,label:r="Notification",duration:o=5e3,swipeDirection:s="right",swipeThreshold:i=50,children:a}=e,[l,u]=n.useState(null),[d,c]=n.useState(0),v=n.useRef(!1),f=n.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(E,"`. Expected non-empty `string`.")),(0,y.jsx)(h.Provider,{scope:t,children:(0,y.jsx)(C,{scope:t,label:r,duration:o,swipeDirection:s,swipeThreshold:i,toastCount:d,viewport:l,onViewportChange:u,onToastAdd:n.useCallback(()=>c(e=>e+1),[]),onToastRemove:n.useCallback(()=>c(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:v,isClosePausedRef:f,children:a})})};L.displayName=E;var R="ToastViewport",D=["F8"],S="toast.viewportPause",N="toast.viewportResume",j=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:o=D,label:s="Notifications ({hotkey})",...a}=e,l=P(R,r),d=b(r),c=n.useRef(null),f=n.useRef(null),p=n.useRef(null),m=n.useRef(null),w=(0,i.s)(t,m,l.onViewportChange),E=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=l.toastCount>0;n.useEffect(()=>{let e=e=>{var t;0!==o.length&&o.every(t=>e[t]||e.code===t)&&(null===(t=m.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),n.useEffect(()=>{let e=c.current,t=m.current;if(x&&e&&t){let r=()=>{if(!l.isClosePausedRef.current){let e=new CustomEvent(S);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},n=()=>{if(l.isClosePausedRef.current){let e=new CustomEvent(N);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},s=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",s),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",s),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[x,l.isClosePausedRef]);let g=n.useCallback(e=>{let{tabbingDirection:t}=e,r=d().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[d]);return n.useEffect(()=>{let e=m.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,o,s;let r=document.activeElement,i=t.shiftKey;if(t.target===e&&i){null===(n=f.current)||void 0===n||n.focus();return}let a=g({tabbingDirection:i?"backwards":"forwards"}),l=a.findIndex(e=>e===r);Y(a.slice(l+1))?t.preventDefault():i?null===(o=f.current)||void 0===o||o.focus():null===(s=p.current)||void 0===s||s.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[d,g]),(0,y.jsxs)(u.lg,{ref:c,role:"region","aria-label":s.replace("{hotkey}",E),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&(0,y.jsx)(F,{ref:f,onFocusFromOutsideViewport:()=>{Y(g({tabbingDirection:"forwards"}))}}),(0,y.jsx)(h.Slot,{scope:r,children:(0,y.jsx)(v.sG.ol,{tabIndex:-1,...a,ref:w})}),x&&(0,y.jsx)(F,{ref:p,onFocusFromOutsideViewport:()=>{Y(g({tabbingDirection:"backwards"}))}})]})});j.displayName=R;var k="ToastFocusProxy",F=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,s=P(k,r);return(0,y.jsx)(w.s6,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=s.viewport)||void 0===t?void 0:t.contains(r))||n()}})});F.displayName=k;var A="Toast",O=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:o,onOpenChange:i,...a}=e,[l,u]=(0,p.i)({prop:n,defaultProp:null==o||o,onChange:i,caller:A});return(0,y.jsx)(c.C,{present:r||l,children:(0,y.jsx)(_,{open:l,...a,ref:t,onClose:()=>u(!1),onPause:(0,f.c)(e.onPause),onResume:(0,f.c)(e.onResume),onSwipeStart:(0,s.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,s.m)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,s.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,s.m)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),u(!1)})})})});O.displayName=A;var[M,I]=g(A,{onClose(){}}),_=n.forwardRef((e,t)=>{let{__scopeToast:r,type:a="foreground",duration:l,open:d,onClose:c,onEscapeKeyDown:p,onPause:m,onResume:w,onSwipeStart:E,onSwipeMove:b,onSwipeCancel:x,onSwipeEnd:g,...T}=e,C=P(A,r),[L,R]=n.useState(null),D=(0,i.s)(t,e=>R(e)),j=n.useRef(null),k=n.useRef(null),F=l||C.duration,O=n.useRef(0),I=n.useRef(F),_=n.useRef(0),{onToastAdd:G,onToastRemove:W}=C,V=(0,f.c)(()=>{var e;(null==L?void 0:L.contains(document.activeElement))&&(null===(e=C.viewport)||void 0===e||e.focus()),c()}),z=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(_.current),O.current=new Date().getTime(),_.current=window.setTimeout(V,e))},[V]);n.useEffect(()=>{let e=C.viewport;if(e){let t=()=>{z(I.current),null==w||w()},r=()=>{let e=new Date().getTime()-O.current;I.current=I.current-e,window.clearTimeout(_.current),null==m||m()};return e.addEventListener(S,r),e.addEventListener(N,t),()=>{e.removeEventListener(S,r),e.removeEventListener(N,t)}}},[C.viewport,F,m,w,z]),n.useEffect(()=>{d&&!C.isClosePausedRef.current&&z(F)},[d,F,C.isClosePausedRef,z]),n.useEffect(()=>(G(),()=>W()),[G,W]);let B=n.useMemo(()=>L?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{var n;if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),(n=t).nodeType===n.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(L):null,[L]);return C.viewport?(0,y.jsxs)(y.Fragment,{children:[B&&(0,y.jsx)(K,{__scopeToast:r,role:"status","aria-live":"foreground"===a?"assertive":"polite","aria-atomic":!0,children:B}),(0,y.jsx)(M,{scope:r,onClose:V,children:o.createPortal((0,y.jsx)(h.ItemSlot,{scope:r,children:(0,y.jsx)(u.bL,{asChild:!0,onEscapeKeyDown:(0,s.m)(p,()=>{C.isFocusedToastEscapeKeyDownRef.current||V(),C.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,y.jsx)(v.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":d?"open":"closed","data-swipe-direction":C.swipeDirection,...T,ref:D,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,s.m)(e.onKeyDown,e=>{"Escape"!==e.key||(null==p||p(e.nativeEvent),e.nativeEvent.defaultPrevented||(C.isFocusedToastEscapeKeyDownRef.current=!0,V()))}),onPointerDown:(0,s.m)(e.onPointerDown,e=>{0===e.button&&(j.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,s.m)(e.onPointerMove,e=>{if(!j.current)return;let t=e.clientX-j.current.x,r=e.clientY-j.current.y,n=!!k.current,o=["left","right"].includes(C.swipeDirection),s=["left","up"].includes(C.swipeDirection)?Math.min:Math.max,i=o?s(0,t):0,a=o?0:s(0,r),l="touch"===e.pointerType?10:2,u={x:i,y:a},d={originalEvent:e,delta:u};n?(k.current=u,X("toast.swipeMove",b,d,{discrete:!1})):H(u,C.swipeDirection,l)?(k.current=u,X("toast.swipeStart",E,d,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(j.current=null)}),onPointerUp:(0,s.m)(e.onPointerUp,e=>{let t=k.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),k.current=null,j.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};H(t,C.swipeDirection,C.swipeThreshold)?X("toast.swipeEnd",g,n,{discrete:!0}):X("toast.swipeCancel",x,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),C.viewport)})]}):null}),K=e=>{let{__scopeToast:t,children:r,...o}=e,s=P(A,t),[i,a]=n.useState(!1),[l,u]=n.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,f.c)(e);(0,m.N)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>a(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,y.jsx)(d.Z,{asChild:!0,children:(0,y.jsx)(w.s6,{...o,children:i&&(0,y.jsxs)(y.Fragment,{children:[s.label," ",r]})})})},G=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,y.jsx)(v.sG.div,{...n,ref:t})});G.displayName="ToastTitle";var W=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,y.jsx)(v.sG.div,{...n,ref:t})});W.displayName="ToastDescription";var V="ToastAction",z=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,y.jsx)(q,{altText:r,asChild:!0,children:(0,y.jsx)(U,{...n,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(V,"`. Expected non-empty `string`.")),null)});z.displayName=V;var B="ToastClose",U=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,o=I(B,r);return(0,y.jsx)(q,{asChild:!0,children:(0,y.jsx)(v.sG.button,{type:"button",...n,ref:t,onClick:(0,s.m)(e.onClick,o.onClose)})})});U.displayName=B;var q=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...o}=e;return(0,y.jsx)(v.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function X(e,t,r,n){let{discrete:o}=n,s=r.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),o?(0,v.hO)(s,i):s.dispatchEvent(i)}var H=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),o=Math.abs(e.y),s=n>o;return"left"===t||"right"===t?s&&n>r:!s&&o>r};function Y(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var Z=L,Q=j,J=O,$=G,ee=W,et=z,er=U},31383:e=>{e.exports={style:{fontFamily:"'GeistSans', 'GeistSans Fallback'"},className:"__className_fb8f2c",variable:"__variable_fb8f2c"}},34378:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(12115),o=r(47650),s=r(63655),i=r(52712),a=r(95155),l=n.forwardRef((e,t)=>{var r,l;let{container:u,...d}=e,[c,v]=n.useState(!1);(0,i.N)(()=>v(!0),[]);let f=u||c&&(null===(l=globalThis)||void 0===l?void 0:null===(r=l.document)||void 0===r?void 0:r.body);return f?o.createPortal((0,a.jsx)(s.sG.div,{...d,ref:t}),f):null});l.displayName="Portal"},34477:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return n.callServer},createServerReference:function(){return s},findSourceMapURL:function(){return o.findSourceMapURL}});let n=r(53806),o=r(31818),s=r(34979).createServerReference},50172:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(40157).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])}}]);