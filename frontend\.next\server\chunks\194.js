"use strict";exports.id=194,exports.ids=[194],exports.modules={43:(e,r,t)=>{t.d(r,{jH:()=>l});var o=t(43210);t(60687);var n=o.createContext(void 0);function l(e){let r=o.useContext(n);return e||r||"ltr"}},15036:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(82614).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},24026:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(82614).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},41360:(e,r,t)=>{t.d(r,{UC:()=>$,B8:()=>X,bL:()=>K,l9:()=>Y});var o=t(43210),n=t(70569),l=t(11273),a=t(9510),i=t(98599),s=t(96963),c=t(14163),u=t(13495),d=t(65551),f=t(43),p=t(60687),v="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[m,b,g]=(0,a.N)(w),[y,S]=(0,l.A)(w,[g]),[x,C]=y(w),E=o.forwardRef((e,r)=>(0,p.jsx)(m.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(m.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(R,{...e,ref:r})})}));E.displayName=w;var R=o.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:l,loop:a=!1,dir:s,currentTabStopId:m,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:y,onEntryFocus:S,preventScrollOnEntryFocus:C=!1,...E}=e,R=o.useRef(null),T=(0,i.s)(r,R),A=(0,f.jH)(s),[j,D]=(0,d.i)({prop:m,defaultProp:g??null,onChange:y,caller:w}),[P,I]=o.useState(!1),_=(0,u.c)(S),N=b(t),F=o.useRef(!1),[H,k]=o.useState(0);return o.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(v,_),()=>e.removeEventListener(v,_)},[_]),(0,p.jsx)(x,{scope:t,orientation:l,dir:A,loop:a,currentTabStopId:j,onItemFocus:o.useCallback(e=>D(e),[D]),onItemShiftTab:o.useCallback(()=>I(!0),[]),onFocusableItemAdd:o.useCallback(()=>k(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>k(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:P||0===H?-1:0,"data-orientation":l,...E,ref:T,style:{outline:"none",...e.style},onMouseDown:(0,n.m)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,n.m)(e.onFocus,e=>{let r=!F.current;if(e.target===e.currentTarget&&r&&!P){let r=new CustomEvent(v,h);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=N().filter(e=>e.focusable);L([e.find(e=>e.active),e.find(e=>e.id===j),...e].filter(Boolean).map(e=>e.ref.current),C)}}F.current=!1}),onBlur:(0,n.m)(e.onBlur,()=>I(!1))})})}),T="RovingFocusGroupItem",A=o.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:l=!0,active:a=!1,tabStopId:i,children:u,...d}=e,f=(0,s.B)(),v=i||f,h=C(T,t),w=h.currentTabStopId===v,g=b(t),{onFocusableItemAdd:y,onFocusableItemRemove:S,currentTabStopId:x}=h;return o.useEffect(()=>{if(l)return y(),()=>S()},[l,y,S]),(0,p.jsx)(m.ItemSlot,{scope:t,id:v,focusable:l,active:a,children:(0,p.jsx)(c.sG.span,{tabIndex:w?0:-1,"data-orientation":h.orientation,...d,ref:r,onMouseDown:(0,n.m)(e.onMouseDown,e=>{l?h.onItemFocus(v):e.preventDefault()}),onFocus:(0,n.m)(e.onFocus,()=>h.onItemFocus(v)),onKeyDown:(0,n.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let r=function(e,r,t){var o;let n=(o=e.key,"rtl"!==t?o:"ArrowLeft"===o?"ArrowRight":"ArrowRight"===o?"ArrowLeft":o);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(n)))return j[n]}(e,h.orientation,h.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)t.reverse();else if("prev"===r||"next"===r){"prev"===r&&t.reverse();let o=t.indexOf(e.currentTarget);t=h.loop?function(e,r){return e.map((t,o)=>e[(r+o)%e.length])}(t,o+1):t.slice(o+1)}setTimeout(()=>L(t))}}),children:"function"==typeof u?u({isCurrentTabStop:w,hasTabStop:null!=x}):u})})});A.displayName=T;var j={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function L(e,r=!1){let t=document.activeElement;for(let o of e)if(o===t||(o.focus({preventScroll:r}),document.activeElement!==t))return}var D=t(46059),P="Tabs",[I,_]=(0,l.A)(P,[S]),N=S(),[F,H]=I(P),k=o.forwardRef((e,r)=>{let{__scopeTabs:t,value:o,onValueChange:n,defaultValue:l,orientation:a="horizontal",dir:i,activationMode:u="automatic",...v}=e,h=(0,f.jH)(i),[w,m]=(0,d.i)({prop:o,onChange:n,defaultProp:l??"",caller:P});return(0,p.jsx)(F,{scope:t,baseId:(0,s.B)(),value:w,onValueChange:m,orientation:a,dir:h,activationMode:u,children:(0,p.jsx)(c.sG.div,{dir:h,"data-orientation":a,...v,ref:r})})});k.displayName=P;var z="TabsList",M=o.forwardRef((e,r)=>{let{__scopeTabs:t,loop:o=!0,...n}=e,l=H(z,t),a=N(t);return(0,p.jsx)(E,{asChild:!0,...a,orientation:l.orientation,dir:l.dir,loop:o,children:(0,p.jsx)(c.sG.div,{role:"tablist","aria-orientation":l.orientation,...n,ref:r})})});M.displayName=z;var G="TabsTrigger",W=o.forwardRef((e,r)=>{let{__scopeTabs:t,value:o,disabled:l=!1,...a}=e,i=H(G,t),s=N(t),u=B(i.baseId,o),d=V(i.baseId,o),f=o===i.value;return(0,p.jsx)(A,{asChild:!0,...s,focusable:!l,active:f,children:(0,p.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:u,...a,ref:r,onMouseDown:(0,n.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(o)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(o)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==i.activationMode;f||l||!e||i.onValueChange(o)})})})});W.displayName=G;var O="TabsContent",U=o.forwardRef((e,r)=>{let{__scopeTabs:t,value:n,forceMount:l,children:a,...i}=e,s=H(O,t),u=B(s.baseId,n),d=V(s.baseId,n),f=n===s.value,v=o.useRef(f);return o.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(D.C,{present:l||f,children:({present:t})=>(0,p.jsx)(c.sG.div,{"data-state":f?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":u,hidden:!t,id:d,tabIndex:0,...i,ref:r,style:{...e.style,animationDuration:v.current?"0s":void 0},children:t&&a})})});function B(e,r){return`${e}-trigger-${r}`}function V(e,r){return`${e}-content-${r}`}U.displayName=O;var K=k,X=M,Y=W,$=U},58369:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(82614).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},67969:(e,r,t)=>{t.d(r,{q:()=>o});function o(e,[r,t]){return Math.min(t,Math.max(r,e))}},68123:(e,r,t)=>{t.d(r,{LM:()=>Y,OK:()=>$,VM:()=>C,bL:()=>X,lr:()=>N});var o=t(43210),n=t(14163),l=t(46059),a=t(11273),i=t(98599),s=t(13495),c=t(43),u=t(66156),d=t(67969),f=t(70569),p=t(60687),v="ScrollArea",[h,w]=(0,a.A)(v),[m,b]=h(v),g=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:l="hover",dir:a,scrollHideDelay:s=600,...u}=e,[d,f]=o.useState(null),[v,h]=o.useState(null),[w,b]=o.useState(null),[g,y]=o.useState(null),[S,x]=o.useState(null),[C,E]=o.useState(0),[R,T]=o.useState(0),[A,j]=o.useState(!1),[L,D]=o.useState(!1),P=(0,i.s)(r,e=>f(e)),I=(0,c.jH)(a);return(0,p.jsx)(m,{scope:t,type:l,dir:I,scrollHideDelay:s,scrollArea:d,viewport:v,onViewportChange:h,content:w,onContentChange:b,scrollbarX:g,onScrollbarXChange:y,scrollbarXEnabled:A,onScrollbarXEnabledChange:j,scrollbarY:S,onScrollbarYChange:x,scrollbarYEnabled:L,onScrollbarYEnabledChange:D,onCornerWidthChange:E,onCornerHeightChange:T,children:(0,p.jsx)(n.sG.div,{dir:I,...u,ref:P,style:{position:"relative","--radix-scroll-area-corner-width":C+"px","--radix-scroll-area-corner-height":R+"px",...e.style}})})});g.displayName=v;var y="ScrollAreaViewport",S=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:l,nonce:a,...s}=e,c=b(y,t),u=o.useRef(null),d=(0,i.s)(r,u,c.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,p.jsx)(n.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:l})})]})});S.displayName=y;var x="ScrollAreaScrollbar",C=o.forwardRef((e,r)=>{let{forceMount:t,...n}=e,l=b(x,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:i}=l,s="horizontal"===e.orientation;return o.useEffect(()=>(s?a(!0):i(!0),()=>{s?a(!1):i(!1)}),[s,a,i]),"hover"===l.type?(0,p.jsx)(E,{...n,ref:r,forceMount:t}):"scroll"===l.type?(0,p.jsx)(R,{...n,ref:r,forceMount:t}):"auto"===l.type?(0,p.jsx)(T,{...n,ref:r,forceMount:t}):"always"===l.type?(0,p.jsx)(A,{...n,ref:r}):null});C.displayName=x;var E=o.forwardRef((e,r)=>{let{forceMount:t,...n}=e,a=b(x,e.__scopeScrollArea),[i,s]=o.useState(!1);return o.useEffect(()=>{let e=a.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),s(!0)},o=()=>{r=window.setTimeout(()=>s(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",o),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",o)}}},[a.scrollArea,a.scrollHideDelay]),(0,p.jsx)(l.C,{present:t||i,children:(0,p.jsx)(T,{"data-state":i?"visible":"hidden",...n,ref:r})})}),R=o.forwardRef((e,r)=>{var t;let{forceMount:n,...a}=e,i=b(x,e.__scopeScrollArea),s="horizontal"===e.orientation,c=V(()=>d("SCROLL_END"),100),[u,d]=(t={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},o.useReducer((e,r)=>t[e][r]??e,"hidden"));return o.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>d("HIDE"),i.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,i.scrollHideDelay,d]),o.useEffect(()=>{let e=i.viewport,r=s?"scrollLeft":"scrollTop";if(e){let t=e[r],o=()=>{let o=e[r];t!==o&&(d("SCROLL"),c()),t=o};return e.addEventListener("scroll",o),()=>e.removeEventListener("scroll",o)}},[i.viewport,s,d,c]),(0,p.jsx)(l.C,{present:n||"hidden"!==u,children:(0,p.jsx)(A,{"data-state":"hidden"===u?"hidden":"visible",...a,ref:r,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),T=o.forwardRef((e,r)=>{let t=b(x,e.__scopeScrollArea),{forceMount:n,...a}=e,[i,s]=o.useState(!1),c="horizontal"===e.orientation,u=V(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;s(c?e:r)}},10);return K(t.viewport,u),K(t.content,u),(0,p.jsx)(l.C,{present:n||i,children:(0,p.jsx)(A,{"data-state":i?"visible":"hidden",...a,ref:r})})}),A=o.forwardRef((e,r)=>{let{orientation:t="vertical",...n}=e,l=b(x,e.__scopeScrollArea),a=o.useRef(null),i=o.useRef(0),[s,c]=o.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=G(s.viewport,s.content),d={...n,sizes:s,onSizesChange:c,hasThumb:!!(u>0&&u<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:e=>i.current=e};function f(e,r){return function(e,r,t,o="ltr"){let n=W(t),l=r||n/2,a=t.scrollbar.paddingStart+l,i=t.scrollbar.size-t.scrollbar.paddingEnd-(n-l),s=t.content-t.viewport;return U([a,i],"ltr"===o?[0,s]:[-1*s,0])(e)}(e,i.current,s,r)}return"horizontal"===t?(0,p.jsx)(j,{...d,ref:r,onThumbPositionChange:()=>{if(l.viewport&&a.current){let e=O(l.viewport.scrollLeft,s,l.dir);a.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=f(e,l.dir))}}):"vertical"===t?(0,p.jsx)(L,{...d,ref:r,onThumbPositionChange:()=>{if(l.viewport&&a.current){let e=O(l.viewport.scrollTop,s);a.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=f(e))}}):null}),j=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:n,...l}=e,a=b(x,e.__scopeScrollArea),[s,c]=o.useState(),u=o.useRef(null),d=(0,i.s)(r,u,a.onScrollbarXChange);return o.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,p.jsx)(I,{"data-orientation":"horizontal",...l,ref:d,sizes:t,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":W(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(a.viewport){let o=a.viewport.scrollLeft+r.deltaX;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{u.current&&a.viewport&&s&&n({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:M(s.paddingLeft),paddingEnd:M(s.paddingRight)}})}})}),L=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:n,...l}=e,a=b(x,e.__scopeScrollArea),[s,c]=o.useState(),u=o.useRef(null),d=(0,i.s)(r,u,a.onScrollbarYChange);return o.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,p.jsx)(I,{"data-orientation":"vertical",...l,ref:d,sizes:t,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":W(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(a.viewport){let o=a.viewport.scrollTop+r.deltaY;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{u.current&&a.viewport&&s&&n({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:M(s.paddingTop),paddingEnd:M(s.paddingBottom)}})}})}),[D,P]=h(x),I=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:l,hasThumb:a,onThumbChange:c,onThumbPointerUp:u,onThumbPointerDown:d,onThumbPositionChange:v,onDragScroll:h,onWheelScroll:w,onResize:m,...g}=e,y=b(x,t),[S,C]=o.useState(null),E=(0,i.s)(r,e=>C(e)),R=o.useRef(null),T=o.useRef(""),A=y.viewport,j=l.content-l.viewport,L=(0,s.c)(w),P=(0,s.c)(v),I=V(m,10);function _(e){R.current&&h({x:e.clientX-R.current.left,y:e.clientY-R.current.top})}return o.useEffect(()=>{let e=e=>{let r=e.target;S?.contains(r)&&L(e,j)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[A,S,j,L]),o.useEffect(P,[l,P]),K(S,I),K(y.content,I),(0,p.jsx)(D,{scope:t,scrollbar:S,hasThumb:a,onThumbChange:(0,s.c)(c),onThumbPointerUp:(0,s.c)(u),onThumbPositionChange:P,onThumbPointerDown:(0,s.c)(d),children:(0,p.jsx)(n.sG.div,{...g,ref:E,style:{position:"absolute",...g.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),R.current=S.getBoundingClientRect(),T.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",y.viewport&&(y.viewport.style.scrollBehavior="auto"),_(e))}),onPointerMove:(0,f.m)(e.onPointerMove,_),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=T.current,y.viewport&&(y.viewport.style.scrollBehavior=""),R.current=null})})})}),_="ScrollAreaThumb",N=o.forwardRef((e,r)=>{let{forceMount:t,...o}=e,n=P(_,e.__scopeScrollArea);return(0,p.jsx)(l.C,{present:t||n.hasThumb,children:(0,p.jsx)(F,{ref:r,...o})})}),F=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:l,...a}=e,s=b(_,t),c=P(_,t),{onThumbPositionChange:u}=c,d=(0,i.s)(r,e=>c.onThumbChange(e)),v=o.useRef(void 0),h=V(()=>{v.current&&(v.current(),v.current=void 0)},100);return o.useEffect(()=>{let e=s.viewport;if(e){let r=()=>{h(),v.current||(v.current=B(e,u),u())};return u(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[s.viewport,h,u]),(0,p.jsx)(n.sG.div,{"data-state":c.hasThumb?"visible":"hidden",...a,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,o=e.clientY-r.top;c.onThumbPointerDown({x:t,y:o})}),onPointerUp:(0,f.m)(e.onPointerUp,c.onThumbPointerUp)})});N.displayName=_;var H="ScrollAreaCorner",k=o.forwardRef((e,r)=>{let t=b(H,e.__scopeScrollArea),o=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&o?(0,p.jsx)(z,{...e,ref:r}):null});k.displayName=H;var z=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,...l}=e,a=b(H,t),[i,s]=o.useState(0),[c,u]=o.useState(0),d=!!(i&&c);return K(a.scrollbarX,()=>{let e=a.scrollbarX?.offsetHeight||0;a.onCornerHeightChange(e),u(e)}),K(a.scrollbarY,()=>{let e=a.scrollbarY?.offsetWidth||0;a.onCornerWidthChange(e),s(e)}),d?(0,p.jsx)(n.sG.div,{...l,ref:r,style:{width:i,height:c,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function M(e){return e?parseInt(e,10):0}function G(e,r){let t=e/r;return isNaN(t)?0:t}function W(e){let r=G(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function O(e,r,t="ltr"){let o=W(r),n=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,l=r.scrollbar.size-n,a=r.content-r.viewport,i=(0,d.q)(e,"ltr"===t?[0,a]:[-1*a,0]);return U([0,a],[0,l-o])(i)}function U(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let o=(r[1]-r[0])/(e[1]-e[0]);return r[0]+o*(t-e[0])}}var B=(e,r=()=>{})=>{let t={left:e.scrollLeft,top:e.scrollTop},o=0;return function n(){let l={left:e.scrollLeft,top:e.scrollTop},a=t.left!==l.left,i=t.top!==l.top;(a||i)&&r(),t=l,o=window.requestAnimationFrame(n)}(),()=>window.cancelAnimationFrame(o)};function V(e,r){let t=(0,s.c)(e),n=o.useRef(0);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),o.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(t,r)},[t,r])}function K(e,r){let t=(0,s.c)(r);(0,u.N)(()=>{let r=0;if(e){let o=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return o.observe(e),()=>{window.cancelAnimationFrame(r),o.unobserve(e)}}},[e,t])}var X=g,Y=S,$=k},96963:(e,r,t)=>{t.d(r,{B:()=>s});var o,n=t(43210),l=t(66156),a=(o||(o=t.t(n,2)))[" useId ".trim().toString()]||(()=>void 0),i=0;function s(e){let[r,t]=n.useState(a());return(0,l.N)(()=>{e||t(e=>e??String(i++))},[e]),e||(r?`radix-${r}`:"")}}};