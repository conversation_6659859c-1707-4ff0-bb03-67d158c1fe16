(()=>{var e={};e.id=610,e.ids=[610],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68291:(e,t,s)=>{Promise.resolve().then(s.bind(s,68601))},68601:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var r=s(60687),i=s(43210),a=s(37079),o=s(70428),n=s(44493),d=s(29523),l=s(15079),c=s(96834),p=s(35950),x=s(78895),m=s(5551),h=s(29867),y=s(82614);let u=(0,y.A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),f=(0,y.A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var b=s(15036),j=s(43010);function v(){let{dispatch:e,orderHistory:t,config:s}=(0,x.U)(),{toast:a}=(0,h.dj)(),[o,y]=(0,i.useState)([]),[b,v]=(0,i.useState)("current"),[N,C]=(0,i.useState)([]),P=m.C.getInstance(),k="current"===b?{name:"Current Session",pair:`${s.crypto1}/${s.crypto2}`,totalTrades:t.length,totalProfitLoss:t.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0),lastModified:Date.now(),isActive:!0}:o.find(e=>e.id===b);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(n.Zp,{className:"border-2 border-border",children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{className:"text-xl font-bold text-primary",children:"Session History"}),(0,r.jsx)(n.BT,{children:"View trading history for current and past sessions."})]}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Select Session:"}),(0,r.jsxs)(l.l6,{value:b,onValueChange:v,children:[(0,r.jsx)(l.bq,{className:"w-full sm:w-[300px]",children:(0,r.jsx)(l.yv,{placeholder:"Select a session"})}),(0,r.jsxs)(l.gC,{children:[(0,r.jsx)(l.eb,{value:"current",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(c.E,{variant:"default",className:"text-xs",children:"Current"}),(0,r.jsxs)("span",{children:["Current Session (",s.crypto1&&s.crypto2?`${s.crypto1}/${s.crypto2}`:"Crypto 1/Crypto 2 = 0",")"]})]})}),o.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.w,{className:"my-1"}),o.map(e=>(0,r.jsx)(l.eb,{value:e.id,children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(c.E,{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Current":"Past"}),(0,r.jsx)("span",{children:e.name})]})},e.id))]})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>{"current"===b?(e({type:"CLEAR_ORDER_HISTORY"}),a({title:"History Cleared",description:"Current session trade history has been cleared."})):a({title:"Cannot Clear",description:"Cannot clear history for past sessions. Use current session to clear history.",variant:"destructive"})},className:"btn-outline-neo",disabled:"current"!==b,children:[(0,r.jsx)(u,{className:"mr-2 h-4 w-4"}),"Clear History"]}),(0,r.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>{let e,t;if(0===N.length){a({title:"No Data to Export",description:"There is no trade history to export for the selected session.",variant:"destructive"});return}if("current"===b)e=["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...N.map(e=>[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,e.amountCrypto1?.toFixed(s.numDigits)||"",e.avgPrice?.toFixed(s.numDigits)||"",e.valueCrypto2?.toFixed(s.numDigits)||"",e.price1?.toFixed(s.numDigits)||"",e.crypto1Symbol,e.price2?.toFixed(s.numDigits)||"",e.crypto2Symbol,e.realizedProfitLossCrypto1?.toFixed(s.numDigits)||"",e.realizedProfitLossCrypto2?.toFixed(s.numDigits)||""].join(","))].join("\n"),t=`current_session_history_${new Date().toISOString().split("T")[0]}.csv`;else{e=P.exportSessionToCSV(b)||"";let s=P.loadSession(b);t=`${s?.name||"session"}_${new Date().toISOString().split("T")[0]}.csv`}if(!e){a({title:"Export Failed",description:"Failed to generate CSV content.",variant:"destructive"});return}let r=new Blob([e],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a"),o=URL.createObjectURL(r);i.setAttribute("href",o),i.setAttribute("download",t),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i),a({title:"Export Complete",description:"Trade history has been exported to CSV file."})},className:"btn-outline-neo",children:[(0,r.jsx)(f,{className:"mr-2 h-4 w-4"}),"Export"]})]})]}),k&&(0,r.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Session:"}),(0,r.jsx)("div",{className:"font-medium",children:k.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Pair:"}),(0,r.jsx)("div",{className:"font-medium",children:k.pair})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Total Trades:"}),(0,r.jsx)("div",{className:"font-medium",children:k.totalTrades})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Total P/L:"}),(0,r.jsx)("div",{className:`font-medium ${k.totalProfitLoss>=0?"text-green-600":"text-red-600"}`,children:k.totalProfitLoss.toFixed(4)})]})]}),"current"!==b&&(0,r.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:["Last modified: ",(0,j.GP)(new Date(k.lastModified),"MMM dd, yyyy HH:mm")]})]})]})]}),(0,r.jsxs)(n.Zp,{className:"border-2 border-border",children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsxs)(n.ZB,{className:"text-lg font-bold text-primary",children:["Trade History - ",k?.name||"Unknown Session"]}),(0,r.jsx)(n.BT,{children:0===N.length?"No trades recorded for this session yet.":`Showing ${N.length} trades for the selected session.`})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsx)(g,{history:N,config:s})})]})]})}function g({history:e,config:t}){let s=e=>e?.toFixed(t.numDigits)??"-",i=(e,t=!1)=>{if("StablecoinSwap"===e.tradingMode){if(("SELL"===e.type||"SELL"===e.orderType)&&void 0!==e.realizedProfitLossCrypto2){let i=t?e.realizedProfitLossCrypto1||0:e.realizedProfitLossCrypto2,a=i>=0?"text-green-600":"text-red-600",o=e.percentageGain||0;return(0,r.jsxs)("div",{className:`${a} font-semibold`,children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{children:i>=0?"\uD83D\uDCC8":"\uD83D\uDCC9"}),(0,r.jsx)("span",{children:s(i)})]}),0!==o&&(0,r.jsxs)("div",{className:"text-xs opacity-75",children:["(",o>0?"+":"",o.toFixed(2),"%)"]})]})}return(0,r.jsx)("span",{className:"text-muted-foreground",children:"-"})}{let i=t?e.realizedProfitLossCrypto1:e.realizedProfitLossCrypto2;if(void 0!==i){let t=i>=0?"text-green-600":"text-red-600",a=e.percentageGain||0;return(0,r.jsxs)("div",{className:`${t} font-semibold`,children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("span",{children:i>=0?"\uD83D\uDCC8":"\uD83D\uDCC9"}),(0,r.jsx)("span",{children:s(i)})]}),0!==a&&(0,r.jsxs)("div",{className:"text-xs opacity-75",children:["(",a>0?"+":"",a.toFixed(2),"%)"]})]})}return(0,r.jsx)("span",{className:"text-muted-foreground",children:"-"})}},a=[{key:"date",label:"Date"},{key:"hour",label:"Hour"},{key:"pair",label:"Couple"},{key:"crypto",label:`Crypto (${t.crypto1})`},{key:"orderType",label:"Order Type"},{key:"amount",label:"Amount"},{key:"avgPrice",label:"Avg Price"},{key:"value",label:`Value (${t.crypto2})`},{key:"price1",label:"Price 1"},{key:"crypto1Symbol",label:`Crypto (${t.crypto1})`},{key:"price2",label:"Price 2"},{key:"crypto2Symbol",label:`Crypto (${t.crypto2})`},{key:"profitCrypto1",label:`Profit/Loss (${t.crypto1})`},{key:"profitCrypto2",label:`Profit/Loss (${t.crypto2})`}];return 0===e.length?(0,r.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,r.jsx)(b.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{children:"No trading history for this session yet."})]}):(0,r.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsx)("tr",{className:"bg-card hover:bg-card border-b",children:a.map(e=>(0,r.jsx)("th",{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm text-left",children:e.label},e.key))})}),(0,r.jsx)("tbody",{children:e.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-card/80 border-b",children:[(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:(0,j.GP)(new Date(e.timestamp),"yyyy-MM-dd")}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:(0,j.GP)(new Date(e.timestamp),"HH:mm:ss")}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:e.pair}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,r.jsx)("td",{className:`px-3 py-2 text-xs font-semibold ${"BUY"===e.orderType?"text-green-400":"text-destructive"}`,children:e.orderType}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:s(e.amountCrypto1)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:s(e.avgPrice)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:s(e.valueCrypto2)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:s(e.price1)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:s(e.price2)??"-"}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto2Symbol}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:i(e,!0)}),(0,r.jsx)("td",{className:"px-3 py-2 text-xs",children:i(e,!1)})]},e.id))})]})})})}function N(){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(a.A,{}),(0,r.jsx)(o.A,{}),(0,r.jsx)(v,{})]})}},74896:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>l});var r=s(65239),i=s(48088),a=s(88170),o=s.n(a),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let l={children:["",{children:["dashboard",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90540)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\history\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,63144)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\history\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/history/page",pathname:"/dashboard/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},90540:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\history\\page.tsx","default")},92267:(e,t,s)=>{Promise.resolve().then(s.bind(s,90540))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[388,992,288,10,475,12],()=>s(74896));module.exports=r})();