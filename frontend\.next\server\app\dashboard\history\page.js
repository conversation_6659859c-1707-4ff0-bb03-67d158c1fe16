(()=>{var e={};e.id=610,e.ids=[610],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7252:e=>{"use strict";e.exports=require("express")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19185:e=>{"use strict";e.exports=require("dgram")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41204:e=>{"use strict";e.exports=require("string_decoder")},44708:e=>{"use strict";e.exports=require("node:https")},54379:e=>{"use strict";e.exports=require("node:path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57207:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68291:(e,t,r)=>{Promise.resolve().then(r.bind(r,88061))},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74896:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>l});var s=r(65239),i=r(48088),o=r(88170),a=r.n(o),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let l={children:["",{children:["dashboard",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90540)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\history\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\history\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/history/page",pathname:"/dashboard/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},74998:e=>{"use strict";e.exports=require("perf_hooks")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},88061:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(60687),i=r(43210),o=r(37079),a=r(70428),n=r(44493),d=r(29523),l=r(15079),c=r(96834),p=r(35950),x=r(78895),u=r(5551),m=r(29867),h=r(57207);let y=(0,r(82614).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var f=r(15036),b=r(43010);function j(){let{dispatch:e,orderHistory:t,config:r}=(0,x.U)(),{toast:o}=(0,m.dj)(),[a,f]=(0,i.useState)([]),[j,g]=(0,i.useState)("current"),[N,q]=(0,i.useState)([]),C=u.C.getInstance(),P="current"===j?{name:"Current Session",pair:`${r.crypto1}/${r.crypto2}`,totalTrades:t.length,totalProfitLoss:t.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2).reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0),lastModified:Date.now(),isActive:!0}:a.find(e=>e.id===j);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(n.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsx)(n.ZB,{className:"text-xl font-bold text-primary",children:"Session History"}),(0,s.jsx)(n.BT,{children:"View trading history for current and past sessions."})]}),(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Select Session:"}),(0,s.jsxs)(l.l6,{value:j,onValueChange:g,children:[(0,s.jsx)(l.bq,{className:"w-full sm:w-[300px]",children:(0,s.jsx)(l.yv,{placeholder:"Select a session"})}),(0,s.jsxs)(l.gC,{children:[(0,s.jsx)(l.eb,{value:"current",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.E,{variant:"default",className:"text-xs",children:"Current"}),(0,s.jsxs)("span",{children:["Current Session (",r.crypto1&&r.crypto2?`${r.crypto1}/${r.crypto2}`:"Crypto 1/Crypto 2 = 0",")"]})]})}),a.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.w,{className:"my-1"}),a.map(e=>(0,s.jsx)(l.eb,{value:e.id,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.E,{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Current":"Past"}),(0,s.jsx)("span",{children:e.name})]})},e.id))]})]})]})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>{"current"===j?(e({type:"CLEAR_ORDER_HISTORY"}),o({title:"History Cleared",description:"Current session trade history has been cleared."})):o({title:"Cannot Clear",description:"Cannot clear history for past sessions. Use current session to clear history.",variant:"destructive"})},className:"btn-outline-neo",disabled:"current"!==j,children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"Clear History"]}),(0,s.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>{let e,t;if(0===N.length){o({title:"No Data to Export",description:"There is no trade history to export for the selected session.",variant:"destructive"});return}if("current"===j)e=["Date,Time,Pair,Crypto,Order Type,Amount,Avg Price,Value,Price 1,Crypto 1,Price 2,Crypto 2,Profit/Loss (Crypto1),Profit/Loss (Crypto2)",...N.map(e=>[new Date(e.timestamp).toISOString().split("T")[0],new Date(e.timestamp).toTimeString().split(" ")[0],e.pair,e.crypto1Symbol,e.orderType,e.amountCrypto1?.toFixed(r.numDigits)||"",e.avgPrice?.toFixed(r.numDigits)||"",e.valueCrypto2?.toFixed(r.numDigits)||"",e.price1?.toFixed(r.numDigits)||"",e.crypto1Symbol,e.price2?.toFixed(r.numDigits)||"",e.crypto2Symbol,e.realizedProfitLossCrypto1?.toFixed(r.numDigits)||"",e.realizedProfitLossCrypto2?.toFixed(r.numDigits)||""].join(","))].join("\n"),t=`current_session_history_${new Date().toISOString().split("T")[0]}.csv`;else{e=C.exportSessionToCSV(j);let r=C.loadSession(j);t=`${r?.name||"session"}_${new Date().toISOString().split("T")[0]}.csv`}if(!e){o({title:"Export Failed",description:"Failed to generate CSV content.",variant:"destructive"});return}let s=new Blob([e],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a"),a=URL.createObjectURL(s);i.setAttribute("href",a),i.setAttribute("download",t),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i),o({title:"Export Complete",description:"Trade history has been exported to CSV file."})},className:"btn-outline-neo",children:[(0,s.jsx)(y,{className:"mr-2 h-4 w-4"}),"Export"]})]})]}),P&&(0,s.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Session:"}),(0,s.jsx)("div",{className:"font-medium",children:P.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Pair:"}),(0,s.jsx)("div",{className:"font-medium",children:P.pair})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Total Trades:"}),(0,s.jsx)("div",{className:"font-medium",children:P.totalTrades})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Total P/L:"}),(0,s.jsx)("div",{className:`font-medium ${P.totalProfitLoss>=0?"text-green-600":"text-red-600"}`,children:P.totalProfitLoss.toFixed(4)})]})]}),"current"!==j&&(0,s.jsxs)("div",{className:"mt-2 text-xs text-muted-foreground",children:["Last modified: ",(0,b.GP)(new Date(P.lastModified),"MMM dd, yyyy HH:mm")]})]})]})]}),(0,s.jsxs)(n.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsxs)(n.ZB,{className:"text-lg font-bold text-primary",children:["Trade History - ",P?.name||"Unknown Session"]}),(0,s.jsx)(n.BT,{children:0===N.length?"No trades recorded for this session yet.":`Showing ${N.length} trades for the selected session.`})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)(v,{history:N,config:r})})]})]})}function v({history:e,config:t}){let r=e=>e?.toFixed(t.numDigits)??"-",i=(e,t=!1)=>{if("StablecoinSwap"===e.tradingMode){if(("SELL"===e.type||"SELL"===e.orderType)&&void 0!==e.realizedProfitLossCrypto2){let i=t?e.realizedProfitLossCrypto1||0:e.realizedProfitLossCrypto2,o=i>=0?"text-green-600":"text-red-600",a=e.percentageGain||0;return(0,s.jsxs)("div",{className:`${o} font-semibold`,children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("span",{children:i>=0?"\uD83D\uDCC8":"\uD83D\uDCC9"}),(0,s.jsx)("span",{children:r(i)})]}),0!==a&&(0,s.jsxs)("div",{className:"text-xs opacity-75",children:["(",a>0?"+":"",a.toFixed(2),"%)"]})]})}return(0,s.jsx)("span",{className:"text-muted-foreground",children:"-"})}{let i=t?e.realizedProfitLossCrypto1:e.realizedProfitLossCrypto2;if(void 0!==i){let t=i>=0?"text-green-600":"text-red-600",o=e.percentageGain||0;return(0,s.jsxs)("div",{className:`${t} font-semibold`,children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)("span",{children:i>=0?"\uD83D\uDCC8":"\uD83D\uDCC9"}),(0,s.jsx)("span",{children:r(i)})]}),0!==o&&(0,s.jsxs)("div",{className:"text-xs opacity-75",children:["(",o>0?"+":"",o.toFixed(2),"%)"]})]})}return(0,s.jsx)("span",{className:"text-muted-foreground",children:"-"})}},o=[{key:"date",label:"Date"},{key:"hour",label:"Hour"},{key:"pair",label:"Couple"},{key:"crypto",label:`Crypto (${t.crypto1})`},{key:"orderType",label:"Order Type"},{key:"amount",label:"Amount"},{key:"avgPrice",label:"Avg Price"},{key:"value",label:`Value (${t.crypto2})`},{key:"price1",label:"Price 1"},{key:"crypto1Symbol",label:`Crypto (${t.crypto1})`},{key:"price2",label:"Price 2"},{key:"crypto2Symbol",label:`Crypto (${t.crypto2})`},{key:"profitCrypto1",label:`Profit/Loss (${t.crypto1})`},{key:"profitCrypto2",label:`Profit/Loss (${t.crypto2})`}];return 0===e.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)(f.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{children:"No trading history for this session yet."})]}):(0,s.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full",children:[(0,s.jsx)("thead",{children:(0,s.jsx)("tr",{className:"bg-card hover:bg-card border-b",children:o.map(e=>(0,s.jsx)("th",{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm text-left",children:e.label},e.key))})}),(0,s.jsx)("tbody",{children:e.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-card/80 border-b",children:[(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:(0,b.GP)(new Date(e.timestamp),"yyyy-MM-dd")}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:(0,b.GP)(new Date(e.timestamp),"HH:mm:ss")}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:e.pair}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,s.jsx)("td",{className:`px-3 py-2 text-xs font-semibold ${"BUY"===e.orderType?"text-green-400":"text-destructive"}`,children:e.orderType}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.amountCrypto1)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.avgPrice)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.valueCrypto2)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.price1)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto1Symbol}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:r(e.price2)??"-"}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:e.crypto2Symbol}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:i(e,!0)}),(0,s.jsx)("td",{className:"px-3 py-2 text-xs",children:i(e,!1)})]},e.id))})]})})})}function g(){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(o.A,{}),(0,s.jsx)(a.A,{}),(0,s.jsx)(j,{})]})}},90540:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\history\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\history\\page.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},92267:(e,t,r)=>{Promise.resolve().then(r.bind(r,90540))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[992,611,194,939,10,740,12],()=>r(74896));module.exports=s})();