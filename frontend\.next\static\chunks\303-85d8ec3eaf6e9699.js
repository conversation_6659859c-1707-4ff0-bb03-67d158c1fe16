"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[303],{3033:(e,r,t)=>{t.d(r,{A:()=>c});var a=t(95155);t(12115);var s=t(17313),n=t(35695),o=t(91462),i=t(3638),l=t(58527);let d=[{value:"orders",label:"Orders",href:"/dashboard",icon:(0,a.jsx)(o.A,{})},{value:"history",label:"History",href:"/dashboard/history",icon:(0,a.jsx)(i.A,{})},{value:"analytics",label:"Analytics",href:"/dashboard/analytics",icon:(0,a.jsx)(l.A,{})}];function c(){let e=(0,n.useRouter)(),r=(0,n.usePathname)(),t="orders";return"/dashboard/history"===r?t="history":"/dashboard/analytics"===r&&(t="analytics"),(0,a.jsx)(s.tU,{value:t,onValueChange:r=>{let t=d.find(e=>e.value===r);t&&e.push(t.href)},className:"w-full mb-6",children:(0,a.jsx)(s.j7,{className:"grid w-full grid-cols-3 bg-card border-2 border-border",children:d.map(e=>(0,a.jsx)(s.Xi,{value:e.value,className:"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,e.label]})},e.value))})})}},17313:(e,r,t)=>{t.d(r,{Xi:()=>d,av:()=>c,j7:()=>l,tU:()=>i});var a=t(95155),s=t(12115),n=t(30064),o=t(59434);let i=n.bL,l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.B8,{ref:r,className:(0,o.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...s})});l.displayName=n.B8.displayName;let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.l9,{ref:r,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...s})});d.displayName=n.l9.displayName;let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.UC,{ref:r,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...s})});c.displayName=n.UC.displayName},26126:(e,r,t)=>{t.d(r,{E:()=>i});var a=t(95155);t(12115);var s=t(74466),n=t(59434);let o=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:r,variant:t,...s}=e;return(0,a.jsx)("div",{className:(0,n.cn)(o({variant:t}),r),...s})}},30285:(e,r,t)=>{t.d(r,{$:()=>d});var a=t(95155),s=t(12115),n=t(99708),o=t(74466),i=t(59434);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,r)=>{let{className:t,variant:s,size:o,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,a.jsx)(u,{className:(0,i.cn)(l({variant:s,size:o,className:t})),ref:r,...c})});d.displayName="Button"},54530:(e,r,t)=>{t.d(r,{A:()=>v});var a=t(95155),s=t(12115),n=t(77213),o=t(66695),i=t(62523),l=t(30285),d=t(80659),c=t(77070),u=t(10518),f=t(25318),p=t(15222),m=t(8531),x=t(16392),b=t(45219),g=t(38988),y=t(87481);function v(){let{crypto1Balance:e,crypto2Balance:r,stablecoinBalance:t,config:v,dispatch:h,calculateTotalPL:N,resetGlobalBalances:j}=(0,n.U)(),[w,S]=(0,s.useState)(null),{toast:C}=(0,y.dj)(),[A,B]=(0,s.useState)({crypto1:e.toString(),crypto2:r.toString(),stablecoin:t.toString()}),k=e=>e.toFixed(v.numDigits),R=a=>{S(a),B({crypto1:e.toString(),crypto2:r.toString(),stablecoin:t.toString()})},_=t=>{let a=parseFloat(A[t]);!isNaN(a)&&a>=0&&("crypto1"===t?h({type:"UPDATE_BALANCES",payload:{crypto1:a,crypto2:r}}):"crypto2"===t?h({type:"UPDATE_BALANCES",payload:{crypto1:e,crypto2:a}}):"stablecoin"===t&&h({type:"UPDATE_STABLECOIN_BALANCE",payload:a})),S(null)},E=()=>{S(null),B({crypto1:e.toString(),crypto2:r.toString(),stablecoin:t.toString()})},U=(e,r,t,s,n)=>(0,a.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-medium text-muted-foreground",children:e}),s]}),(0,a.jsx)(o.Wu,{children:w===t?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.p,{type:"number",value:A[t],onChange:e=>B(r=>({...r,[t]:e.target.value})),className:"text-lg font-bold",step:"any",min:"0"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.$,{size:"sm",onClick:()=>_(t),className:"flex-1",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}),"Save"]}),(0,a.jsxs)(l.$,{size:"sm",variant:"outline",onClick:E,className:"flex-1",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-1"}),"Cancel"]})]})]}):(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-foreground",children:k(r)}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Available ",n]})]}),(0,a.jsx)(l.$,{size:"sm",variant:"ghost",onClick:()=>R(t),className:"ml-2",children:(0,a.jsx)(p.A,{className:"h-4 w-4"})})]})})]});return(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(()=>{let e=N(),r="StablecoinSwap"===v.tradingMode?v.crypto2:"USD",t="StablecoinSwap"===v.tradingMode?"":"$",s=e>=0?"text-green-600":"text-red-600",n=e>=0?(0,a.jsx)(d.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(c.A,{className:"h-5 w-5 text-red-600"}),i=e>=0?"\uD83D\uDCC8":"\uD83D\uDCC9";return(0,a.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(o.ZB,{className:"text-sm font-medium text-muted-foreground",children:"Total Realized P/L"}),n]}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold ".concat(s," flex items-center gap-2"),children:[(0,a.jsx)("span",{children:i}),(0,a.jsxs)("span",{children:[t,e>=0?"+":"",e.toFixed(v.numDigits)," ",r]})]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"StablecoinSwap"===v.tradingMode?"StablecoinSwap Mode":"SimpleSpot Mode"})]})})})]})})(),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)(l.$,{size:"sm",variant:"outline",onClick:()=>{let e=j();C({title:"Global Balances Reset",description:"Balances reset to defaults: ".concat(e.crypto1Balance," ").concat(v.crypto1||"Crypto1",", ").concat(e.crypto2Balance," ").concat(v.crypto2||"Crypto2",", ").concat(e.stablecoinBalance," ").concat(v.preferredStablecoin||"Stablecoin")})},className:"text-orange-600 border-orange-600 hover:bg-orange-50",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Reset Global Balances"]})}),(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[U("".concat(v.crypto1||"Crypto 1"," Balance"),e,"crypto1",(0,a.jsx)(x.A,{className:"h-5 w-5 text-primary"}),v.crypto1||"Crypto 1"),U("".concat(v.crypto2||"Crypto 2"," Balance"),r,"crypto2",(0,a.jsx)(b.A,{className:"h-5 w-5 text-primary"}),v.crypto2||"Crypto 2"),U("Stablecoin Balance (".concat(v.preferredStablecoin||"N/A",")"),t,"stablecoin",(0,a.jsx)(g.A,{className:"h-5 w-5 text-primary"}),"Stablecoins")]})]})}},62523:(e,r,t)=>{t.d(r,{p:()=>o});var a=t(95155),s=t(12115),n=t(59434);let o=s.forwardRef((e,r)=>{let{className:t,type:s,...o}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:r,...o})});o.displayName="Input"},66695:(e,r,t)=>{t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i});var a=t(95155),s=t(12115),n=t(59434);let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",t),...s})});o.displayName="Card";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-4 md:p-6",t),...s})});i.displayName="CardHeader";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h3",{ref:r,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",t),...s})});l.displayName="CardTitle";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-4 md:p-6 pt-0",t),...s})});c.displayName="CardContent",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-4 md:p-6 pt-0",t),...s})}).displayName="CardFooter"}}]);