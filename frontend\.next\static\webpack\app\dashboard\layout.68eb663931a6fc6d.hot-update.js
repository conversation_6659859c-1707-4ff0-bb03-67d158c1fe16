"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/TelegramSettings.tsx":
/*!*******************************************************!*\
  !*** ./src/components/dashboard/TelegramSettings.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TelegramSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_MessageSquare_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=MessageSquare,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _utils_telegramNotifications__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/telegramNotifications */ \"(app-pages-browser)/./src/utils/telegramNotifications.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction TelegramSettings(param) {\n    let { isOpen, onClose } = param;\n    _s();\n    const { telegramConfig, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [localConfig, setLocalConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(telegramConfig);\n    const [isTestingConnection, setIsTestingConnection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSave = ()=>{\n        const validation = (0,_utils_telegramNotifications__WEBPACK_IMPORTED_MODULE_10__.validateTelegramConfig)(localConfig);\n        if (!validation.valid) {\n            toast({\n                title: \"Configuration Error\",\n                description: validation.errors.join(', '),\n                variant: \"destructive\"\n            });\n            return;\n        }\n        dispatch({\n            type: 'SET_TELEGRAM_CONFIG',\n            payload: localConfig\n        });\n        toast({\n            title: \"Telegram Settings Saved\",\n            description: \"Your Telegram notification settings have been updated.\",\n            duration: 3000\n        });\n        onClose();\n    };\n    const handleTestConnection = async ()=>{\n        const validation = (0,_utils_telegramNotifications__WEBPACK_IMPORTED_MODULE_10__.validateTelegramConfig)(localConfig);\n        if (!validation.valid) {\n            toast({\n                title: \"Configuration Error\",\n                description: validation.errors.join(', '),\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setIsTestingConnection(true);\n        try {\n            const success = await (0,_utils_telegramNotifications__WEBPACK_IMPORTED_MODULE_10__.testTelegramConfig)(localConfig);\n            if (success) {\n                toast({\n                    title: \"Test Successful\",\n                    description: \"Test message sent successfully! Check your Telegram chat.\",\n                    duration: 5000\n                });\n            } else {\n                toast({\n                    title: \"Test Failed\",\n                    description: \"Could not send test message. Please check your configuration.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"Test Failed\",\n                description: \"An error occurred while testing the connection.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsTestingConnection(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogContent, {\n            className: \"sm:max-w-[600px] max-h-[90vh] flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogHeader, {\n                    className: \"flex-shrink-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                \"Telegram Notifications\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_8__.DialogDescription, {\n                            children: \"Configure Telegram notifications for trading alerts, errors, and system events.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6 overflow-y-auto flex-1 pr-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm\",\n                                        children: \"Notification Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                id: \"telegramEnabled\",\n                                                checked: localConfig.enabled,\n                                                onCheckedChange: (checked)=>setLocalConfig({\n                                                        ...localConfig,\n                                                        enabled: checked\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"telegramEnabled\",\n                                                children: \"Enable Telegram notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this),\n                        localConfig.enabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                    className: \"text-sm\",\n                                                    children: \"Bot Configuration\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                                    children: \"Create a Telegram bot via @BotFather and get your bot token.\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"botToken\",\n                                                            children: \"Bot Token\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"botToken\",\n                                                            type: \"password\",\n                                                            value: localConfig.botToken,\n                                                            onChange: (e)=>setLocalConfig({\n                                                                    ...localConfig,\n                                                                    botToken: e.target.value\n                                                                }),\n                                                            placeholder: \"123456789:ABCdefGHIjklMNOpqrsTUVwxyz\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: \"Format: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                            htmlFor: \"chatId\",\n                                                            children: \"Chat ID\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"chatId\",\n                                                            value: localConfig.chatId,\n                                                            onChange: (e)=>setLocalConfig({\n                                                                    ...localConfig,\n                                                                    chatId: e.target.value\n                                                                }),\n                                                            placeholder: \"-1001234567890 or 123456789\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: \"Your personal chat ID or group chat ID (starts with -)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                className: \"text-sm\",\n                                                children: \"Setup Instructions\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-primary\",\n                                                                children: \"1.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Message @BotFather on Telegram and create a new bot with /newbot\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-primary\",\n                                                                children: \"2.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Copy the bot token and paste it above\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-primary\",\n                                                                children: \"3.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Message @userinfobot to get your chat ID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-primary\",\n                                                                children: \"4.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                                lineNumber: 182,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Start a conversation with your bot first\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-primary\",\n                                                                children: \"5.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Test the connection using the button below\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                                lineNumber: 187,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                className: \"text-sm\",\n                                                children: \"Test Connection\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleTestConnection,\n                                                disabled: isTestingConnection || !localConfig.botToken || !localConfig.chatId,\n                                                className: \"w-full\",\n                                                variant: \"outline\",\n                                                children: isTestingConnection ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Sending Test Message...\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageSquare_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        \"Send Test Message\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-3 pt-4 border-t border-border flex-shrink-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: onClose,\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: handleSave,\n                            className: \"btn-neo\",\n                            children: \"Save Settings\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\TelegramSettings.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, this);\n}\n_s(TelegramSettings, \"zGFw4KD4U9p8ovMNNeBZMV/lF9g=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = TelegramSettings;\nvar _c;\n$RefreshReg$(_c, \"TelegramSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/TelegramSettings.tsx\n"));

/***/ })

});