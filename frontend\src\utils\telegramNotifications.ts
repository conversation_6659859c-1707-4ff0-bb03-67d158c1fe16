/**
 * Telegram Notifications Utility
 * Comprehensive notification system for trading errors, connection issues, and API failures
 */

export interface TelegramConfig {
  enabled: boolean;
  botToken: string;
  chatId: string;
}

export type NotificationType = 'error' | 'success' | 'info' | 'warning' | 'trade' | 'connection';

export interface NotificationMessage {
  type: NotificationType;
  title: string;
  message: string;
  timestamp?: Date;
  metadata?: Record<string, any>;
}

/**
 * Send a Telegram notification
 */
export async function sendTelegramNotification(
  message: string,
  type: NotificationType,
  config: TelegramConfig,
  metadata?: Record<string, any>
): Promise<boolean> {
  try {
    if (!config.enabled || !config.botToken || !config.chatId) {
      console.warn('Telegram notifications disabled or not configured');
      return false;
    }

    const emoji = getEmojiForType(type);
    const formattedMessage = formatMessage(message, type, emoji, metadata);

    const response = await fetch(`https://api.telegram.org/bot${config.botToken}/sendMessage`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chat_id: config.chatId,
        text: formattedMessage,
        parse_mode: 'HTML',
        disable_web_page_preview: true
      })
    });

    if (!response.ok) {
      throw new Error(`Telegram API error: ${response.status} ${response.statusText}`);
    }

    console.log(`📱 Telegram notification sent: ${type}`);
    return true;
  } catch (error) {
    console.error('Failed to send Telegram notification:', error);
    return false;
  }
}

/**
 * Send trading-specific notifications
 */
export async function sendTradingNotification(
  action: 'buy' | 'sell' | 'error' | 'start' | 'stop',
  details: {
    pair?: string;
    price?: number;
    amount?: number;
    profit?: number;
    error?: string;
  },
  config: TelegramConfig
): Promise<boolean> {
  let message = '';
  let type: NotificationType = 'info';

  switch (action) {
    case 'buy':
      message = `🟢 <b>BUY ORDER EXECUTED</b>\n` +
                `Pair: ${details.pair}\n` +
                `Price: $${details.price?.toFixed(6)}\n` +
                `Amount: ${details.amount?.toFixed(6)}`;
      type = 'trade';
      break;
    
    case 'sell':
      message = `🔴 <b>SELL ORDER EXECUTED</b>\n` +
                `Pair: ${details.pair}\n` +
                `Price: $${details.price?.toFixed(6)}\n` +
                `Amount: ${details.amount?.toFixed(6)}` +
                (details.profit ? `\nProfit: $${details.profit.toFixed(2)}` : '');
      type = 'trade';
      break;
    
    case 'error':
      message = `🚨 <b>TRADING ERROR</b>\n` +
                `Error: ${details.error}\n` +
                (details.pair ? `Pair: ${details.pair}` : '');
      type = 'error';
      break;
    
    case 'start':
      message = `🚀 <b>BOT STARTED</b>\n` +
                `Trading pair: ${details.pair}\n` +
                `Status: Active`;
      type = 'success';
      break;
    
    case 'stop':
      message = `⏹️ <b>BOT STOPPED</b>\n` +
                `Trading pair: ${details.pair}\n` +
                `Status: Inactive`;
      type = 'warning';
      break;
  }

  return await sendTelegramNotification(message, type, config, details);
}

/**
 * Send connection status notifications
 */
export async function sendConnectionNotification(
  status: 'connected' | 'disconnected' | 'reconnected' | 'api_error',
  details: {
    service?: string;
    error?: string;
    duration?: number;
  },
  config: TelegramConfig
): Promise<boolean> {
  let message = '';
  let type: NotificationType = 'info';

  switch (status) {
    case 'connected':
      message = `🟢 <b>CONNECTION ESTABLISHED</b>\n` +
                `Service: ${details.service || 'Trading API'}\n` +
                `Status: Online`;
      type = 'success';
      break;
    
    case 'disconnected':
      message = `🔴 <b>CONNECTION LOST</b>\n` +
                `Service: ${details.service || 'Trading API'}\n` +
                `Status: Offline\n` +
                `Bot has been stopped for safety`;
      type = 'error';
      break;
    
    case 'reconnected':
      message = `🟡 <b>CONNECTION RESTORED</b>\n` +
                `Service: ${details.service || 'Trading API'}\n` +
                `Downtime: ${details.duration ? `${details.duration}s` : 'Unknown'}\n` +
                `Status: Online`;
      type = 'success';
      break;
    
    case 'api_error':
      message = `⚠️ <b>API ERROR</b>\n` +
                `Service: ${details.service || 'Trading API'}\n` +
                `Error: ${details.error}\n` +
                `Please check your API configuration`;
      type = 'warning';
      break;
  }

  return await sendTelegramNotification(message, type, config, details);
}

/**
 * Send system notifications
 */
export async function sendSystemNotification(
  event: 'high_memory' | 'session_saved' | 'session_restored' | 'config_updated',
  details: {
    memory?: number;
    sessionName?: string;
    configName?: string;
    message?: string;
  },
  config: TelegramConfig
): Promise<boolean> {
  let message = '';
  let type: NotificationType = 'info';

  switch (event) {
    case 'high_memory':
      message = `🧠 <b>HIGH MEMORY USAGE</b>\n` +
                `Current usage: ${details.memory}MB\n` +
                `Recommendation: Consider refreshing the page`;
      type = 'warning';
      break;
    
    case 'session_saved':
      message = `💾 <b>SESSION SAVED</b>\n` +
                `Session: ${details.sessionName}\n` +
                `Status: Backup created successfully`;
      type = 'success';
      break;
    
    case 'session_restored':
      message = `🔄 <b>SESSION RESTORED</b>\n` +
                `Session: ${details.sessionName}\n` +
                `Status: Trading state recovered`;
      type = 'success';
      break;
    
    case 'config_updated':
      message = `⚙️ <b>CONFIGURATION UPDATED</b>\n` +
                `Config: ${details.configName}\n` +
                `Changes: ${details.message}`;
      type = 'info';
      break;
  }

  return await sendTelegramNotification(message, type, config, details);
}

/**
 * Get emoji for notification type
 */
function getEmojiForType(type: NotificationType): string {
  const emojiMap: Record<NotificationType, string> = {
    error: '🚨',
    success: '✅',
    info: 'ℹ️',
    warning: '⚠️',
    trade: '💰',
    connection: '🌐'
  };
  return emojiMap[type] || 'ℹ️';
}

/**
 * Format message with timestamp and metadata
 */
function formatMessage(
  message: string,
  type: NotificationType,
  emoji: string,
  metadata?: Record<string, any>
): string {
  const timestamp = new Date().toLocaleString();
  let formatted = `${emoji} <b>PLUTO TRADING BOT</b>\n\n${message}\n\n⏰ ${timestamp}`;
  
  if (metadata && Object.keys(metadata).length > 0) {
    formatted += '\n\n<i>Additional Info:</i>';
    Object.entries(metadata).forEach(([key, value]) => {
      formatted += `\n• ${key}: ${value}`;
    });
  }
  
  return formatted;
}

/**
 * Test Telegram configuration
 */
export async function testTelegramConfig(config: TelegramConfig): Promise<boolean> {
  return await sendTelegramNotification(
    'Telegram notifications are working correctly! 🎉',
    'success',
    config,
    { test: true, timestamp: new Date().toISOString() }
  );
}

/**
 * Validate Telegram configuration
 */
export function validateTelegramConfig(config: TelegramConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!config.enabled) {
    return { valid: true, errors: [] }; // Valid if disabled
  }
  
  if (!config.botToken) {
    errors.push('Bot token is required');
  } else if (!config.botToken.match(/^\d+:[A-Za-z0-9_-]+$/)) {
    errors.push('Invalid bot token format');
  }
  
  if (!config.chatId) {
    errors.push('Chat ID is required');
  } else if (!config.chatId.match(/^-?\d+$/)) {
    errors.push('Invalid chat ID format');
  }
  
  return { valid: errors.length === 0, errors };
}
