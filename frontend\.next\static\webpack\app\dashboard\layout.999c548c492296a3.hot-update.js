"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/dashboard/AlarmSettings.tsx":
/*!****************************************************!*\
  !*** ./src/components/dashboard/AlarmSettings.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AlarmSettings)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_slider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/slider */ \"(app-pages-browser)/./src/components/ui/slider.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Settings,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Settings,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Settings,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Available ringtone options from frontend/ringtones folder\nconst RINGTONE_OPTIONS = [\n    {\n        value: 'G_hades_curse.wav',\n        label: 'Hades Curse'\n    },\n    {\n        value: 'G_hades_demat.wav',\n        label: 'Hades Demat'\n    },\n    {\n        value: 'G_hades_mat.wav',\n        label: 'Hades Mat'\n    },\n    {\n        value: 'G_hades_sanctify.wav',\n        label: 'Hades Sanctify'\n    },\n    {\n        value: 'S_mon1.mp3',\n        label: 'Monster 1'\n    },\n    {\n        value: 'S_mon2.mp3',\n        label: 'Monster 2'\n    },\n    {\n        value: 'Satyr_atk4.wav',\n        label: 'Satyr Attack'\n    },\n    {\n        value: 'bells.wav',\n        label: 'Bells'\n    },\n    {\n        value: 'bird1.wav',\n        label: 'Bird 1'\n    },\n    {\n        value: 'bird7.wav',\n        label: 'Bird 7'\n    },\n    {\n        value: 'cheer.wav',\n        label: 'Cheer'\n    },\n    {\n        value: 'chest1.wav',\n        label: 'Chest'\n    },\n    {\n        value: 'chime2.wav',\n        label: 'Chime'\n    },\n    {\n        value: 'dark2.wav',\n        label: 'Dark'\n    },\n    {\n        value: 'foundry2.wav',\n        label: 'Foundry'\n    },\n    {\n        value: 'goatherd1.wav',\n        label: 'Goatherd'\n    },\n    {\n        value: 'marble1.wav',\n        label: 'Marble'\n    },\n    {\n        value: 'sanctuary1.wav',\n        label: 'Sanctuary'\n    },\n    {\n        value: 'space_bells4a.wav',\n        label: 'Space Bells'\n    },\n    {\n        value: 'sparrow1.wav',\n        label: 'Sparrow'\n    },\n    {\n        value: 'tax3.wav',\n        label: 'Tax'\n    },\n    {\n        value: 'wolf4.wav',\n        label: 'Wolf'\n    }\n];\nfunction AlarmSettings(param) {\n    let { isOpen, onClose } = param;\n    _s();\n    const { sessionAlarmConfig, dispatch, playSessionAlarm } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const [localConfig, setLocalConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(sessionAlarmConfig);\n    const handleSave = ()=>{\n        dispatch({\n            type: 'SET_SESSION_ALARM_CONFIG',\n            payload: localConfig\n        });\n        toast({\n            title: \"Alarm Settings Saved\",\n            description: \"Your session-specific alarm settings have been updated.\",\n            duration: 3000\n        });\n        onClose();\n    };\n    const handleTestSound = (soundFile)=>{\n        try {\n            const audio = new Audio(\"/ringtones/\".concat(soundFile));\n            audio.volume = localConfig.volume / 100;\n            audio.play().catch(console.error);\n        } catch (error) {\n            console.error('Failed to play test sound:', error);\n            toast({\n                title: \"Sound Test Failed\",\n                description: \"Could not play the selected sound file.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n            className: \"sm:max-w-[500px] max-h-[80vh]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                \"Session Alarm Settings\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                            children: \"Configure custom alarm sounds and settings for this trading session.\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6 overflow-y-auto max-h-[60vh] pr-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Volume Control\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                children: [\n                                                    \"Volume: \",\n                                                    localConfig.volume,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_8__.Slider, {\n                                                value: [\n                                                    localConfig.volume\n                                                ],\n                                                onValueChange: (value)=>setLocalConfig({\n                                                        ...localConfig,\n                                                        volume: value[0]\n                                                    }),\n                                                max: 100,\n                                                min: 0,\n                                                step: 5,\n                                                className: \"w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                lineNumber: 103,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"Order Execution Success Alarms\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                    id: \"buyAlarmEnabled\",\n                                                    checked: localConfig.buyAlarmEnabled,\n                                                    onCheckedChange: (checked)=>setLocalConfig({\n                                                            ...localConfig,\n                                                            buyAlarmEnabled: checked\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"buyAlarmEnabled\",\n                                                    children: \"Enable alerts on successful order execution\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    children: \"Success Alarm Sound\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                            value: localConfig.buyAlarmSound,\n                                                            onValueChange: (value)=>setLocalConfig({\n                                                                    ...localConfig,\n                                                                    buyAlarmSound: value\n                                                                }),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                        lineNumber: 140,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                    lineNumber: 139,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                    className: \"max-h-[200px] overflow-y-auto\",\n                                                                    children: RINGTONE_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: option.value,\n                                                                            children: option.label\n                                                                        }, option.value, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                            lineNumber: 144,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                    lineNumber: 142,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>handleTestSound(localConfig.buyAlarmSound),\n                                                            disabled: !localConfig.buyAlarmEnabled,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm text-red-600\",\n                                        children: \"Error/Failure Alarms\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                    id: \"sellAlarmEnabled\",\n                                                    checked: localConfig.sellAlarmEnabled,\n                                                    onCheckedChange: (checked)=>setLocalConfig({\n                                                            ...localConfig,\n                                                            sellAlarmEnabled: checked\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"sellAlarmEnabled\",\n                                                    children: \"Enable alerts on errors/failures\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    children: \"Error Alarm Sound\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                            value: localConfig.sellAlarmSound,\n                                                            onValueChange: (value)=>setLocalConfig({\n                                                                    ...localConfig,\n                                                                    sellAlarmSound: value\n                                                                }),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {}, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                        lineNumber: 188,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                    className: \"max-h-[200px] overflow-y-auto\",\n                                                                    children: RINGTONE_OPTIONS.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: option.value,\n                                                                            children: option.label\n                                                                        }, option.value, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                            lineNumber: 192,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>handleTestSound(localConfig.sellAlarmSound),\n                                                            disabled: !localConfig.sellAlarmEnabled,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Settings_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleSave,\n                                    className: \"btn-neo\",\n                                    children: \"Save Settings\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n            lineNumber: 80,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\AlarmSettings.tsx\",\n        lineNumber: 79,\n        columnNumber: 5\n    }, this);\n}\n_s(AlarmSettings, \"LTcPcqDkqCuWerGDBcVvA00BCAE=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = AlarmSettings;\nvar _c;\n$RefreshReg$(_c, \"AlarmSettings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9BbGFybVNldHRpbmdzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFd0M7QUFDc0I7QUFDZDtBQUVGO0FBQ007QUFDbUQ7QUFDTjtBQUNqRDtBQUM0RTtBQUNyRTtBQUNWO0FBRTdDLDREQUE0RDtBQUM1RCxNQUFNeUIsbUJBQW1CO0lBQ3ZCO1FBQUVDLE9BQU87UUFBcUJDLE9BQU87SUFBYztJQUNuRDtRQUFFRCxPQUFPO1FBQXFCQyxPQUFPO0lBQWM7SUFDbkQ7UUFBRUQsT0FBTztRQUFtQkMsT0FBTztJQUFZO0lBQy9DO1FBQUVELE9BQU87UUFBd0JDLE9BQU87SUFBaUI7SUFDekQ7UUFBRUQsT0FBTztRQUFjQyxPQUFPO0lBQVk7SUFDMUM7UUFBRUQsT0FBTztRQUFjQyxPQUFPO0lBQVk7SUFDMUM7UUFBRUQsT0FBTztRQUFrQkMsT0FBTztJQUFlO0lBQ2pEO1FBQUVELE9BQU87UUFBYUMsT0FBTztJQUFRO0lBQ3JDO1FBQUVELE9BQU87UUFBYUMsT0FBTztJQUFTO0lBQ3RDO1FBQUVELE9BQU87UUFBYUMsT0FBTztJQUFTO0lBQ3RDO1FBQUVELE9BQU87UUFBYUMsT0FBTztJQUFRO0lBQ3JDO1FBQUVELE9BQU87UUFBY0MsT0FBTztJQUFRO0lBQ3RDO1FBQUVELE9BQU87UUFBY0MsT0FBTztJQUFRO0lBQ3RDO1FBQUVELE9BQU87UUFBYUMsT0FBTztJQUFPO0lBQ3BDO1FBQUVELE9BQU87UUFBZ0JDLE9BQU87SUFBVTtJQUMxQztRQUFFRCxPQUFPO1FBQWlCQyxPQUFPO0lBQVc7SUFDNUM7UUFBRUQsT0FBTztRQUFlQyxPQUFPO0lBQVM7SUFDeEM7UUFBRUQsT0FBTztRQUFrQkMsT0FBTztJQUFZO0lBQzlDO1FBQUVELE9BQU87UUFBcUJDLE9BQU87SUFBYztJQUNuRDtRQUFFRCxPQUFPO1FBQWdCQyxPQUFPO0lBQVU7SUFDMUM7UUFBRUQsT0FBTztRQUFZQyxPQUFPO0lBQU07SUFDbEM7UUFBRUQsT0FBTztRQUFhQyxPQUFPO0lBQU87Q0FDckM7QUFPYyxTQUFTQyxjQUFjLEtBQXVDO1FBQXZDLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFzQixHQUF2Qzs7SUFDcEMsTUFBTSxFQUFFQyxrQkFBa0IsRUFBRUMsUUFBUSxFQUFFQyxnQkFBZ0IsRUFBRSxHQUFHL0IsMkVBQWlCQTtJQUM1RSxNQUFNLEVBQUVnQyxLQUFLLEVBQUUsR0FBR1YsMkRBQVFBO0lBRTFCLE1BQU0sQ0FBQ1csYUFBYUMsZUFBZSxHQUFHbkMsK0NBQVFBLENBQUM4QjtJQUUvQyxNQUFNTSxhQUFhO1FBQ2pCTCxTQUFTO1lBQUVNLE1BQU07WUFBNEJDLFNBQVNKO1FBQVk7UUFDbEVELE1BQU07WUFDSk0sT0FBTztZQUNQQyxhQUFhO1lBQ2JDLFVBQVU7UUFDWjtRQUNBWjtJQUNGO0lBRUEsTUFBTWEsa0JBQWtCLENBQUNDO1FBQ3ZCLElBQUk7WUFDRixNQUFNQyxRQUFRLElBQUlDLE1BQU0sY0FBd0IsT0FBVkY7WUFDdENDLE1BQU1FLE1BQU0sR0FBR1osWUFBWVksTUFBTSxHQUFHO1lBQ3BDRixNQUFNRyxJQUFJLEdBQUdDLEtBQUssQ0FBQ0MsUUFBUUMsS0FBSztRQUNsQyxFQUFFLE9BQU9BLE9BQU87WUFDZEQsUUFBUUMsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUNqQixNQUFNO2dCQUNKTSxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiVyxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEscUJBQ0UsOERBQUNwQyx5REFBTUE7UUFBQ3FDLE1BQU14QjtRQUFReUIsY0FBY3hCO2tCQUNsQyw0RUFBQ2IsZ0VBQWFBO1lBQUNzQyxXQUFVOzs4QkFDdkIsOERBQUNwQywrREFBWUE7O3NDQUNYLDhEQUFDQyw4REFBV0E7NEJBQUNtQyxXQUFVOzs4Q0FDckIsOERBQUNoQyxrR0FBUUE7b0NBQUNnQyxXQUFVOzs7Ozs7Z0NBQVk7Ozs7Ozs7c0NBR2xDLDhEQUFDckMsb0VBQWlCQTtzQ0FBQzs7Ozs7Ozs7Ozs7OzhCQUtyQiw4REFBQ3NDO29CQUFJRCxXQUFVOztzQ0FFYiw4REFBQzVDLHFEQUFJQTs7OENBQ0gsOERBQUNFLDJEQUFVQTs4Q0FDVCw0RUFBQ0MsMERBQVNBO3dDQUFDeUMsV0FBVTs7MERBQ25CLDhEQUFDbEMsa0dBQU9BO2dEQUFDa0MsV0FBVTs7Ozs7OzRDQUFZOzs7Ozs7Ozs7Ozs7OENBSW5DLDhEQUFDM0MsNERBQVdBOzhDQUNWLDRFQUFDNEM7d0NBQUlELFdBQVU7OzBEQUNiLDhEQUFDbkQsdURBQUtBOztvREFBQztvREFBUytCLFlBQVlZLE1BQU07b0RBQUM7Ozs7Ozs7MERBQ25DLDhEQUFDaEMseURBQU1BO2dEQUNMVyxPQUFPO29EQUFDUyxZQUFZWSxNQUFNO2lEQUFDO2dEQUMzQlUsZUFBZSxDQUFDL0IsUUFBVVUsZUFBZTt3REFBRSxHQUFHRCxXQUFXO3dEQUFFWSxRQUFRckIsS0FBSyxDQUFDLEVBQUU7b0RBQUM7Z0RBQzVFZ0MsS0FBSztnREFDTEMsS0FBSztnREFDTEMsTUFBTTtnREFDTkwsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT2xCLDhEQUFDNUMscURBQUlBOzs4Q0FDSCw4REFBQ0UsMkRBQVVBOzhDQUNULDRFQUFDQywwREFBU0E7d0NBQUN5QyxXQUFVO2tEQUF5Qjs7Ozs7Ozs7Ozs7OENBRWhELDhEQUFDM0MsNERBQVdBO29DQUFDMkMsV0FBVTs7c0RBQ3JCLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNsRCw2REFBUUE7b0RBQ1B3RCxJQUFHO29EQUNIQyxTQUFTM0IsWUFBWTRCLGVBQWU7b0RBQ3BDQyxpQkFBaUIsQ0FBQ0YsVUFDaEIxQixlQUFlOzREQUFFLEdBQUdELFdBQVc7NERBQUU0QixpQkFBaUJEO3dEQUFtQjs7Ozs7OzhEQUd6RSw4REFBQzFELHVEQUFLQTtvREFBQzZELFNBQVE7OERBQWtCOzs7Ozs7Ozs7Ozs7c0RBR25DLDhEQUFDVDs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNuRCx1REFBS0E7OERBQUM7Ozs7Ozs4REFDUCw4REFBQ29EO29EQUFJRCxXQUFVOztzRUFDYiw4REFBQ2pELHlEQUFNQTs0REFDTG9CLE9BQU9TLFlBQVkrQixhQUFhOzREQUNoQ1QsZUFBZSxDQUFDL0IsUUFBVVUsZUFBZTtvRUFBRSxHQUFHRCxXQUFXO29FQUFFK0IsZUFBZXhDO2dFQUFNOzs4RUFFaEYsOERBQUNqQixnRUFBYUE7b0VBQUM4QyxXQUFVOzhFQUN2Qiw0RUFBQzdDLDhEQUFXQTs7Ozs7Ozs7Ozs4RUFFZCw4REFBQ0gsZ0VBQWFBO29FQUFDZ0QsV0FBVTs4RUFDdEI5QixpQkFBaUIwQyxHQUFHLENBQUMsQ0FBQ0MsdUJBQ3JCLDhEQUFDNUQsNkRBQVVBOzRFQUFvQmtCLE9BQU8wQyxPQUFPMUMsS0FBSztzRkFDL0MwQyxPQUFPekMsS0FBSzsyRUFERXlDLE9BQU8xQyxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7O3NFQU1uQyw4REFBQ3ZCLHlEQUFNQTs0REFDTGlELFNBQVE7NERBQ1JpQixNQUFLOzREQUNMQyxTQUFTLElBQU0zQixnQkFBZ0JSLFlBQVkrQixhQUFhOzREQUN4REssVUFBVSxDQUFDcEMsWUFBWTRCLGVBQWU7c0VBRXRDLDRFQUFDekMsa0dBQUlBO2dFQUFDaUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBUTFCLDhEQUFDNUMscURBQUlBOzs4Q0FDSCw4REFBQ0UsMkRBQVVBOzhDQUNULDRFQUFDQywwREFBU0E7d0NBQUN5QyxXQUFVO2tEQUF1Qjs7Ozs7Ozs7Ozs7OENBRTlDLDhEQUFDM0MsNERBQVdBO29DQUFDMkMsV0FBVTs7c0RBQ3JCLDhEQUFDQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNsRCw2REFBUUE7b0RBQ1B3RCxJQUFHO29EQUNIQyxTQUFTM0IsWUFBWXFDLGdCQUFnQjtvREFDckNSLGlCQUFpQixDQUFDRixVQUNoQjFCLGVBQWU7NERBQUUsR0FBR0QsV0FBVzs0REFBRXFDLGtCQUFrQlY7d0RBQW1COzs7Ozs7OERBRzFFLDhEQUFDMUQsdURBQUtBO29EQUFDNkQsU0FBUTs4REFBbUI7Ozs7Ozs7Ozs7OztzREFHcEMsOERBQUNUOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ25ELHVEQUFLQTs4REFBQzs7Ozs7OzhEQUNQLDhEQUFDb0Q7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDakQseURBQU1BOzREQUNMb0IsT0FBT1MsWUFBWXNDLGNBQWM7NERBQ2pDaEIsZUFBZSxDQUFDL0IsUUFBVVUsZUFBZTtvRUFBRSxHQUFHRCxXQUFXO29FQUFFc0MsZ0JBQWdCL0M7Z0VBQU07OzhFQUVqRiw4REFBQ2pCLGdFQUFhQTtvRUFBQzhDLFdBQVU7OEVBQ3ZCLDRFQUFDN0MsOERBQVdBOzs7Ozs7Ozs7OzhFQUVkLDhEQUFDSCxnRUFBYUE7b0VBQUNnRCxXQUFVOzhFQUN0QjlCLGlCQUFpQjBDLEdBQUcsQ0FBQyxDQUFDQyx1QkFDckIsOERBQUM1RCw2REFBVUE7NEVBQW9Ca0IsT0FBTzBDLE9BQU8xQyxLQUFLO3NGQUMvQzBDLE9BQU96QyxLQUFLOzJFQURFeUMsT0FBTzFDLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBTW5DLDhEQUFDdkIseURBQU1BOzREQUNMaUQsU0FBUTs0REFDUmlCLE1BQUs7NERBQ0xDLFNBQVMsSUFBTTNCLGdCQUFnQlIsWUFBWXNDLGNBQWM7NERBQ3pERixVQUFVLENBQUNwQyxZQUFZcUMsZ0JBQWdCO3NFQUV2Qyw0RUFBQ2xELGtHQUFJQTtnRUFBQ2lDLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVExQiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDcEQseURBQU1BO29DQUFDaUQsU0FBUTtvQ0FBVWtCLFNBQVN4Qzs4Q0FBUzs7Ozs7OzhDQUc1Qyw4REFBQzNCLHlEQUFNQTtvQ0FBQ21FLFNBQVNqQztvQ0FBWWtCLFdBQVU7OENBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUTdEO0dBakx3QjNCOztRQUNxQzFCLHVFQUFpQkE7UUFDMURzQix1REFBUUE7OztLQUZKSSIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcZGFzaGJvYXJkXFxBbGFybVNldHRpbmdzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlVHJhZGluZ0NvbnRleHQgfSBmcm9tICdAL2NvbnRleHRzL1RyYWRpbmdDb250ZXh0JztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCI7XG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIjtcbmltcG9ydCB7IExhYmVsIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9sYWJlbFwiO1xuaW1wb3J0IHsgQ2hlY2tib3ggfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NoZWNrYm94XCI7XG5pbXBvcnQgeyBTZWxlY3QsIFNlbGVjdENvbnRlbnQsIFNlbGVjdEl0ZW0sIFNlbGVjdFRyaWdnZXIsIFNlbGVjdFZhbHVlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9zZWxlY3RcIjtcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiO1xuaW1wb3J0IHsgU2xpZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9zbGlkZXJcIjtcbmltcG9ydCB7IERpYWxvZywgRGlhbG9nQ29udGVudCwgRGlhbG9nRGVzY3JpcHRpb24sIERpYWxvZ0hlYWRlciwgRGlhbG9nVGl0bGUsIERpYWxvZ1RyaWdnZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2RpYWxvZ1wiO1xuaW1wb3J0IHsgVm9sdW1lMiwgUGxheSwgU2V0dGluZ3MgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdXNlVG9hc3QgfSBmcm9tICdAL2hvb2tzL3VzZS10b2FzdCc7XG5cbi8vIEF2YWlsYWJsZSByaW5ndG9uZSBvcHRpb25zIGZyb20gZnJvbnRlbmQvcmluZ3RvbmVzIGZvbGRlclxuY29uc3QgUklOR1RPTkVfT1BUSU9OUyA9IFtcbiAgeyB2YWx1ZTogJ0dfaGFkZXNfY3Vyc2Uud2F2JywgbGFiZWw6ICdIYWRlcyBDdXJzZScgfSxcbiAgeyB2YWx1ZTogJ0dfaGFkZXNfZGVtYXQud2F2JywgbGFiZWw6ICdIYWRlcyBEZW1hdCcgfSxcbiAgeyB2YWx1ZTogJ0dfaGFkZXNfbWF0LndhdicsIGxhYmVsOiAnSGFkZXMgTWF0JyB9LFxuICB7IHZhbHVlOiAnR19oYWRlc19zYW5jdGlmeS53YXYnLCBsYWJlbDogJ0hhZGVzIFNhbmN0aWZ5JyB9LFxuICB7IHZhbHVlOiAnU19tb24xLm1wMycsIGxhYmVsOiAnTW9uc3RlciAxJyB9LFxuICB7IHZhbHVlOiAnU19tb24yLm1wMycsIGxhYmVsOiAnTW9uc3RlciAyJyB9LFxuICB7IHZhbHVlOiAnU2F0eXJfYXRrNC53YXYnLCBsYWJlbDogJ1NhdHlyIEF0dGFjaycgfSxcbiAgeyB2YWx1ZTogJ2JlbGxzLndhdicsIGxhYmVsOiAnQmVsbHMnIH0sXG4gIHsgdmFsdWU6ICdiaXJkMS53YXYnLCBsYWJlbDogJ0JpcmQgMScgfSxcbiAgeyB2YWx1ZTogJ2JpcmQ3LndhdicsIGxhYmVsOiAnQmlyZCA3JyB9LFxuICB7IHZhbHVlOiAnY2hlZXIud2F2JywgbGFiZWw6ICdDaGVlcicgfSxcbiAgeyB2YWx1ZTogJ2NoZXN0MS53YXYnLCBsYWJlbDogJ0NoZXN0JyB9LFxuICB7IHZhbHVlOiAnY2hpbWUyLndhdicsIGxhYmVsOiAnQ2hpbWUnIH0sXG4gIHsgdmFsdWU6ICdkYXJrMi53YXYnLCBsYWJlbDogJ0RhcmsnIH0sXG4gIHsgdmFsdWU6ICdmb3VuZHJ5Mi53YXYnLCBsYWJlbDogJ0ZvdW5kcnknIH0sXG4gIHsgdmFsdWU6ICdnb2F0aGVyZDEud2F2JywgbGFiZWw6ICdHb2F0aGVyZCcgfSxcbiAgeyB2YWx1ZTogJ21hcmJsZTEud2F2JywgbGFiZWw6ICdNYXJibGUnIH0sXG4gIHsgdmFsdWU6ICdzYW5jdHVhcnkxLndhdicsIGxhYmVsOiAnU2FuY3R1YXJ5JyB9LFxuICB7IHZhbHVlOiAnc3BhY2VfYmVsbHM0YS53YXYnLCBsYWJlbDogJ1NwYWNlIEJlbGxzJyB9LFxuICB7IHZhbHVlOiAnc3BhcnJvdzEud2F2JywgbGFiZWw6ICdTcGFycm93JyB9LFxuICB7IHZhbHVlOiAndGF4My53YXYnLCBsYWJlbDogJ1RheCcgfSxcbiAgeyB2YWx1ZTogJ3dvbGY0LndhdicsIGxhYmVsOiAnV29sZicgfVxuXTtcblxuaW50ZXJmYWNlIEFsYXJtU2V0dGluZ3NQcm9wcyB7XG4gIGlzT3BlbjogYm9vbGVhbjtcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWxhcm1TZXR0aW5ncyh7IGlzT3Blbiwgb25DbG9zZSB9OiBBbGFybVNldHRpbmdzUHJvcHMpIHtcbiAgY29uc3QgeyBzZXNzaW9uQWxhcm1Db25maWcsIGRpc3BhdGNoLCBwbGF5U2Vzc2lvbkFsYXJtIH0gPSB1c2VUcmFkaW5nQ29udGV4dCgpO1xuICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpO1xuXG4gIGNvbnN0IFtsb2NhbENvbmZpZywgc2V0TG9jYWxDb25maWddID0gdXNlU3RhdGUoc2Vzc2lvbkFsYXJtQ29uZmlnKTtcblxuICBjb25zdCBoYW5kbGVTYXZlID0gKCkgPT4ge1xuICAgIGRpc3BhdGNoKHsgdHlwZTogJ1NFVF9TRVNTSU9OX0FMQVJNX0NPTkZJRycsIHBheWxvYWQ6IGxvY2FsQ29uZmlnIH0pO1xuICAgIHRvYXN0KHtcbiAgICAgIHRpdGxlOiBcIkFsYXJtIFNldHRpbmdzIFNhdmVkXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJZb3VyIHNlc3Npb24tc3BlY2lmaWMgYWxhcm0gc2V0dGluZ3MgaGF2ZSBiZWVuIHVwZGF0ZWQuXCIsXG4gICAgICBkdXJhdGlvbjogMzAwMFxuICAgIH0pO1xuICAgIG9uQ2xvc2UoKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVUZXN0U291bmQgPSAoc291bmRGaWxlOiBzdHJpbmcpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgYXVkaW8gPSBuZXcgQXVkaW8oYC9yaW5ndG9uZXMvJHtzb3VuZEZpbGV9YCk7XG4gICAgICBhdWRpby52b2x1bWUgPSBsb2NhbENvbmZpZy52b2x1bWUgLyAxMDA7XG4gICAgICBhdWRpby5wbGF5KCkuY2F0Y2goY29uc29sZS5lcnJvcik7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBwbGF5IHRlc3Qgc291bmQ6JywgZXJyb3IpO1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJTb3VuZCBUZXN0IEZhaWxlZFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJDb3VsZCBub3QgcGxheSB0aGUgc2VsZWN0ZWQgc291bmQgZmlsZS5cIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8RGlhbG9nIG9wZW49e2lzT3Blbn0gb25PcGVuQ2hhbmdlPXtvbkNsb3NlfT5cbiAgICAgIDxEaWFsb2dDb250ZW50IGNsYXNzTmFtZT1cInNtOm1heC13LVs1MDBweF0gbWF4LWgtWzgwdmhdXCI+XG4gICAgICAgIDxEaWFsb2dIZWFkZXI+XG4gICAgICAgICAgPERpYWxvZ1RpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICA8U2V0dGluZ3MgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICBTZXNzaW9uIEFsYXJtIFNldHRpbmdzXG4gICAgICAgICAgPC9EaWFsb2dUaXRsZT5cbiAgICAgICAgICA8RGlhbG9nRGVzY3JpcHRpb24+XG4gICAgICAgICAgICBDb25maWd1cmUgY3VzdG9tIGFsYXJtIHNvdW5kcyBhbmQgc2V0dGluZ3MgZm9yIHRoaXMgdHJhZGluZyBzZXNzaW9uLlxuICAgICAgICAgIDwvRGlhbG9nRGVzY3JpcHRpb24+XG4gICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02IG92ZXJmbG93LXktYXV0byBtYXgtaC1bNjB2aF0gcHItMlwiPlxuICAgICAgICAgIHsvKiBWb2x1bWUgQ29udHJvbCAqL31cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc20gZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8Vm9sdW1lMiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICBWb2x1bWUgQ29udHJvbFxuICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICA8TGFiZWw+Vm9sdW1lOiB7bG9jYWxDb25maWcudm9sdW1lfSU8L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxTbGlkZXJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtbbG9jYWxDb25maWcudm9sdW1lXX1cbiAgICAgICAgICAgICAgICAgIG9uVmFsdWVDaGFuZ2U9eyh2YWx1ZSkgPT4gc2V0TG9jYWxDb25maWcoeyAuLi5sb2NhbENvbmZpZywgdm9sdW1lOiB2YWx1ZVswXSB9KX1cbiAgICAgICAgICAgICAgICAgIG1heD17MTAwfVxuICAgICAgICAgICAgICAgICAgbWluPXswfVxuICAgICAgICAgICAgICAgICAgc3RlcD17NX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIHsvKiBTdWNjZXNzIEFsYXJtIFNldHRpbmdzICovfVxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyZWVuLTYwMFwiPk9yZGVyIEV4ZWN1dGlvbiBTdWNjZXNzIEFsYXJtczwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgIDxDaGVja2JveFxuICAgICAgICAgICAgICAgICAgaWQ9XCJidXlBbGFybUVuYWJsZWRcIlxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17bG9jYWxDb25maWcuYnV5QWxhcm1FbmFibGVkfVxuICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT5cbiAgICAgICAgICAgICAgICAgICAgc2V0TG9jYWxDb25maWcoeyAuLi5sb2NhbENvbmZpZywgYnV5QWxhcm1FbmFibGVkOiBjaGVja2VkIGFzIGJvb2xlYW4gfSlcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiYnV5QWxhcm1FbmFibGVkXCI+RW5hYmxlIGFsZXJ0cyBvbiBzdWNjZXNzZnVsIG9yZGVyIGV4ZWN1dGlvbjwvTGFiZWw+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgPExhYmVsPlN1Y2Nlc3MgQWxhcm0gU291bmQ8L0xhYmVsPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17bG9jYWxDb25maWcuYnV5QWxhcm1Tb3VuZH1cbiAgICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiBzZXRMb2NhbENvbmZpZyh7IC4uLmxvY2FsQ29uZmlnLCBidXlBbGFybVNvdW5kOiB2YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFZhbHVlIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0VHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQgY2xhc3NOYW1lPVwibWF4LWgtWzIwMHB4XSBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgICB7UklOR1RPTkVfT1BUSU9OUy5tYXAoKG9wdGlvbikgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0ga2V5PXtvcHRpb24udmFsdWV9IHZhbHVlPXtvcHRpb24udmFsdWV9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7b3B0aW9uLmxhYmVsfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVUZXN0U291bmQobG9jYWxDb25maWcuYnV5QWxhcm1Tb3VuZCl9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXshbG9jYWxDb25maWcuYnV5QWxhcm1FbmFibGVkfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8UGxheSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgey8qIEVycm9yL0ZhaWx1cmUgQWxhcm0gU2V0dGluZ3MgKi99XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtcmVkLTYwMFwiPkVycm9yL0ZhaWx1cmUgQWxhcm1zPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgPENoZWNrYm94XG4gICAgICAgICAgICAgICAgICBpZD1cInNlbGxBbGFybUVuYWJsZWRcIlxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17bG9jYWxDb25maWcuc2VsbEFsYXJtRW5hYmxlZH1cbiAgICAgICAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17KGNoZWNrZWQpID0+XG4gICAgICAgICAgICAgICAgICAgIHNldExvY2FsQ29uZmlnKHsgLi4ubG9jYWxDb25maWcsIHNlbGxBbGFybUVuYWJsZWQ6IGNoZWNrZWQgYXMgYm9vbGVhbiB9KVxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJzZWxsQWxhcm1FbmFibGVkXCI+RW5hYmxlIGFsZXJ0cyBvbiBlcnJvcnMvZmFpbHVyZXM8L0xhYmVsPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgIDxMYWJlbD5FcnJvciBBbGFybSBTb3VuZDwvTGFiZWw+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtsb2NhbENvbmZpZy5zZWxsQWxhcm1Tb3VuZH1cbiAgICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiBzZXRMb2NhbENvbmZpZyh7IC4uLmxvY2FsQ29uZmlnLCBzZWxsQWxhcm1Tb3VuZDogdmFsdWUgfSl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyIGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSAvPlxuICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50IGNsYXNzTmFtZT1cIm1heC1oLVsyMDBweF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgICAge1JJTkdUT05FX09QVElPTlMubWFwKChvcHRpb24pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIGtleT17b3B0aW9uLnZhbHVlfSB2YWx1ZT17b3B0aW9uLnZhbHVlfT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge29wdGlvbi5sYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlVGVzdFNvdW5kKGxvY2FsQ29uZmlnLnNlbGxBbGFybVNvdW5kKX1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFsb2NhbENvbmZpZy5zZWxsQWxhcm1FbmFibGVkfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8UGxheSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgICAgey8qIEFjdGlvbiBCdXR0b25zICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBnYXAtM1wiPlxuICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9e29uQ2xvc2V9PlxuICAgICAgICAgICAgICBDYW5jZWxcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVTYXZlfSBjbGFzc05hbWU9XCJidG4tbmVvXCI+XG4gICAgICAgICAgICAgIFNhdmUgU2V0dGluZ3NcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICA8L0RpYWxvZz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlVHJhZGluZ0NvbnRleHQiLCJCdXR0b24iLCJMYWJlbCIsIkNoZWNrYm94IiwiU2VsZWN0IiwiU2VsZWN0Q29udGVudCIsIlNlbGVjdEl0ZW0iLCJTZWxlY3RUcmlnZ2VyIiwiU2VsZWN0VmFsdWUiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiU2xpZGVyIiwiRGlhbG9nIiwiRGlhbG9nQ29udGVudCIsIkRpYWxvZ0Rlc2NyaXB0aW9uIiwiRGlhbG9nSGVhZGVyIiwiRGlhbG9nVGl0bGUiLCJWb2x1bWUyIiwiUGxheSIsIlNldHRpbmdzIiwidXNlVG9hc3QiLCJSSU5HVE9ORV9PUFRJT05TIiwidmFsdWUiLCJsYWJlbCIsIkFsYXJtU2V0dGluZ3MiLCJpc09wZW4iLCJvbkNsb3NlIiwic2Vzc2lvbkFsYXJtQ29uZmlnIiwiZGlzcGF0Y2giLCJwbGF5U2Vzc2lvbkFsYXJtIiwidG9hc3QiLCJsb2NhbENvbmZpZyIsInNldExvY2FsQ29uZmlnIiwiaGFuZGxlU2F2ZSIsInR5cGUiLCJwYXlsb2FkIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImR1cmF0aW9uIiwiaGFuZGxlVGVzdFNvdW5kIiwic291bmRGaWxlIiwiYXVkaW8iLCJBdWRpbyIsInZvbHVtZSIsInBsYXkiLCJjYXRjaCIsImNvbnNvbGUiLCJlcnJvciIsInZhcmlhbnQiLCJvcGVuIiwib25PcGVuQ2hhbmdlIiwiY2xhc3NOYW1lIiwiZGl2Iiwib25WYWx1ZUNoYW5nZSIsIm1heCIsIm1pbiIsInN0ZXAiLCJpZCIsImNoZWNrZWQiLCJidXlBbGFybUVuYWJsZWQiLCJvbkNoZWNrZWRDaGFuZ2UiLCJodG1sRm9yIiwiYnV5QWxhcm1Tb3VuZCIsIm1hcCIsIm9wdGlvbiIsInNpemUiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJzZWxsQWxhcm1FbmFibGVkIiwic2VsbEFsYXJtU291bmQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/AlarmSettings.tsx\n"));

/***/ })

});