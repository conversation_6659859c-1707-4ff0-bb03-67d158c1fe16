[{"name": "generate-buildid", "duration": 656, "timestamp": 18783188407, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751897288220, "traceId": "7147b664ef7d10a1"}, {"name": "load-custom-routes", "duration": 1178, "timestamp": 18783189315, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751897288221, "traceId": "7147b664ef7d10a1"}, {"name": "create-dist-dir", "duration": 884, "timestamp": 18783416759, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751897288449, "traceId": "7147b664ef7d10a1"}, {"name": "create-pages-mapping", "duration": 545, "timestamp": 18783521187, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751897288553, "traceId": "7147b664ef7d10a1"}, {"name": "collect-app-paths", "duration": 16533, "timestamp": 18783525186, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751897288557, "traceId": "7147b664ef7d10a1"}, {"name": "create-app-mapping", "duration": 1648, "timestamp": 18783541785, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751897288574, "traceId": "7147b664ef7d10a1"}, {"name": "public-dir-conflict-check", "duration": 1718, "timestamp": 18783556119, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751897288588, "traceId": "7147b664ef7d10a1"}, {"name": "generate-routes-manifest", "duration": 16113, "timestamp": 18783558853, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751897288591, "traceId": "7147b664ef7d10a1"}, {"name": "create-entrypoints", "duration": 356415, "timestamp": 18783770577, "id": 14, "parentId": 1, "tags": {}, "startTime": 1751897288803, "traceId": "7147b664ef7d10a1"}, {"name": "generate-webpack-config", "duration": 1532902, "timestamp": 18784127073, "id": 15, "parentId": 13, "tags": {}, "startTime": 1751897289159, "traceId": "7147b664ef7d10a1"}, {"name": "next-trace-entrypoint-plugin", "duration": 3393, "timestamp": 18786102011, "id": 17, "parentId": 16, "tags": {}, "startTime": 1751897291134, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 1006224, "timestamp": 18786127927, "id": 20, "parentId": 18, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1751897291160, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 1101864, "timestamp": 18786128008, "id": 21, "parentId": 18, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1751897291160, "traceId": "7147b664ef7d10a1"}, {"name": "build-module-tsx", "duration": 88952, "timestamp": 18788000822, "id": 28, "parentId": 16, "tags": {"name": "E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx", "layer": "rsc"}, "startTime": 1751897293033, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 1966838, "timestamp": 18786127068, "id": 19, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751897291159, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 1965826, "timestamp": 18786128107, "id": 23, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Flogin%2Fpage&name=app%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=%2Flogin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751897291160, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 1965784, "timestamp": 18786128157, "id": 24, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751897291160, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 1965741, "timestamp": 18786128206, "id": 25, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fanalytics%2Fpage&name=app%2Fdashboard%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fanalytics%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=%2Fdashboard%2Fanalytics%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751897291160, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 1965701, "timestamp": 18786128252, "id": 26, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fhistory%2Fpage&name=app%2Fdashboard%2Fhistory%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fhistory%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=%2Fdashboard%2Fhistory%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751897291160, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 1965657, "timestamp": 18786128302, "id": 27, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fpage&name=app%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=E%3A%5Cbot%5Ctradingbot_final%5Cfrontend%5Csrc%5Capp&appPaths=%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751897291160, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 2049511, "timestamp": 18786128053, "id": 22, "parentId": 18, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1751897291160, "traceId": "7147b664ef7d10a1"}, {"name": "build-module-tsx", "duration": 321333, "timestamp": 18788499177, "id": 69, "parentId": 16, "tags": {"name": "E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx", "layer": "ssr"}, "startTime": 1751897293531, "traceId": "7147b664ef7d10a1"}, {"name": "make", "duration": 5314988, "timestamp": 18786126493, "id": 18, "parentId": 16, "tags": {}, "startTime": 1751897291159, "traceId": "7147b664ef7d10a1"}, {"name": "get-entries", "duration": 6437, "timestamp": 18791444119, "id": 71, "parentId": 70, "tags": {}, "startTime": 1751897296476, "traceId": "7147b664ef7d10a1"}, {"name": "node-file-trace-plugin", "duration": 298348, "timestamp": 18791458040, "id": 72, "parentId": 70, "tags": {"traceEntryCount": "14"}, "startTime": 1751897296490, "traceId": "7147b664ef7d10a1"}, {"name": "collect-traced-files", "duration": 1137, "timestamp": 18791756415, "id": 73, "parentId": 70, "tags": {}, "startTime": 1751897296788, "traceId": "7147b664ef7d10a1"}, {"name": "finish-modules", "duration": 313877, "timestamp": 18791443690, "id": 70, "parentId": 17, "tags": {}, "startTime": 1751897296476, "traceId": "7147b664ef7d10a1"}, {"name": "chunk-graph", "duration": 78575, "timestamp": 18792010978, "id": 75, "parentId": 74, "tags": {}, "startTime": 1751897297043, "traceId": "7147b664ef7d10a1"}, {"name": "optimize-modules", "duration": 79, "timestamp": 18792089875, "id": 77, "parentId": 74, "tags": {}, "startTime": 1751897297122, "traceId": "7147b664ef7d10a1"}, {"name": "optimize-chunks", "duration": 70139, "timestamp": 18792090199, "id": 78, "parentId": 74, "tags": {}, "startTime": 1751897297122, "traceId": "7147b664ef7d10a1"}, {"name": "optimize-tree", "duration": 279, "timestamp": 18792160478, "id": 79, "parentId": 74, "tags": {}, "startTime": 1751897297193, "traceId": "7147b664ef7d10a1"}, {"name": "optimize-chunk-modules", "duration": 115361, "timestamp": 18792160917, "id": 80, "parentId": 74, "tags": {}, "startTime": 1751897297193, "traceId": "7147b664ef7d10a1"}, {"name": "optimize", "duration": 187065, "timestamp": 18792089745, "id": 76, "parentId": 74, "tags": {}, "startTime": 1751897297122, "traceId": "7147b664ef7d10a1"}, {"name": "module-hash", "duration": 104728, "timestamp": 18792353132, "id": 81, "parentId": 74, "tags": {}, "startTime": 1751897297385, "traceId": "7147b664ef7d10a1"}, {"name": "code-generation", "duration": 121189, "timestamp": 18792458019, "id": 82, "parentId": 74, "tags": {}, "startTime": 1751897297490, "traceId": "7147b664ef7d10a1"}, {"name": "hash", "duration": 38391, "timestamp": 18792603222, "id": 83, "parentId": 74, "tags": {}, "startTime": 1751897297635, "traceId": "7147b664ef7d10a1"}, {"name": "code-generation-jobs", "duration": 753, "timestamp": 18792641606, "id": 84, "parentId": 74, "tags": {}, "startTime": 1751897297674, "traceId": "7147b664ef7d10a1"}, {"name": "module-assets", "duration": 1031, "timestamp": 18792642171, "id": 85, "parentId": 74, "tags": {}, "startTime": 1751897297674, "traceId": "7147b664ef7d10a1"}, {"name": "create-chunk-assets", "duration": 12274, "timestamp": 18792643247, "id": 86, "parentId": 74, "tags": {}, "startTime": 1751897297675, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15961, "timestamp": 18792687935, "id": 88, "parentId": 87, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1751897297720, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15699, "timestamp": 18792688210, "id": 89, "parentId": 87, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1751897297720, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15677, "timestamp": 18792688234, "id": 90, "parentId": 87, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1751897297720, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15664, "timestamp": 18792688249, "id": 91, "parentId": 87, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1751897297720, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15652, "timestamp": 18792688262, "id": 92, "parentId": 87, "tags": {"name": "../app/login/page.js", "cache": "HIT"}, "startTime": 1751897297720, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15628, "timestamp": 18792688288, "id": 93, "parentId": 87, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1751897297720, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15616, "timestamp": 18792688302, "id": 94, "parentId": 87, "tags": {"name": "../app/dashboard/analytics/page.js", "cache": "HIT"}, "startTime": 1751897297720, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15606, "timestamp": 18792688313, "id": 95, "parentId": 87, "tags": {"name": "../app/dashboard/history/page.js", "cache": "HIT"}, "startTime": 1751897297720, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15597, "timestamp": 18792688324, "id": 96, "parentId": 87, "tags": {"name": "../app/dashboard/page.js", "cache": "HIT"}, "startTime": 1751897297720, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15429, "timestamp": 18792688493, "id": 97, "parentId": 87, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1751897297721, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15392, "timestamp": 18792688531, "id": 98, "parentId": 87, "tags": {"name": "388.js", "cache": "HIT"}, "startTime": 1751897297721, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15374, "timestamp": 18792688550, "id": 99, "parentId": 87, "tags": {"name": "992.js", "cache": "HIT"}, "startTime": 1751897297721, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15365, "timestamp": 18792688561, "id": 100, "parentId": 87, "tags": {"name": "288.js", "cache": "HIT"}, "startTime": 1751897297721, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15334, "timestamp": 18792688594, "id": 101, "parentId": 87, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1751897297721, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 15325, "timestamp": 18792688604, "id": 102, "parentId": 87, "tags": {"name": "10.js", "cache": "HIT"}, "startTime": 1751897297721, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 76, "timestamp": 18792703855, "id": 104, "parentId": 87, "tags": {"name": "12.js", "cache": "HIT"}, "startTime": 1751897297736, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 107016, "timestamp": 18792688613, "id": 103, "parentId": 87, "tags": {"name": "475.js", "cache": "MISS"}, "startTime": 1751897297721, "traceId": "7147b664ef7d10a1"}, {"name": "minify-webpack-plugin-optimize", "duration": 134699, "timestamp": 18792660948, "id": 87, "parentId": 16, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1751897297693, "traceId": "7147b664ef7d10a1"}, {"name": "css-minimizer-plugin", "duration": 274, "timestamp": 18792795902, "id": 105, "parentId": 16, "tags": {}, "startTime": 1751897297828, "traceId": "7147b664ef7d10a1"}, {"name": "create-trace-assets", "duration": 7935, "timestamp": 18792796539, "id": 106, "parentId": 17, "tags": {}, "startTime": 1751897297829, "traceId": "7147b664ef7d10a1"}, {"name": "create-trace-assets", "duration": 1889, "timestamp": 18792804904, "id": 107, "parentId": 17, "tags": {}, "startTime": 1751897297837, "traceId": "7147b664ef7d10a1"}, {"name": "seal", "duration": 956789, "timestamp": 18791879183, "id": 74, "parentId": 16, "tags": {}, "startTime": 1751897296911, "traceId": "7147b664ef7d10a1"}, {"name": "webpack-compilation", "duration": 6793144, "timestamp": 18786095632, "id": 16, "parentId": 13, "tags": {"name": "server"}, "startTime": 1751897291128, "traceId": "7147b664ef7d10a1"}, {"name": "emit", "duration": 66105, "timestamp": 18792890029, "id": 108, "parentId": 13, "tags": {}, "startTime": 1751897297922, "traceId": "7147b664ef7d10a1"}, {"name": "webpack-close", "duration": 602117, "timestamp": 18792961249, "id": 109, "parentId": 13, "tags": {"name": "server"}, "startTime": 1751897297993, "traceId": "7147b664ef7d10a1"}, {"name": "webpack-generate-error-stats", "duration": 10664, "timestamp": 18793566507, "id": 110, "parentId": 109, "tags": {}, "startTime": 1751897298599, "traceId": "7147b664ef7d10a1"}, {"name": "make", "duration": 600, "timestamp": 18793661471, "id": 112, "parentId": 111, "tags": {}, "startTime": 1751897298694, "traceId": "7147b664ef7d10a1"}, {"name": "chunk-graph", "duration": 629, "timestamp": 18793677499, "id": 114, "parentId": 113, "tags": {}, "startTime": 1751897298710, "traceId": "7147b664ef7d10a1"}, {"name": "optimize-modules", "duration": 89, "timestamp": 18793678560, "id": 116, "parentId": 113, "tags": {}, "startTime": 1751897298711, "traceId": "7147b664ef7d10a1"}, {"name": "optimize-chunks", "duration": 261, "timestamp": 18793678876, "id": 117, "parentId": 113, "tags": {}, "startTime": 1751897298711, "traceId": "7147b664ef7d10a1"}, {"name": "optimize-tree", "duration": 29, "timestamp": 18793679277, "id": 118, "parentId": 113, "tags": {}, "startTime": 1751897298711, "traceId": "7147b664ef7d10a1"}, {"name": "optimize-chunk-modules", "duration": 261, "timestamp": 18793679505, "id": 119, "parentId": 113, "tags": {}, "startTime": 1751897298712, "traceId": "7147b664ef7d10a1"}, {"name": "optimize", "duration": 1494, "timestamp": 18793678401, "id": 115, "parentId": 113, "tags": {}, "startTime": 1751897298710, "traceId": "7147b664ef7d10a1"}, {"name": "module-hash", "duration": 290, "timestamp": 18793682775, "id": 120, "parentId": 113, "tags": {}, "startTime": 1751897298715, "traceId": "7147b664ef7d10a1"}, {"name": "code-generation", "duration": 65, "timestamp": 18793683167, "id": 121, "parentId": 113, "tags": {}, "startTime": 1751897298715, "traceId": "7147b664ef7d10a1"}, {"name": "hash", "duration": 299, "timestamp": 18793683426, "id": 122, "parentId": 113, "tags": {}, "startTime": 1751897298715, "traceId": "7147b664ef7d10a1"}, {"name": "code-generation-jobs", "duration": 211, "timestamp": 18793683722, "id": 123, "parentId": 113, "tags": {}, "startTime": 1751897298716, "traceId": "7147b664ef7d10a1"}, {"name": "module-assets", "duration": 65, "timestamp": 18793683892, "id": 124, "parentId": 113, "tags": {}, "startTime": 1751897298716, "traceId": "7147b664ef7d10a1"}, {"name": "create-chunk-assets", "duration": 49, "timestamp": 18793683975, "id": 125, "parentId": 113, "tags": {}, "startTime": 1751897298716, "traceId": "7147b664ef7d10a1"}, {"name": "minify-js", "duration": 82, "timestamp": 18793740853, "id": 127, "parentId": 126, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1751897298773, "traceId": "7147b664ef7d10a1"}, {"name": "minify-webpack-plugin-optimize", "duration": 2378, "timestamp": 18793738584, "id": 126, "parentId": 111, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1751897298771, "traceId": "7147b664ef7d10a1"}, {"name": "css-minimizer-plugin", "duration": 21, "timestamp": 18793741152, "id": 128, "parentId": 111, "tags": {}, "startTime": 1751897298773, "traceId": "7147b664ef7d10a1"}, {"name": "seal", "duration": 81499, "timestamp": 18793676952, "id": 113, "parentId": 111, "tags": {}, "startTime": 1751897298709, "traceId": "7147b664ef7d10a1"}, {"name": "webpack-compilation", "duration": 105564, "timestamp": 18793653273, "id": 111, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1751897298685, "traceId": "7147b664ef7d10a1"}, {"name": "emit", "duration": 6962, "timestamp": 18793759369, "id": 129, "parentId": 13, "tags": {}, "startTime": 1751897298791, "traceId": "7147b664ef7d10a1"}, {"name": "webpack-close", "duration": 581, "timestamp": 18793767692, "id": 130, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1751897298800, "traceId": "7147b664ef7d10a1"}, {"name": "webpack-generate-error-stats", "duration": 2804, "timestamp": 18793768366, "id": 131, "parentId": 130, "tags": {}, "startTime": 1751897298800, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 1468274, "timestamp": 18793819407, "id": 137, "parentId": 133, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1751897298851, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 1526979, "timestamp": 18793819415, "id": 138, "parentId": 133, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1751897298851, "traceId": "7147b664ef7d10a1"}, {"name": "build-module-tsx", "duration": 226345, "timestamp": 18795224755, "id": 149, "parentId": 132, "tags": {"name": "E:\\bot\\tradingbot_final\\frontend\\src\\contexts\\TradingContext.tsx", "layer": "app-pages-browser"}, "startTime": 1751897300257, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 1894112, "timestamp": 18793819434, "id": 140, "parentId": 133, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1751897298851, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 2107878, "timestamp": 18793819396, "id": 136, "parentId": 133, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751897298851, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 2149882, "timestamp": 18793819353, "id": 135, "parentId": 133, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1751897298851, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 2182466, "timestamp": 18793819476, "id": 143, "parentId": 133, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751897298852, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 2239096, "timestamp": 18793819454, "id": 142, "parentId": 133, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751897298852, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 3408364, "timestamp": 18793819425, "id": 139, "parentId": 133, "tags": {"request": "E:\\bot\\tradingbot_final\\frontend\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1751897298851, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 3408621, "timestamp": 18793819190, "id": 134, "parentId": 133, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1751897298851, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 3540097, "timestamp": 18793819510, "id": 147, "parentId": 133, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751897298852, "traceId": "7147b664ef7d10a1"}, {"name": "postcss-process", "duration": 1656700, "timestamp": 18796755307, "id": 152, "parentId": 151, "tags": {}, "startTime": 1751897301787, "traceId": "7147b664ef7d10a1"}, {"name": "postcss-loader", "duration": 2606873, "timestamp": 18795805331, "id": 151, "parentId": 150, "tags": {}, "startTime": 1751897300837, "traceId": "7147b664ef7d10a1"}, {"name": "css-loader", "duration": 183764, "timestamp": 18798412566, "id": 153, "parentId": 150, "tags": {"astUsed": "true"}, "startTime": 1751897303445, "traceId": "7147b664ef7d10a1"}, {"name": "add-entry", "duration": 5041082, "timestamp": 18793819503, "id": 146, "parentId": 133, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22E%3A%5C%5Cbot%5C%5Ctradingbot_final%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Chistory%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751897298852, "traceId": "7147b664ef7d10a1"}]