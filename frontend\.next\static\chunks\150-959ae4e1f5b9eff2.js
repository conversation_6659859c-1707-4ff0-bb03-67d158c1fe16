"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[150],{3638:(t,e,n)=>{n.d(e,{A:()=>a});let a=(0,n(40157).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},15222:(t,e,n)=>{n.d(e,{A:()=>a});let a=(0,n(40157).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},16392:(t,e,n)=>{n.d(e,{A:()=>a});let a=(0,n(40157).A)("Bitcoin",[["path",{d:"M11.767 19.089c4.924.868 6.14-6.025 1.216-6.894m-1.216 6.894L5.86 18.047m5.908 1.042-.347 1.97m1.563-8.864c4.924.869 6.14-6.025 1.215-6.893m-1.215 6.893-3.94-.694m5.155-6.2L8.29 4.26m5.908 1.042.348-1.97M7.48 20.364l3.126-17.727",key:"yr8idg"}]])},28184:(t,e,n)=>{n.d(e,{GP:()=>F});let a={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function r(t){return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}let i={date:r({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:r({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:r({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function u(t){return(e,n)=>{let a;if("formatting"===((null==n?void 0:n.context)?String(n.context):"standalone")&&t.formattingValues){let e=t.defaultFormattingWidth||t.defaultWidth,r=(null==n?void 0:n.width)?String(n.width):e;a=t.formattingValues[r]||t.formattingValues[e]}else{let e=t.defaultWidth,r=(null==n?void 0:n.width)?String(n.width):t.defaultWidth;a=t.values[r]||t.values[e]}return a[t.argumentCallback?t.argumentCallback(e):e]}}function l(t){return function(e){let n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=a.width,i=r&&t.matchPatterns[r]||t.matchPatterns[t.defaultMatchWidth],o=e.match(i);if(!o)return null;let u=o[0],l=r&&t.parsePatterns[r]||t.parsePatterns[t.defaultParseWidth],d=Array.isArray(l)?function(t,e){for(let n=0;n<t.length;n++)if(e(t[n]))return n}(l,t=>t.test(u)):function(t,e){for(let n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e(t[n]))return n}(l,t=>t.test(u));return n=t.valueCallback?t.valueCallback(d):d,{value:n=a.valueCallback?a.valueCallback(n):n,rest:e.slice(u.length)}}}let d={code:"en-US",formatDistance:(t,e,n)=>{let r;let i=a[t];return(r="string"==typeof i?i:1===e?i.one:i.other.replace("{{count}}",e.toString()),null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"in "+r:r+" ago":r},formatLong:i,formatRelative:(t,e,n,a)=>o[t],localize:{ordinalNumber:(t,e)=>{let n=Number(t),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:u({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:u({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:u({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:u({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:u({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(t){return function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=e.match(t.matchPattern);if(!a)return null;let r=a[0],i=e.match(t.parsePattern);if(!i)return null;let o=t.valueCallback?t.valueCallback(i[0]):i[0];return{value:o=n.valueCallback?n.valueCallback(o):o,rest:e.slice(r.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)}),era:l({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:l({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:l({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:l({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:l({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},s={};function c(t){let e=Object.prototype.toString.call(t);return t instanceof Date||"object"==typeof t&&"[object Date]"===e?new t.constructor(+t):new Date("number"==typeof t||"[object Number]"===e||"string"==typeof t||"[object String]"===e?t:NaN)}function h(t){let e=c(t);return e.setHours(0,0,0,0),e}function m(t){let e=c(t),n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return n.setUTCFullYear(e.getFullYear()),+t-+n}function f(t,e){return t instanceof Date?new t.constructor(e):new Date(e)}function g(t,e){var n,a,r,i,o,u,l,d;let h=null!==(d=null!==(l=null!==(u=null!==(o=null==e?void 0:e.weekStartsOn)&&void 0!==o?o:null==e?void 0:null===(a=e.locale)||void 0===a?void 0:null===(n=a.options)||void 0===n?void 0:n.weekStartsOn)&&void 0!==u?u:s.weekStartsOn)&&void 0!==l?l:null===(i=s.locale)||void 0===i?void 0:null===(r=i.options)||void 0===r?void 0:r.weekStartsOn)&&void 0!==d?d:0,m=c(t),f=m.getDay();return m.setDate(m.getDate()-(7*(f<h)+f-h)),m.setHours(0,0,0,0),m}function w(t){return g(t,{weekStartsOn:1})}function v(t){let e=c(t),n=e.getFullYear(),a=f(t,0);a.setFullYear(n+1,0,4),a.setHours(0,0,0,0);let r=w(a),i=f(t,0);i.setFullYear(n,0,4),i.setHours(0,0,0,0);let o=w(i);return e.getTime()>=r.getTime()?n+1:e.getTime()>=o.getTime()?n:n-1}function y(t,e){var n,a,r,i,o,u,l,d;let h=c(t),m=h.getFullYear(),w=null!==(d=null!==(l=null!==(u=null!==(o=null==e?void 0:e.firstWeekContainsDate)&&void 0!==o?o:null==e?void 0:null===(a=e.locale)||void 0===a?void 0:null===(n=a.options)||void 0===n?void 0:n.firstWeekContainsDate)&&void 0!==u?u:s.firstWeekContainsDate)&&void 0!==l?l:null===(i=s.locale)||void 0===i?void 0:null===(r=i.options)||void 0===r?void 0:r.firstWeekContainsDate)&&void 0!==d?d:1,v=f(t,0);v.setFullYear(m+1,0,w),v.setHours(0,0,0,0);let y=g(v,e),b=f(t,0);b.setFullYear(m,0,w),b.setHours(0,0,0,0);let p=g(b,e);return h.getTime()>=y.getTime()?m+1:h.getTime()>=p.getTime()?m:m-1}function b(t,e){let n=Math.abs(t).toString().padStart(e,"0");return(t<0?"-":"")+n}let p={y(t,e){let n=t.getFullYear(),a=n>0?n:1-n;return b("yy"===e?a%100:a,e.length)},M(t,e){let n=t.getMonth();return"M"===e?String(n+1):b(n+1,2)},d:(t,e)=>b(t.getDate(),e.length),a(t,e){let n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(t,e)=>b(t.getHours()%12||12,e.length),H:(t,e)=>b(t.getHours(),e.length),m:(t,e)=>b(t.getMinutes(),e.length),s:(t,e)=>b(t.getSeconds(),e.length),S(t,e){let n=e.length;return b(Math.trunc(t.getMilliseconds()*Math.pow(10,n-3)),e.length)}},M={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},k={G:function(t,e,n){let a=+(t.getFullYear()>0);switch(e){case"G":case"GG":case"GGG":return n.era(a,{width:"abbreviated"});case"GGGGG":return n.era(a,{width:"narrow"});default:return n.era(a,{width:"wide"})}},y:function(t,e,n){if("yo"===e){let e=t.getFullYear();return n.ordinalNumber(e>0?e:1-e,{unit:"year"})}return p.y(t,e)},Y:function(t,e,n,a){let r=y(t,a),i=r>0?r:1-r;return"YY"===e?b(i%100,2):"Yo"===e?n.ordinalNumber(i,{unit:"year"}):b(i,e.length)},R:function(t,e){return b(v(t),e.length)},u:function(t,e){return b(t.getFullYear(),e.length)},Q:function(t,e,n){let a=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(a);case"QQ":return b(a,2);case"Qo":return n.ordinalNumber(a,{unit:"quarter"});case"QQQ":return n.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(a,{width:"narrow",context:"formatting"});default:return n.quarter(a,{width:"wide",context:"formatting"})}},q:function(t,e,n){let a=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(a);case"qq":return b(a,2);case"qo":return n.ordinalNumber(a,{unit:"quarter"});case"qqq":return n.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(a,{width:"narrow",context:"standalone"});default:return n.quarter(a,{width:"wide",context:"standalone"})}},M:function(t,e,n){let a=t.getMonth();switch(e){case"M":case"MM":return p.M(t,e);case"Mo":return n.ordinalNumber(a+1,{unit:"month"});case"MMM":return n.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(a,{width:"narrow",context:"formatting"});default:return n.month(a,{width:"wide",context:"formatting"})}},L:function(t,e,n){let a=t.getMonth();switch(e){case"L":return String(a+1);case"LL":return b(a+1,2);case"Lo":return n.ordinalNumber(a+1,{unit:"month"});case"LLL":return n.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(a,{width:"narrow",context:"standalone"});default:return n.month(a,{width:"wide",context:"standalone"})}},w:function(t,e,n,a){let r=function(t,e){let n=c(t);return Math.round((+g(n,e)-+function(t,e){var n,a,r,i,o,u,l,d;let c=null!==(d=null!==(l=null!==(u=null!==(o=null==e?void 0:e.firstWeekContainsDate)&&void 0!==o?o:null==e?void 0:null===(a=e.locale)||void 0===a?void 0:null===(n=a.options)||void 0===n?void 0:n.firstWeekContainsDate)&&void 0!==u?u:s.firstWeekContainsDate)&&void 0!==l?l:null===(i=s.locale)||void 0===i?void 0:null===(r=i.options)||void 0===r?void 0:r.firstWeekContainsDate)&&void 0!==d?d:1,h=y(t,e),m=f(t,0);return m.setFullYear(h,0,c),m.setHours(0,0,0,0),g(m,e)}(n,e))/6048e5)+1}(t,a);return"wo"===e?n.ordinalNumber(r,{unit:"week"}):b(r,e.length)},I:function(t,e,n){let a=function(t){let e=c(t);return Math.round((+w(e)-+function(t){let e=v(t),n=f(t,0);return n.setFullYear(e,0,4),n.setHours(0,0,0,0),w(n)}(e))/6048e5)+1}(t);return"Io"===e?n.ordinalNumber(a,{unit:"week"}):b(a,e.length)},d:function(t,e,n){return"do"===e?n.ordinalNumber(t.getDate(),{unit:"date"}):p.d(t,e)},D:function(t,e,n){let a=function(t){let e=c(t);return function(t,e){let n=h(t),a=h(e);return Math.round((+n-m(n)-(+a-m(a)))/864e5)}(e,function(t){let e=c(t),n=f(t,0);return n.setFullYear(e.getFullYear(),0,1),n.setHours(0,0,0,0),n}(e))+1}(t);return"Do"===e?n.ordinalNumber(a,{unit:"dayOfYear"}):b(a,e.length)},E:function(t,e,n){let a=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},e:function(t,e,n,a){let r=t.getDay(),i=(r-a.weekStartsOn+8)%7||7;switch(e){case"e":return String(i);case"ee":return b(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},c:function(t,e,n,a){let r=t.getDay(),i=(r-a.weekStartsOn+8)%7||7;switch(e){case"c":return String(i);case"cc":return b(i,e.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(r,{width:"narrow",context:"standalone"});case"cccccc":return n.day(r,{width:"short",context:"standalone"});default:return n.day(r,{width:"wide",context:"standalone"})}},i:function(t,e,n){let a=t.getDay(),r=0===a?7:a;switch(e){case"i":return String(r);case"ii":return b(r,e.length);case"io":return n.ordinalNumber(r,{unit:"day"});case"iii":return n.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},a:function(t,e,n){let a=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(t,e,n){let a;let r=t.getHours();switch(a=12===r?M.noon:0===r?M.midnight:r/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(t,e,n){let a;let r=t.getHours();switch(a=r>=17?M.evening:r>=12?M.afternoon:r>=4?M.morning:M.night,e){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(t,e,n){if("ho"===e){let e=t.getHours()%12;return 0===e&&(e=12),n.ordinalNumber(e,{unit:"hour"})}return p.h(t,e)},H:function(t,e,n){return"Ho"===e?n.ordinalNumber(t.getHours(),{unit:"hour"}):p.H(t,e)},K:function(t,e,n){let a=t.getHours()%12;return"Ko"===e?n.ordinalNumber(a,{unit:"hour"}):b(a,e.length)},k:function(t,e,n){let a=t.getHours();return(0===a&&(a=24),"ko"===e)?n.ordinalNumber(a,{unit:"hour"}):b(a,e.length)},m:function(t,e,n){return"mo"===e?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):p.m(t,e)},s:function(t,e,n){return"so"===e?n.ordinalNumber(t.getSeconds(),{unit:"second"}):p.s(t,e)},S:function(t,e){return p.S(t,e)},X:function(t,e,n){let a=t.getTimezoneOffset();if(0===a)return"Z";switch(e){case"X":return P(a);case"XXXX":case"XX":return S(a);default:return S(a,":")}},x:function(t,e,n){let a=t.getTimezoneOffset();switch(e){case"x":return P(a);case"xxxx":case"xx":return S(a);default:return S(a,":")}},O:function(t,e,n){let a=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+x(a,":");default:return"GMT"+S(a,":")}},z:function(t,e,n){let a=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+x(a,":");default:return"GMT"+S(a,":")}},t:function(t,e,n){return b(Math.trunc(t.getTime()/1e3),e.length)},T:function(t,e,n){return b(t.getTime(),e.length)}};function x(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=t>0?"-":"+",a=Math.abs(t),r=Math.trunc(a/60),i=a%60;return 0===i?n+String(r):n+String(r)+e+b(i,2)}function P(t,e){return t%60==0?(t>0?"-":"+")+b(Math.abs(t)/60,2):S(t,e)}function S(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=Math.abs(t);return(t>0?"-":"+")+b(Math.trunc(n/60),2)+e+b(n%60,2)}let D=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},W=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},T={p:W,P:(t,e)=>{let n;let a=t.match(/(P+)(p+)?/)||[],r=a[1],i=a[2];if(!i)return D(t,e);switch(r){case"P":n=e.dateTime({width:"short"});break;case"PP":n=e.dateTime({width:"medium"});break;case"PPP":n=e.dateTime({width:"long"});break;default:n=e.dateTime({width:"full"})}return n.replace("{{date}}",D(r,e)).replace("{{time}}",W(i,e))}},C=/^D+$/,Y=/^Y+$/,q=["D","DD","YY","YYYY"],A=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,N=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,O=/^'([^]*?)'?$/,H=/''/g,E=/[a-zA-Z]/;function F(t,e,n){var a,r,i,o,u,l,h,m,f,g,w,v,y,b,p,M,x,P;let S=null!==(g=null!==(f=null==n?void 0:n.locale)&&void 0!==f?f:s.locale)&&void 0!==g?g:d,D=null!==(b=null!==(y=null!==(v=null!==(w=null==n?void 0:n.firstWeekContainsDate)&&void 0!==w?w:null==n?void 0:null===(r=n.locale)||void 0===r?void 0:null===(a=r.options)||void 0===a?void 0:a.firstWeekContainsDate)&&void 0!==v?v:s.firstWeekContainsDate)&&void 0!==y?y:null===(o=s.locale)||void 0===o?void 0:null===(i=o.options)||void 0===i?void 0:i.firstWeekContainsDate)&&void 0!==b?b:1,W=null!==(P=null!==(x=null!==(M=null!==(p=null==n?void 0:n.weekStartsOn)&&void 0!==p?p:null==n?void 0:null===(l=n.locale)||void 0===l?void 0:null===(u=l.options)||void 0===u?void 0:u.weekStartsOn)&&void 0!==M?M:s.weekStartsOn)&&void 0!==x?x:null===(m=s.locale)||void 0===m?void 0:null===(h=m.options)||void 0===h?void 0:h.weekStartsOn)&&void 0!==P?P:0,F=c(t);if(!((F instanceof Date||"object"==typeof F&&"[object Date]"===Object.prototype.toString.call(F)||"number"==typeof F)&&!isNaN(Number(c(F)))))throw RangeError("Invalid time value");let L=e.match(N).map(t=>{let e=t[0];return"p"===e||"P"===e?(0,T[e])(t,S.formatLong):t}).join("").match(A).map(t=>{if("''"===t)return{isToken:!1,value:"'"};let e=t[0];if("'"===e)return{isToken:!1,value:function(t){let e=t.match(O);return e?e[1].replace(H,"'"):t}(t)};if(k[e])return{isToken:!0,value:t};if(e.match(E))throw RangeError("Format string contains an unescaped latin alphabet character `"+e+"`");return{isToken:!1,value:t}});S.localize.preprocessor&&(L=S.localize.preprocessor(F,L));let j={firstWeekContainsDate:D,weekStartsOn:W,locale:S};return L.map(a=>{if(!a.isToken)return a.value;let r=a.value;return(!(null==n?void 0:n.useAdditionalWeekYearTokens)&&Y.test(r)||!(null==n?void 0:n.useAdditionalDayOfYearTokens)&&C.test(r))&&!function(t,e,n){let a=function(t,e,n){let a="Y"===t[0]?"years":"days of the month";return"Use `".concat(t.toLowerCase(),"` instead of `").concat(t,"` (in `").concat(e,"`) for formatting ").concat(a," to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md")}(t,e,n);if(console.warn(a),q.includes(t))throw RangeError(a)}(r,e,String(t)),(0,k[r[0]])(F,r,S.localize,j)}).join("")}},38988:(t,e,n)=>{n.d(e,{A:()=>a});let a=(0,n(40157).A)("Coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]])},45219:(t,e,n)=>{n.d(e,{A:()=>a});let a=(0,n(40157).A)("CircleDollarSign",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 18V6",key:"zqpxq5"}]])},58527:(t,e,n)=>{n.d(e,{A:()=>a});let a=(0,n(40157).A)("ChartLine",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},77070:(t,e,n)=>{n.d(e,{A:()=>a});let a=(0,n(40157).A)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},91462:(t,e,n)=>{n.d(e,{A:()=>a});let a=(0,n(40157).A)("ListOrdered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])}}]);