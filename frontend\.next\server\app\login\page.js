(()=>{var e={};e.id=520,e.ids=[520],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7252:e=>{"use strict";e.exports=require("express")},8417:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(60687),a=t(43210),i=t(60240);let o=({className:e,useFullName:r=!0})=>{let[t,o]=(0,a.useState)(!1);return(0,s.jsxs)("div",{className:`flex items-center text-2xl font-bold text-primary ${e}`,children:[t?(0,s.jsx)(i.A,{className:"mr-2 h-7 w-7"}):(0,s.jsx)("img",{src:"https://i.imgur.com/Q0HDcMH.png",alt:"Pluto Trading Bot Logo",className:"mr-2 h-7 w-7 rounded-full object-cover",onError:()=>{o(!0)},onLoad:()=>console.log("Pluto logo loaded successfully")}),(0,s.jsxs)("span",{children:["Pluto",r?" Trading Bot":""]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19185:e=>{"use strict";e.exports=require("dgram")},19771:e=>{"use strict";e.exports=require("process")},20514:(e,r,t)=>{Promise.resolve().then(t.bind(t,67308))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29260:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),o=t.n(i),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94934)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\login\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(60687),a=t(43210),i=t(8730),o=t(24224),n=t(4780);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...o},d)=>{let c=a?i.DX:"button";return(0,s.jsx)(c,{className:(0,n.cn)(l({variant:r,size:t,className:e})),ref:d,...o})});d.displayName="Button"},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41204:e=>{"use strict";e.exports=require("string_decoder")},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>n});var s=t(60687),a=t(43210),i=t(4780);let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",e),...r}));o.displayName="Card";let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-4 md:p-6",e),...r}));n.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-4 md:p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-4 md:p-6 pt-0",e),...r})).displayName="CardFooter"},44708:e=>{"use strict";e.exports=require("node:https")},54379:e=>{"use strict";e.exports=require("node:path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67308:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>S});var s=t(60687),a=t(43210),i=t(63213),o=t(29523),n=t(89667),l=t(80013),d=t(44493),c=t(8417),x=t(11516),u=t(82614);let m=(0,u.A)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]),p=(0,u.A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),h=(0,u.A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),f=(0,u.A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var g=t(67857),b=t(8158),y=t(8751);let v=(0,u.A)("BotMessageSquare",[["path",{d:"M12 6V2H8",key:"1155em"}],["path",{d:"m8 18-4 4V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2Z",key:"w2lp3e"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M9 11v2",key:"1ueba0"}],["path",{d:"M15 11v2",key:"i11awn"}],["path",{d:"M20 12h2",key:"1q8mjw"}]]),j=(0,u.A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),N=(0,u.A)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var w=t(85814),k=t.n(w),q=t(62185);function S(){let[e,r]=(0,a.useState)(""),[t,u]=(0,a.useState)(""),[w,S]=(0,a.useState)(""),[C,P]=(0,a.useState)(!1),{login:A,isLoading:_,isAuthenticated:M}=(0,i.A)(),[R,T]=(0,a.useState)(""),[E,z]=(0,a.useState)(""),[B,F]=(0,a.useState)(!1),[L,Z]=(0,a.useState)(!1),[H,D]=(0,a.useState)(new Date().getFullYear()),[I,$]=(0,a.useState)("checking"),J=async r=>{r.preventDefault(),T("");try{await A(e,t)||T("Invalid credentials. Try using testuser/password123")}catch(e){console.error("Login error:",e),T("Login failed. Please try again.")}},W=async s=>{if(s.preventDefault(),T(""),z(""),!e||!t||!w){T("All fields are required");return}try{await q.ZQ.register(e,t,w),z("Registration successful! You can now log in."),setTimeout(()=>{Z(!1),F(!0),r(""),u(""),S(""),z("")},2e3)}catch(e){console.error("Registration error:",e),T(e.message||"Could not connect to server. Please try again later.")}},U=()=>{Z(!1),F(!0),T(""),z("")},G=()=>{F(!1),Z(!0),T(""),z("")};return _&&!M?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background p-4",children:(0,s.jsx)(x.A,{className:"h-8 w-8 animate-spin text-primary"})}):M?null:(0,s.jsxs)("div",{className:"min-h-screen bg-background text-foreground flex flex-col",children:[(0,s.jsxs)("header",{className:"py-4 px-6 md:px-10 flex justify-between items-center border-b border-border",children:[(0,s.jsx)(c.A,{className:"text-2xl"}),(0,s.jsxs)("nav",{className:"hidden md:flex items-center space-x-6 text-sm",children:[(0,s.jsx)(k(),{href:"#",className:"hover:text-primary",children:"Home"}),(0,s.jsx)(k(),{href:"#",className:"hover:text-primary",children:"Features"}),(0,s.jsx)(k(),{href:"#",className:"hover:text-primary",children:"Pricing"}),(0,s.jsx)(k(),{href:"#",className:"hover:text-primary",children:"Contact"})]}),(0,s.jsxs)("div",{className:"space-x-2",children:[(0,s.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>U(),children:"Login"}),(0,s.jsx)(o.$,{className:"btn-neo",onClick:()=>G(),children:"Register"})]})]}),(0,s.jsxs)("main",{className:"flex-grow flex flex-col items-center justify-center text-center px-4 py-10 md:py-20",children:[(0,s.jsxs)("h1",{className:"text-4xl sm:text-5xl md:text-6xl font-bold mb-6",children:[(0,s.jsx)("span",{className:"text-primary",children:"Pluto"})," Trading Bot Platform"]}),(0,s.jsx)("p",{className:"text-lg md:text-xl text-muted-foreground max-w-2xl mb-10",children:"Automate your trading strategy with our powerful Simple Spot and Stablecoin Swap bots. Maximize your profits with minimal effort."}),!B&&!L&&(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4",children:[(0,s.jsxs)(o.$,{size:"lg",className:"btn-outline-neo text-lg px-8 py-4",onClick:()=>U(),children:[(0,s.jsx)(m,{className:"mr-2 h-5 w-5"})," Login to Trading Platform"]}),(0,s.jsxs)(o.$,{size:"lg",className:"btn-neo text-lg px-8 py-4",onClick:()=>G(),children:[(0,s.jsx)(p,{className:"mr-2 h-5 w-5"})," Create Free Account"]})]}),B&&(0,s.jsxs)(d.Zp,{className:"w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10",children:[(0,s.jsxs)(d.aR,{className:"text-center",children:[(0,s.jsx)(d.ZB,{className:"text-3xl font-bold",children:"Account Login"}),(0,s.jsx)(d.BT,{children:"Access your Pluto Trading Bot dashboard."})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:J,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"username",className:"text-lg sr-only",children:"Username"}),(0,s.jsx)(n.p,{id:"username",type:"text",value:e,onChange:e=>r(e.target.value),required:!0,className:"text-base",placeholder:"Username"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"password",className:"text-lg sr-only",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.p,{id:"password",type:C?"text":"password",value:t,onChange:e=>u(e.target.value),required:!0,className:"text-base pr-10",placeholder:"Password (try: password123)"}),(0,s.jsx)("button",{type:"button",onClick:()=>P(!C),className:"absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground","aria-label":C?"Hide password":"Show password",children:C?(0,s.jsx)(h,{className:"h-5 w-5"}):(0,s.jsx)(f,{className:"h-5 w-5"})})]})]}),R&&(0,s.jsx)("p",{className:"text-destructive text-sm",children:R}),E&&(0,s.jsx)("p",{className:"text-green-500 text-sm",children:E}),(0,s.jsx)(o.$,{type:"submit",className:"w-full btn-neo text-lg py-3",disabled:_||"offline"===I,children:_?(0,s.jsx)(x.A,{className:"mr-2 h-5 w-5 animate-spin"}):"Login"}),(0,s.jsxs)("div",{className:"flex items-center justify-center mt-2 text-sm",children:["checking"===I&&(0,s.jsxs)("div",{className:"flex items-center text-orange-500",children:[(0,s.jsx)(x.A,{className:"h-3 w-3 mr-1 animate-spin"}),(0,s.jsx)("span",{children:"Checking server connection..."})]}),"online"===I&&(0,s.jsxs)("div",{className:"flex items-center text-green-500",children:[(0,s.jsx)(g.A,{className:"h-3 w-3 mr-1"}),(0,s.jsx)("span",{children:"Server connected"})]}),"offline"===I&&(0,s.jsxs)("div",{className:"flex items-center text-destructive",children:[(0,s.jsx)(b.A,{className:"h-3 w-3 mr-1"}),(0,s.jsx)("span",{children:"Server offline - Please start the backend"})]})]}),(0,s.jsxs)("div",{className:"text-center text-sm text-muted-foreground space-y-2 pt-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("a",{href:"#",className:"hover:text-primary underline",children:"Forgot password?"})," (Simulated)"]}),(0,s.jsxs)("p",{children:["Don't have an account? ",(0,s.jsx)("button",{type:"button",onClick:G,className:"hover:text-primary underline",children:"Create Account"})]})]})]})})]}),L&&(0,s.jsxs)(d.Zp,{className:"w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10",children:[(0,s.jsxs)(d.aR,{className:"text-center",children:[(0,s.jsx)(d.ZB,{className:"text-3xl font-bold",children:"Create Account"}),(0,s.jsx)(d.BT,{children:"Join Pluto Trading Bot platform."})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:W,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"reg-username",className:"text-lg",children:"Username"}),(0,s.jsx)(n.p,{id:"reg-username",type:"text",value:e,onChange:e=>r(e.target.value),required:!0,className:"text-base",placeholder:"Choose a username"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"reg-email",className:"text-lg",children:"Email"}),(0,s.jsx)(n.p,{id:"reg-email",type:"email",value:w,onChange:e=>S(e.target.value),required:!0,className:"text-base",placeholder:"Your email address"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"reg-password",className:"text-lg",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.p,{id:"reg-password",type:C?"text":"password",value:t,onChange:e=>u(e.target.value),required:!0,className:"text-base pr-10",placeholder:"Create a password"}),(0,s.jsx)("button",{type:"button",onClick:()=>P(!C),className:"absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground","aria-label":C?"Hide password":"Show password",children:C?(0,s.jsx)(h,{className:"h-5 w-5"}):(0,s.jsx)(f,{className:"h-5 w-5"})})]})]}),R&&(0,s.jsx)("p",{className:"text-destructive text-sm",children:R}),E&&(0,s.jsx)("p",{className:"text-green-500 text-sm",children:E}),(0,s.jsx)(o.$,{type:"submit",className:"w-full btn-neo text-lg py-3",disabled:_,children:_?(0,s.jsx)(x.A,{className:"mr-2 h-5 w-5 animate-spin"}):"Register"}),(0,s.jsx)("div",{className:"text-center text-sm text-muted-foreground pt-2",children:(0,s.jsxs)("p",{children:["Already have an account? ",(0,s.jsx)("button",{type:"button",onClick:U,className:"hover:text-primary underline",children:"Login"})]})})]})})]})]}),(0,s.jsx)("section",{className:"py-10 md:py-16 bg-card border-t border-border",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold mb-8 text-primary",children:"Trading Strategies"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-12",children:[(0,s.jsxs)("div",{className:"border-2 border-border p-6 rounded-sm bg-background/30",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-3",children:"Simple Spot Mode"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Execute direct buy and sell orders based on your target prices. Ideal for straightforward market participation and capitalizing on price movements."})]}),(0,s.jsxs)("div",{className:"border-2 border-border p-6 rounded-sm bg-background/30",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-3",children:"Stablecoin Swap Mode"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Leverage stablecoins in your trading strategy, aiming for value preservation while capitalizing on market volatility against your chosen crypto assets."})]})]}),(0,s.jsx)("h2",{className:"text-3xl font-bold mb-8 text-primary mt-16",children:"Why Pluto?"}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto mb-12",children:[{icon:(0,s.jsx)(y.A,{className:"h-10 w-10 text-primary mb-3"}),title:"Automated Profits",description:"24/7 trading execution, so you never miss an opportunity."},{icon:(0,s.jsx)(v,{className:"h-10 w-10 text-primary mb-3"}),title:"AI-Powered Insights",description:"Smart suggestions to help you choose the best trading mode."},{icon:(0,s.jsx)(j,{className:"h-10 w-10 text-primary mb-3"}),title:"Dual Strategy Modes",description:"Flexible Simple Spot and Stablecoin Swap options to fit your style."},{icon:(0,s.jsx)(N,{className:"h-10 w-10 text-primary mb-3"}),title:"Secure Simulation",description:"Test strategies risk-free in a simulated environment."}].map(e=>(0,s.jsxs)("div",{className:"border-2 border-border p-6 rounded-sm bg-background/30 flex flex-col items-center",children:[e.icon,(0,s.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]},e.title))}),(0,s.jsx)("h2",{className:"text-3xl font-bold mb-8 text-primary mt-16",children:"What Our Users Say"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,s.jsx)(d.Zp,{className:"border-2 border-border bg-background/30 p-6 text-left",children:(0,s.jsxs)(d.Wu,{className:"p-0",children:[(0,s.jsx)("blockquote",{className:"text-muted-foreground italic",children:'"Pluto\'s Simple Spot mode helped me capture quick profits effortlessly! The interface is so intuitive."'}),(0,s.jsx)("p",{className:"text-right font-semibold text-primary mt-3",children:"- TraderX (Simulated)"})]})}),(0,s.jsx)(d.Zp,{className:"border-2 border-border bg-background/30 p-6 text-left",children:(0,s.jsxs)(d.Wu,{className:"p-0",children:[(0,s.jsx)("blockquote",{className:"text-muted-foreground italic",children:'"The Stablecoin Swap is perfect for my long-term strategy. Love the AI suggestions for mode selection!"'}),(0,s.jsx)("p",{className:"text-right font-semibold text-primary mt-3",children:"- Crypto Enthusiast (Simulated)"})]})})]})]})}),(0,s.jsxs)("footer",{className:"py-6 text-center text-sm text-muted-foreground border-t border-border",children:["\xa9 ",H," Pluto Trading. All Rights Reserved (Simulation)."]})]})}},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80013:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var s=t(60687),a=t(43210),i=t(78148),o=t(24224),n=t(4780);let l=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)(i.b,{ref:t,className:(0,n.cn)(l(),e),...r}));d.displayName=i.b.displayName},80346:(e,r,t)=>{Promise.resolve().then(t.bind(t,94934))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var s=t(60687),a=t(43210),i=t(4780);let o=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));o.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\login\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[992,611,740],()=>t(29260));module.exports=s})();