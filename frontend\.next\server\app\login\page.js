(()=>{var e={};e.id=520,e.ids=[520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8417:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(60687),a=r(43210),n=r(60240);let o=({className:e,useFullName:t=!0})=>{let[r,o]=(0,a.useState)(!1);return(0,s.jsxs)("div",{className:`flex items-center text-2xl font-bold text-primary ${e}`,children:[r?(0,s.jsx)(n.A,{className:"mr-2 h-7 w-7"}):(0,s.jsx)("img",{src:"https://i.imgur.com/Q0HDcMH.png",alt:"Pluto Trading Bot Logo",className:"mr-2 h-7 w-7 rounded-full object-cover",onError:()=>{o(!0)},onLoad:()=>console.log("Pluto logo loaded successfully")}),(0,s.jsxs)("span",{children:["Pluto",t?" Trading Bot":""]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20514:(e,t,r)=>{Promise.resolve().then(r.bind(r,67308))},29260:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94934)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\login\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(60687),a=r(43210),n=r(8730),o=r(24224),i=r(4780);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...o},d)=>{let c=a?n.DX:"button";return(0,s.jsx)(c,{className:(0,i.cn)(l({variant:t,size:r,className:e})),ref:d,...o})});d.displayName="Button"},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i});var s=r(60687),a=r(43210),n=r(4780);let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",e),...t}));o.displayName="Card";let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-4 md:p-6",e),...t}));i.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-4 md:p-6 pt-0",e),...t}));c.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-4 md:p-6 pt-0",e),...t})).displayName="CardFooter"},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67308:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(60687),a=r(43210),n=r(63213),o=r(29523),i=r(89667),l=r(80013),d=r(44493),c=r(8417),m=r(11516),x=r(82614);let p=(0,x.A)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]),u=(0,x.A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),h=(0,x.A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]),g=(0,x.A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var f=r(67857),b=r(85777),y=r(8751);let v=(0,x.A)("BotMessageSquare",[["path",{d:"M12 6V2H8",key:"1155em"}],["path",{d:"m8 18-4 4V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2Z",key:"w2lp3e"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M9 11v2",key:"1ueba0"}],["path",{d:"M15 11v2",key:"i11awn"}],["path",{d:"M20 12h2",key:"1q8mjw"}]]),j=(0,x.A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),N=(0,x.A)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var w=r(85814),k=r.n(w),S=r(62185);function C(){let[e,t]=(0,a.useState)(""),[r,x]=(0,a.useState)(""),[w,C]=(0,a.useState)(""),[P,A]=(0,a.useState)(!1),{login:M,isLoading:_,isAuthenticated:q}=(0,n.A)(),[R,T]=(0,a.useState)(""),[E,z]=(0,a.useState)(""),[B,F]=(0,a.useState)(!1),[L,Z]=(0,a.useState)(!1),[H,D]=(0,a.useState)(new Date().getFullYear()),[I,$]=(0,a.useState)("checking"),J=async t=>{t.preventDefault(),T("");try{await M(e,r)||T("Invalid credentials. Try using testuser/password123")}catch(e){console.error("Login error:",e),T("Login failed. Please try again.")}},W=async s=>{if(s.preventDefault(),T(""),z(""),!e||!r||!w){T("All fields are required");return}try{await S.ZQ.register(e,r,w),z("Registration successful! You can now log in."),setTimeout(()=>{Z(!1),F(!0),t(""),x(""),C(""),z("")},2e3)}catch(e){console.error("Registration error:",e),T(e.message||"Could not connect to server. Please try again later.")}},U=()=>{Z(!1),F(!0),T(""),z("")},G=()=>{F(!1),Z(!0),T(""),z("")};return _&&!q?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background p-4",children:(0,s.jsx)(m.A,{className:"h-8 w-8 animate-spin text-primary"})}):q?null:(0,s.jsxs)("div",{className:"min-h-screen bg-background text-foreground flex flex-col",children:[(0,s.jsxs)("header",{className:"py-4 px-6 md:px-10 flex justify-between items-center border-b border-border",children:[(0,s.jsx)(c.A,{className:"text-2xl"}),(0,s.jsxs)("nav",{className:"hidden md:flex items-center space-x-6 text-sm",children:[(0,s.jsx)(k(),{href:"#",className:"hover:text-primary",children:"Home"}),(0,s.jsx)(k(),{href:"#",className:"hover:text-primary",children:"Features"}),(0,s.jsx)(k(),{href:"#",className:"hover:text-primary",children:"Pricing"}),(0,s.jsx)(k(),{href:"#",className:"hover:text-primary",children:"Contact"})]}),(0,s.jsxs)("div",{className:"space-x-2",children:[(0,s.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>U(),children:"Login"}),(0,s.jsx)(o.$,{className:"btn-neo",onClick:()=>G(),children:"Register"})]})]}),(0,s.jsxs)("main",{className:"flex-grow flex flex-col items-center justify-center text-center px-4 py-10 md:py-20",children:[(0,s.jsxs)("h1",{className:"text-4xl sm:text-5xl md:text-6xl font-bold mb-6",children:[(0,s.jsx)("span",{className:"text-primary",children:"Pluto"})," Trading Bot Platform"]}),(0,s.jsx)("p",{className:"text-lg md:text-xl text-muted-foreground max-w-2xl mb-10",children:"Automate your trading strategy with our powerful Simple Spot and Stablecoin Swap bots. Maximize your profits with minimal effort."}),!B&&!L&&(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4",children:[(0,s.jsxs)(o.$,{size:"lg",className:"btn-outline-neo text-lg px-8 py-4",onClick:()=>U(),children:[(0,s.jsx)(p,{className:"mr-2 h-5 w-5"})," Login to Trading Platform"]}),(0,s.jsxs)(o.$,{size:"lg",className:"btn-neo text-lg px-8 py-4",onClick:()=>G(),children:[(0,s.jsx)(u,{className:"mr-2 h-5 w-5"})," Create Free Account"]})]}),B&&(0,s.jsxs)(d.Zp,{className:"w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10",children:[(0,s.jsxs)(d.aR,{className:"text-center",children:[(0,s.jsx)(d.ZB,{className:"text-3xl font-bold",children:"Account Login"}),(0,s.jsx)(d.BT,{children:"Access your Pluto Trading Bot dashboard."})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:J,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"username",className:"text-lg sr-only",children:"Username"}),(0,s.jsx)(i.p,{id:"username",type:"text",value:e,onChange:e=>t(e.target.value),required:!0,className:"text-base",placeholder:"Username"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"password",className:"text-lg sr-only",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.p,{id:"password",type:P?"text":"password",value:r,onChange:e=>x(e.target.value),required:!0,className:"text-base pr-10",placeholder:"Password (try: password123)"}),(0,s.jsx)("button",{type:"button",onClick:()=>A(!P),className:"absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground","aria-label":P?"Hide password":"Show password",children:P?(0,s.jsx)(h,{className:"h-5 w-5"}):(0,s.jsx)(g,{className:"h-5 w-5"})})]})]}),R&&(0,s.jsx)("p",{className:"text-destructive text-sm",children:R}),E&&(0,s.jsx)("p",{className:"text-green-500 text-sm",children:E}),(0,s.jsx)(o.$,{type:"submit",className:"w-full btn-neo text-lg py-3",disabled:_||"offline"===I,children:_?(0,s.jsx)(m.A,{className:"mr-2 h-5 w-5 animate-spin"}):"Login"}),(0,s.jsxs)("div",{className:"flex items-center justify-center mt-2 text-sm",children:["checking"===I&&(0,s.jsxs)("div",{className:"flex items-center text-orange-500",children:[(0,s.jsx)(m.A,{className:"h-3 w-3 mr-1 animate-spin"}),(0,s.jsx)("span",{children:"Checking server connection..."})]}),"online"===I&&(0,s.jsxs)("div",{className:"flex items-center text-green-500",children:[(0,s.jsx)(f.A,{className:"h-3 w-3 mr-1"}),(0,s.jsx)("span",{children:"Server connected"})]}),"offline"===I&&(0,s.jsxs)("div",{className:"flex items-center text-destructive",children:[(0,s.jsx)(b.A,{className:"h-3 w-3 mr-1"}),(0,s.jsx)("span",{children:"Server offline - Please start the backend"})]})]}),(0,s.jsxs)("div",{className:"text-center text-sm text-muted-foreground space-y-2 pt-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("a",{href:"#",className:"hover:text-primary underline",children:"Forgot password?"})," (Simulated)"]}),(0,s.jsxs)("p",{children:["Don't have an account? ",(0,s.jsx)("button",{type:"button",onClick:G,className:"hover:text-primary underline",children:"Create Account"})]})]})]})})]}),L&&(0,s.jsxs)(d.Zp,{className:"w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10",children:[(0,s.jsxs)(d.aR,{className:"text-center",children:[(0,s.jsx)(d.ZB,{className:"text-3xl font-bold",children:"Create Account"}),(0,s.jsx)(d.BT,{children:"Join Pluto Trading Bot platform."})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:W,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"reg-username",className:"text-lg",children:"Username"}),(0,s.jsx)(i.p,{id:"reg-username",type:"text",value:e,onChange:e=>t(e.target.value),required:!0,className:"text-base",placeholder:"Choose a username"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"reg-email",className:"text-lg",children:"Email"}),(0,s.jsx)(i.p,{id:"reg-email",type:"email",value:w,onChange:e=>C(e.target.value),required:!0,className:"text-base",placeholder:"Your email address"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"reg-password",className:"text-lg",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.p,{id:"reg-password",type:P?"text":"password",value:r,onChange:e=>x(e.target.value),required:!0,className:"text-base pr-10",placeholder:"Create a password"}),(0,s.jsx)("button",{type:"button",onClick:()=>A(!P),className:"absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground","aria-label":P?"Hide password":"Show password",children:P?(0,s.jsx)(h,{className:"h-5 w-5"}):(0,s.jsx)(g,{className:"h-5 w-5"})})]})]}),R&&(0,s.jsx)("p",{className:"text-destructive text-sm",children:R}),E&&(0,s.jsx)("p",{className:"text-green-500 text-sm",children:E}),(0,s.jsx)(o.$,{type:"submit",className:"w-full btn-neo text-lg py-3",disabled:_,children:_?(0,s.jsx)(m.A,{className:"mr-2 h-5 w-5 animate-spin"}):"Register"}),(0,s.jsx)("div",{className:"text-center text-sm text-muted-foreground pt-2",children:(0,s.jsxs)("p",{children:["Already have an account? ",(0,s.jsx)("button",{type:"button",onClick:U,className:"hover:text-primary underline",children:"Login"})]})})]})})]})]}),(0,s.jsx)("section",{className:"py-10 md:py-16 bg-card border-t border-border",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold mb-8 text-primary",children:"Trading Strategies"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-12",children:[(0,s.jsxs)("div",{className:"border-2 border-border p-6 rounded-sm bg-background/30",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-3",children:"Simple Spot Mode"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Execute direct buy and sell orders based on your target prices. Ideal for straightforward market participation and capitalizing on price movements."})]}),(0,s.jsxs)("div",{className:"border-2 border-border p-6 rounded-sm bg-background/30",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-3",children:"Stablecoin Swap Mode"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Leverage stablecoins in your trading strategy, aiming for value preservation while capitalizing on market volatility against your chosen crypto assets."})]})]}),(0,s.jsx)("h2",{className:"text-3xl font-bold mb-8 text-primary mt-16",children:"Why Pluto?"}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto mb-12",children:[{icon:(0,s.jsx)(y.A,{className:"h-10 w-10 text-primary mb-3"}),title:"Automated Profits",description:"24/7 trading execution, so you never miss an opportunity."},{icon:(0,s.jsx)(v,{className:"h-10 w-10 text-primary mb-3"}),title:"AI-Powered Insights",description:"Smart suggestions to help you choose the best trading mode."},{icon:(0,s.jsx)(j,{className:"h-10 w-10 text-primary mb-3"}),title:"Dual Strategy Modes",description:"Flexible Simple Spot and Stablecoin Swap options to fit your style."},{icon:(0,s.jsx)(N,{className:"h-10 w-10 text-primary mb-3"}),title:"Secure Simulation",description:"Test strategies risk-free in a simulated environment."}].map(e=>(0,s.jsxs)("div",{className:"border-2 border-border p-6 rounded-sm bg-background/30 flex flex-col items-center",children:[e.icon,(0,s.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]},e.title))}),(0,s.jsx)("h2",{className:"text-3xl font-bold mb-8 text-primary mt-16",children:"What Our Users Say"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,s.jsx)(d.Zp,{className:"border-2 border-border bg-background/30 p-6 text-left",children:(0,s.jsxs)(d.Wu,{className:"p-0",children:[(0,s.jsx)("blockquote",{className:"text-muted-foreground italic",children:'"Pluto\'s Simple Spot mode helped me capture quick profits effortlessly! The interface is so intuitive."'}),(0,s.jsx)("p",{className:"text-right font-semibold text-primary mt-3",children:"- TraderX (Simulated)"})]})}),(0,s.jsx)(d.Zp,{className:"border-2 border-border bg-background/30 p-6 text-left",children:(0,s.jsxs)(d.Wu,{className:"p-0",children:[(0,s.jsx)("blockquote",{className:"text-muted-foreground italic",children:'"The Stablecoin Swap is perfect for my long-term strategy. Love the AI suggestions for mode selection!"'}),(0,s.jsx)("p",{className:"text-right font-semibold text-primary mt-3",children:"- Crypto Enthusiast (Simulated)"})]})})]})]})}),(0,s.jsxs)("footer",{className:"py-6 text-center text-sm text-muted-foreground border-t border-border",children:["\xa9 ",H," Pluto Trading. All Rights Reserved (Simulation)."]})]})}},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var s=r(60687),a=r(43210),n=r(78148),o=r(24224),i=r(4780);let l=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.b,{ref:r,className:(0,i.cn)(l(),e),...t}));d.displayName=n.b.displayName},80346:(e,t,r)=>{Promise.resolve().then(r.bind(r,94934))},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var s=r(60687),a=r(43210),n=r(4780);let o=a.forwardRef(({className:e,type:t,...r},a)=>(0,s.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...r}));o.displayName="Input"},94934:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\login\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[388,992,475],()=>r(29260));module.exports=s})();