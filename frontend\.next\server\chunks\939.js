"use strict";exports.id=939,exports.ids=[939],exports.modules={1305:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},1359:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(43210),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},1833:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},3341:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("ChartLine",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},9812:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("Coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]])},18329:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},18853:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(43210),o=n(66156);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},19422:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},21277:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("CircleDollarSign",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 18V6",key:"zqpxq5"}]])},24851:(e,t,n)=>{n.d(t,{CC:()=>q,Q6:()=>V,bL:()=>z,zi:()=>G});var r=n(43210),o=n(67969),i=n(70569),a=n(98599),l=n(11273),s=n(65551),u=n(43),c=n(83721),d=n(18853),f=n(14163),p=n(9510),h=n(60687),m=["PageUp","PageDown"],v=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],g={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},y="Slider",[w,x,b]=(0,p.N)(y),[S,E]=(0,l.A)(y,[b]),[k,A]=S(y),C=r.forwardRef((e,t)=>{let{name:n,min:a=0,max:l=100,step:u=1,orientation:c="horizontal",disabled:d=!1,minStepsBetweenThumbs:f=0,defaultValue:p=[a],value:g,onValueChange:y=()=>{},onValueCommit:x=()=>{},inverted:b=!1,form:S,...E}=e,A=r.useRef(new Set),C=r.useRef(0),R="horizontal"===c,[M=[],T]=(0,s.i)({prop:g,defaultProp:p,onChange:e=>{let t=[...A.current];t[C.current]?.focus(),y(e)}}),D=r.useRef(M);function L(e,t,{commit:n}={commit:!1}){let r=(String(u).split(".")[1]||"").length,i=function(e,t){let n=Math.pow(10,t);return Math.round(e*n)/n}(Math.round((e-a)/u)*u+a,r),s=(0,o.q)(i,[a,l]);T((e=[])=>{let r=function(e=[],t,n){let r=[...e];return r[n]=t,r.sort((e,t)=>e-t)}(e,s,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,n)=>e[n+1]-t))>=t;return!0}(r,f*u))return e;{C.current=r.indexOf(s);let t=String(r)!==String(e);return t&&n&&x(r),t?r:e}})}return(0,h.jsx)(k,{scope:e.__scopeSlider,name:n,disabled:d,min:a,max:l,valueIndexToChangeRef:C,thumbs:A.current,values:M,orientation:c,form:S,children:(0,h.jsx)(w.Provider,{scope:e.__scopeSlider,children:(0,h.jsx)(w.Slot,{scope:e.__scopeSlider,children:(0,h.jsx)(R?j:P,{"aria-disabled":d,"data-disabled":d?"":void 0,...E,ref:t,onPointerDown:(0,i.m)(E.onPointerDown,()=>{d||(D.current=M)}),min:a,max:l,inverted:b,onSlideStart:d?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let n=e.map(e=>Math.abs(e-t)),r=Math.min(...n);return n.indexOf(r)}(M,e);L(e,t)},onSlideMove:d?void 0:function(e){L(e,C.current)},onSlideEnd:d?void 0:function(){let e=D.current[C.current];M[C.current]!==e&&x(M)},onHomeKeyDown:()=>!d&&L(a,0,{commit:!0}),onEndKeyDown:()=>!d&&L(l,M.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!d){let n=m.includes(e.key)||e.shiftKey&&v.includes(e.key),r=C.current;L(M[r]+u*(n?10:1)*t,r,{commit:!0})}}})})})})});C.displayName=y;var[R,M]=S(y,{startEdge:"left",endEdge:"right",size:"width",direction:1}),j=r.forwardRef((e,t)=>{let{min:n,max:o,dir:i,inverted:l,onSlideStart:s,onSlideMove:c,onSlideEnd:d,onStepKeyDown:f,...p}=e,[m,v]=r.useState(null),y=(0,a.s)(t,e=>v(e)),w=r.useRef(void 0),x=(0,u.jH)(i),b="ltr"===x,S=b&&!l||!b&&l;function E(e){let t=w.current||m.getBoundingClientRect(),r=W([0,t.width],S?[n,o]:[o,n]);return w.current=t,r(e-t.left)}return(0,h.jsx)(R,{scope:e.__scopeSlider,startEdge:S?"left":"right",endEdge:S?"right":"left",direction:S?1:-1,size:"width",children:(0,h.jsx)(T,{dir:x,"data-orientation":"horizontal",...p,ref:y,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=E(e.clientX);s?.(t)},onSlideMove:e=>{let t=E(e.clientX);c?.(t)},onSlideEnd:()=>{w.current=void 0,d?.()},onStepKeyDown:e=>{let t=g[S?"from-left":"from-right"].includes(e.key);f?.({event:e,direction:t?-1:1})}})})}),P=r.forwardRef((e,t)=>{let{min:n,max:o,inverted:i,onSlideStart:l,onSlideMove:s,onSlideEnd:u,onStepKeyDown:c,...d}=e,f=r.useRef(null),p=(0,a.s)(t,f),m=r.useRef(void 0),v=!i;function y(e){let t=m.current||f.current.getBoundingClientRect(),r=W([0,t.height],v?[o,n]:[n,o]);return m.current=t,r(e-t.top)}return(0,h.jsx)(R,{scope:e.__scopeSlider,startEdge:v?"bottom":"top",endEdge:v?"top":"bottom",size:"height",direction:v?1:-1,children:(0,h.jsx)(T,{"data-orientation":"vertical",...d,ref:p,style:{...d.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=y(e.clientY);l?.(t)},onSlideMove:e=>{let t=y(e.clientY);s?.(t)},onSlideEnd:()=>{m.current=void 0,u?.()},onStepKeyDown:e=>{let t=g[v?"from-bottom":"from-top"].includes(e.key);c?.({event:e,direction:t?-1:1})}})})}),T=r.forwardRef((e,t)=>{let{__scopeSlider:n,onSlideStart:r,onSlideMove:o,onSlideEnd:a,onHomeKeyDown:l,onEndKeyDown:s,onStepKeyDown:u,...c}=e,d=A(y,n);return(0,h.jsx)(f.sG.span,{...c,ref:t,onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Home"===e.key?(l(e),e.preventDefault()):"End"===e.key?(s(e),e.preventDefault()):m.concat(v).includes(e.key)&&(u(e),e.preventDefault())}),onPointerDown:(0,i.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),d.thumbs.has(t)?t.focus():r(e)}),onPointerMove:(0,i.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&o(e)}),onPointerUp:(0,i.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),a(e))})})}),D="SliderTrack",L=r.forwardRef((e,t)=>{let{__scopeSlider:n,...r}=e,o=A(D,n);return(0,h.jsx)(f.sG.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...r,ref:t})});L.displayName=D;var N="SliderRange",I=r.forwardRef((e,t)=>{let{__scopeSlider:n,...o}=e,i=A(N,n),l=M(N,n),s=r.useRef(null),u=(0,a.s)(t,s),c=i.values.length,d=i.values.map(e=>B(e,i.min,i.max)),p=c>1?Math.min(...d):0,m=100-Math.max(...d);return(0,h.jsx)(f.sG.span,{"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,...o,ref:u,style:{...e.style,[l.startEdge]:p+"%",[l.endEdge]:m+"%"}})});I.displayName=N;var O="SliderThumb",H=r.forwardRef((e,t)=>{let n=x(e.__scopeSlider),[o,i]=r.useState(null),l=(0,a.s)(t,e=>i(e)),s=r.useMemo(()=>o?n().findIndex(e=>e.ref.current===o):-1,[n,o]);return(0,h.jsx)(F,{...e,ref:l,index:s})}),F=r.forwardRef((e,t)=>{let{__scopeSlider:n,index:o,name:l,...s}=e,u=A(O,n),c=M(O,n),[p,m]=r.useState(null),v=(0,a.s)(t,e=>m(e)),g=!p||u.form||!!p.closest("form"),y=(0,d.X)(p),x=u.values[o],b=void 0===x?0:B(x,u.min,u.max),S=function(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}(o,u.values.length),E=y?.[c.size],k=E?function(e,t,n){let r=e/2,o=W([0,50],[0,r]);return(r-o(t)*n)*n}(E,b,c.direction):0;return r.useEffect(()=>{if(p)return u.thumbs.add(p),()=>{u.thumbs.delete(p)}},[p,u.thumbs]),(0,h.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[c.startEdge]:`calc(${b}% + ${k}px)`},children:[(0,h.jsx)(w.ItemSlot,{scope:e.__scopeSlider,children:(0,h.jsx)(f.sG.span,{role:"slider","aria-label":e["aria-label"]||S,"aria-valuemin":u.min,"aria-valuenow":x,"aria-valuemax":u.max,"aria-orientation":u.orientation,"data-orientation":u.orientation,"data-disabled":u.disabled?"":void 0,tabIndex:u.disabled?void 0:0,...s,ref:v,style:void 0===x?{display:"none"}:e.style,onFocus:(0,i.m)(e.onFocus,()=>{u.valueIndexToChangeRef.current=o})})}),g&&(0,h.jsx)(_,{name:l??(u.name?u.name+(u.values.length>1?"[]":""):void 0),form:u.form,value:x},o)]})});H.displayName=O;var _=r.forwardRef(({__scopeSlider:e,value:t,...n},o)=>{let i=r.useRef(null),l=(0,a.s)(i,o),s=(0,c.Z)(t);return r.useEffect(()=>{let e=i.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(s!==t&&n){let r=new Event("input",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[s,t]),(0,h.jsx)(f.sG.input,{style:{display:"none"},...n,ref:l,defaultValue:t})});function B(e,t,n){return(0,o.q)(100/(n-t)*(e-t),[0,100])}function W(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}_.displayName="RadioBubbleInput";var z=C,q=L,V=I,G=H},25371:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("PowerOff",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},26134:(e,t,n)=>{n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>ei,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=n(43210),o=n(70569),i=n(98599),a=n(11273),l=n(96963),s=n(65551),u=n(31355),c=n(32547),d=n(25028),f=n(46059),p=n(14163),h=n(1359),m=n(42247),v=n(63376),g=n(8730),y=n(60687),w="Dialog",[x,b]=(0,a.A)(w),[S,E]=x(w),k=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:a,modal:u=!0}=e,c=r.useRef(null),d=r.useRef(null),[f,p]=(0,s.i)({prop:o,defaultProp:i??!1,onChange:a,caller:w});return(0,y.jsx)(S,{scope:t,triggerRef:c,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:u,children:n})};k.displayName=w;var A="DialogTrigger",C=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=E(A,n),l=(0,i.s)(t,a.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":K(a.open),...r,ref:l,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});C.displayName=A;var R="DialogPortal",[M,j]=x(R,{forceMount:void 0}),P=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,a=E(R,t);return(0,y.jsx)(M,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:n||a.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};P.displayName=R;var T="DialogOverlay",D=r.forwardRef((e,t)=>{let n=j(T,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=E(T,e.__scopeDialog);return i.modal?(0,y.jsx)(f.C,{present:r||i.open,children:(0,y.jsx)(N,{...o,ref:t})}):null});D.displayName=T;var L=(0,g.TL)("DialogOverlay.RemoveScroll"),N=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(T,n);return(0,y.jsx)(m.A,{as:L,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":K(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),I="DialogContent",O=r.forwardRef((e,t)=>{let n=j(I,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=E(I,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||i.open,children:i.modal?(0,y.jsx)(H,{...o,ref:t}):(0,y.jsx)(F,{...o,ref:t})})});O.displayName=I;var H=r.forwardRef((e,t)=>{let n=E(I,e.__scopeDialog),a=r.useRef(null),l=(0,i.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(_,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=r.forwardRef((e,t)=>{let n=E(I,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(_,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),_=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l,...s}=e,d=E(I,n),f=r.useRef(null),p=(0,i.s)(t,f);return(0,h.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,y.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":K(d.open),...s,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Y,{titleId:d.titleId}),(0,y.jsx)(Z,{contentRef:f,descriptionId:d.descriptionId})]})]})}),B="DialogTitle",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(B,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});W.displayName=B;var z="DialogDescription",q=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=E(z,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});q.displayName=z;var V="DialogClose",G=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=E(V,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function K(e){return e?"open":"closed"}G.displayName=V;var $="DialogTitleWarning",[U,X]=(0,a.q)($,{contentName:I,titleName:B,docsSlug:"dialog"}),Y=({titleId:e})=>{let t=X($),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},Z=({contentRef:e,descriptionId:t})=>{let n=X("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},J=k,Q=C,ee=P,et=D,en=O,er=W,eo=q,ei=G},28850:(e,t,n)=>{n.d(t,{UC:()=>nf,YJ:()=>nh,In:()=>nc,q7:()=>nv,VF:()=>ny,p4:()=>ng,JU:()=>nm,ZL:()=>nd,bL:()=>nl,wn:()=>nx,PP:()=>nw,wv:()=>nb,l9:()=>ns,WT:()=>nu,LM:()=>np});var r=n(43210),o=n(51215),i=n(67969),a=n(70569),l=n(9510),s=n(98599),u=n(11273),c=n(43),d=n(31355),f=n(1359),p=n(32547),h=n(96963);let m=["top","right","bottom","left"],v=Math.min,g=Math.max,y=Math.round,w=Math.floor,x=e=>({x:e,y:e}),b={left:"right",right:"left",bottom:"top",top:"bottom"},S={start:"end",end:"start"};function E(e,t){return"function"==typeof e?e(t):e}function k(e){return e.split("-")[0]}function A(e){return e.split("-")[1]}function C(e){return"x"===e?"y":"x"}function R(e){return"y"===e?"height":"width"}let M=new Set(["top","bottom"]);function j(e){return M.has(k(e))?"y":"x"}function P(e){return e.replace(/start|end/g,e=>S[e])}let T=["left","right"],D=["right","left"],L=["top","bottom"],N=["bottom","top"];function I(e){return e.replace(/left|right|bottom|top/g,e=>b[e])}function O(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function H(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function F(e,t,n){let r,{reference:o,floating:i}=e,a=j(t),l=C(j(t)),s=R(l),u=k(t),c="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(u){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(A(t)){case"start":r[l]-=p*(n&&c?-1:1);break;case"end":r[l]+=p*(n&&c?-1:1)}return r}let _=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),s=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=F(u,r,s),f=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:w}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(u=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=F(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function B(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=E(t,e),h=O(p),m=l[f?"floating"===d?"reference":"floating":d],v=H(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:c,strategy:s})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=H(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:y,strategy:s}):g);return{top:(v.top-x.top+h.top)/w.y,bottom:(x.bottom-v.bottom+h.bottom)/w.y,left:(v.left-x.left+h.left)/w.x,right:(x.right-v.right+h.right)/w.x}}function W(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function z(e){return m.some(t=>e[t]>=0)}let q=new Set(["left","top"]);async function V(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=k(n),l=A(n),s="y"===j(n),u=q.has(a)?-1:1,c=i&&s?-1:1,d=E(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof h&&(p="end"===l?-1*h:h),s?{x:p*c,y:f*u}:{x:f*u,y:p*c}}function G(){return"undefined"!=typeof window}function K(e){return X(e)?(e.nodeName||"").toLowerCase():"#document"}function $(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function U(e){var t;return null==(t=(X(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function X(e){return!!G()&&(e instanceof Node||e instanceof $(e).Node)}function Y(e){return!!G()&&(e instanceof Element||e instanceof $(e).Element)}function Z(e){return!!G()&&(e instanceof HTMLElement||e instanceof $(e).HTMLElement)}function J(e){return!!G()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof $(e).ShadowRoot)}let Q=new Set(["inline","contents"]);function ee(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ed(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Q.has(o)}let et=new Set(["table","td","th"]),en=[":popover-open",":modal"];function er(e){return en.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eo=["transform","translate","scale","rotate","perspective"],ei=["transform","translate","scale","rotate","perspective","filter"],ea=["paint","layout","strict","content"];function el(e){let t=es(),n=Y(e)?ed(e):e;return eo.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||ei.some(e=>(n.willChange||"").includes(e))||ea.some(e=>(n.contain||"").includes(e))}function es(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let eu=new Set(["html","body","#document"]);function ec(e){return eu.has(K(e))}function ed(e){return $(e).getComputedStyle(e)}function ef(e){return Y(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ep(e){if("html"===K(e))return e;let t=e.assignedSlot||e.parentNode||J(e)&&e.host||U(e);return J(t)?t.host:t}function eh(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ep(t);return ec(n)?t.ownerDocument?t.ownerDocument.body:t.body:Z(n)&&ee(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=$(o);if(i){let e=em(a);return t.concat(a,a.visualViewport||[],ee(o)?o:[],e&&n?eh(e):[])}return t.concat(o,eh(o,[],n))}function em(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ev(e){let t=ed(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=Z(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=y(n)!==i||y(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function eg(e){return Y(e)?e:e.contextElement}function ey(e){let t=eg(e);if(!Z(t))return x(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ev(t),a=(i?y(n.width):n.width)/r,l=(i?y(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let ew=x(0);function ex(e){let t=$(e);return es()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ew}function eb(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=eg(e),l=x(1);t&&(r?Y(r)&&(l=ey(r)):l=ey(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===$(a))&&o)?ex(a):x(0),u=(i.left+s.x)/l.x,c=(i.top+s.y)/l.y,d=i.width/l.x,f=i.height/l.y;if(a){let e=$(a),t=r&&Y(r)?$(r):r,n=e,o=em(n);for(;o&&r&&t!==n;){let e=ey(o),t=o.getBoundingClientRect(),r=ed(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,f*=e.y,u+=i,c+=a,o=em(n=$(o))}}return H({width:d,height:f,x:u,y:c})}function eS(e,t){let n=ef(e).scrollLeft;return t?t.left+n:eb(U(e)).left+n}function eE(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eS(e,r)),y:r.top+t.scrollTop}}let ek=new Set(["absolute","fixed"]);function eA(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=$(e),r=U(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,s=0;if(o){i=o.width,a=o.height;let e=es();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,s=o.offsetTop)}return{width:i,height:a,x:l,y:s}}(e,n);else if("document"===t)r=function(e){let t=U(e),n=ef(e),r=e.ownerDocument.body,o=g(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=g(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+eS(e),l=-n.scrollTop;return"rtl"===ed(r).direction&&(a+=g(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:l}}(U(e));else if(Y(t))r=function(e,t){let n=eb(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Z(e)?ey(e):x(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=ex(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return H(r)}function eC(e){return"static"===ed(e).position}function eR(e,t){if(!Z(e)||"fixed"===ed(e).position)return null;if(t)return t(e);let n=e.offsetParent;return U(e)===n&&(n=n.ownerDocument.body),n}function eM(e,t){var n;let r=$(e);if(er(e))return r;if(!Z(e)){let t=ep(e);for(;t&&!ec(t);){if(Y(t)&&!eC(t))return t;t=ep(t)}return r}let o=eR(e,t);for(;o&&(n=o,et.has(K(n)))&&eC(o);)o=eR(o,t);return o&&ec(o)&&eC(o)&&!el(o)?r:o||function(e){let t=ep(e);for(;Z(t)&&!ec(t);){if(el(t))return t;if(er(t))break;t=ep(t)}return null}(e)||r}let ej=async function(e){let t=this.getOffsetParent||eM,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=Z(t),o=U(t),i="fixed"===n,a=eb(e,!0,i,t),l={scrollLeft:0,scrollTop:0},s=x(0);if(r||!r&&!i){if(("body"!==K(t)||ee(o))&&(l=ef(t)),r){let e=eb(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eS(o))}i&&!r&&o&&(s.x=eS(o));let u=!o||r||i?x(0):eE(o,l);return{x:a.left+l.scrollLeft-s.x-u.x,y:a.top+l.scrollTop-s.y-u.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eP={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=U(r),l=!!t&&er(t.floating);if(r===a||l&&i)return n;let s={scrollLeft:0,scrollTop:0},u=x(1),c=x(0),d=Z(r);if((d||!d&&!i)&&(("body"!==K(r)||ee(a))&&(s=ef(r)),Z(r))){let e=eb(r);u=ey(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let f=!a||d||i?x(0):eE(a,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+f.x,y:n.y*u.y-s.scrollTop*u.y+c.y+f.y}},getDocumentElement:U,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?er(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eh(e,[],!1).filter(e=>Y(e)&&"body"!==K(e)),o=null,i="fixed"===ed(e).position,a=i?ep(e):e;for(;Y(a)&&!ec(a);){let t=ed(a),n=el(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ek.has(o.position)||ee(a)&&!n&&function e(t,n){let r=ep(t);return!(r===n||!Y(r)||ec(r))&&("fixed"===ed(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=ep(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((e,n)=>{let r=eA(t,n,o);return e.top=g(r.top,e.top),e.right=v(r.right,e.right),e.bottom=v(r.bottom,e.bottom),e.left=g(r.left,e.left),e},eA(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eM,getElementRects:ej,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ev(e);return{width:t,height:n}},getScale:ey,isElement:Y,isRTL:function(e){return"rtl"===ed(e).direction}};function eT(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eD=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:l,middlewareData:s}=t,{element:u,padding:c=0}=E(e,t)||{};if(null==u)return{};let d=O(c),f={x:n,y:r},p=C(j(o)),h=R(p),m=await a.getDimensions(u),y="y"===p,w=y?"clientHeight":"clientWidth",x=i.reference[h]+i.reference[p]-f[p]-i.floating[h],b=f[p]-i.reference[p],S=await (null==a.getOffsetParent?void 0:a.getOffsetParent(u)),k=S?S[w]:0;k&&await (null==a.isElement?void 0:a.isElement(S))||(k=l.floating[w]||i.floating[h]);let M=k/2-m[h]/2-1,P=v(d[y?"top":"left"],M),T=v(d[y?"bottom":"right"],M),D=k-m[h]-T,L=k/2-m[h]/2+(x/2-b/2),N=g(P,v(L,D)),I=!s.arrow&&null!=A(o)&&L!==N&&i.reference[h]/2-(L<P?P:T)-m[h]/2<0,H=I?L<P?L-P:L-D:0;return{[p]:f[p]+H,data:{[p]:N,centerOffset:L-N-H,...I&&{alignmentOffset:H}},reset:I}}}),eL=(e,t,n)=>{let r=new Map,o={platform:eP,...n},i={...o.platform,_c:r};return _(e,t,{...o,platform:i})};var eN="undefined"!=typeof document?r.useLayoutEffect:function(){};function eI(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eI(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eI(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eO(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eH(e,t){let n=eO(e);return Math.round(t*n)/n}function eF(e){let t=r.useRef(e);return eN(()=>{t.current=e}),t}let e_=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eD({element:n.current,padding:r}).fn(t):{}:n?eD({element:n,padding:r}).fn(t):{}}}),eB=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,s=await V(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:a}}}}}(e),options:[e,t]}),eW=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=E(e,t),u={x:n,y:r},c=await B(t,s),d=j(k(o)),f=C(d),p=u[f],h=u[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+c[e],r=p-c[t];p=g(n,v(p,r))}if(a){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=h+c[e],r=h-c[t];h=g(n,v(h,r))}let m=l.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[f]:i,[d]:a}}}}}}(e),options:[e,t]}),ez=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:s=!0,crossAxis:u=!0}=E(e,t),c={x:n,y:r},d=j(o),f=C(d),p=c[f],h=c[d],m=E(l,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var g,y;let e="y"===f?"width":"height",t=q.has(k(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),eq=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=E(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let x=k(l),b=j(c),S=k(c)===c,M=await (null==d.isRTL?void 0:d.isRTL(f.floating)),O=m||(S||!y?[I(c)]:function(e){let t=I(e);return[P(e),t,P(t)]}(c)),H="none"!==g;!m&&H&&O.push(...function(e,t,n,r){let o=A(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?D:T;return t?T:D;case"left":case"right":return t?L:N;default:return[]}}(k(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(P)))),i}(c,y,g,M));let F=[c,...O],_=await B(t,w),W=[],z=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&W.push(_[x]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=A(e),o=C(j(e)),i=R(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=I(a)),[a,I(a)]}(l,u,M);W.push(_[e[0]],_[e[1]])}if(z=[...z,{placement:l,overflows:W}],!W.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=F[e];if(t&&("alignment"!==h||b===j(t)||z.every(e=>e.overflows[0]>0&&j(e.placement)===b)))return{data:{index:e,overflows:z},reset:{placement:t}};let n=null==(i=z.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=z.filter(e=>{if(H){let t=j(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eV=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i;let{placement:a,rects:l,platform:s,elements:u}=t,{apply:c=()=>{},...d}=E(e,t),f=await B(t,d),p=k(a),h=A(a),m="y"===j(a),{width:y,height:w}=l.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let x=w-f.top-f.bottom,b=y-f.left-f.right,S=v(w-f[o],x),C=v(y-f[i],b),R=!t.middlewareData.shift,M=S,P=C;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(P=b),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(M=x),R&&!h){let e=g(f.left,0),t=g(f.right,0),n=g(f.top,0),r=g(f.bottom,0);m?P=y-2*(0!==e||0!==t?e+t:g(f.left,f.right)):M=w-2*(0!==n||0!==r?n+r:g(f.top,f.bottom))}await c({...t,availableWidth:P,availableHeight:M});let T=await s.getDimensions(u.floating);return y!==T.width||w!==T.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eG=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=E(e,t);switch(r){case"referenceHidden":{let e=W(await B(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:z(e)}}}case"escaped":{let e=W(await B(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:z(e)}}}default:return{}}}}}(e),options:[e,t]}),eK=(e,t)=>({...e_(e),options:[e,t]});var e$=n(14163),eU=n(60687),eX=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eU.jsx)(e$.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eU.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eX.displayName="Arrow";var eY=n(13495),eZ=n(66156),eJ=n(18853),eQ="Popper",[e0,e1]=(0,u.A)(eQ),[e2,e6]=e0(eQ),e4=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eU.jsx)(e2,{scope:t,anchor:o,onAnchorChange:i,children:n})};e4.displayName=eQ;var e5="PopperAnchor",e3=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=e6(e5,n),l=r.useRef(null),u=(0,s.s)(t,l);return r.useEffect(()=>{a.onAnchorChange(o?.current||l.current)}),o?null:(0,eU.jsx)(e$.sG.div,{...i,ref:u})});e3.displayName=e5;var e8="PopperContent",[e9,e7]=e0(e8),te=r.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:a=0,align:l="center",alignOffset:u=0,arrowPadding:c=0,avoidCollisions:d=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:h="partial",hideWhenDetached:m=!1,updatePositionStrategy:y="optimized",onPlaced:x,...b}=e,S=e6(e8,n),[E,k]=r.useState(null),A=(0,s.s)(t,e=>k(e)),[C,R]=r.useState(null),M=(0,eJ.X)(C),j=M?.width??0,P=M?.height??0,T="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},D=Array.isArray(f)?f:[f],L=D.length>0,N={padding:T,boundary:D.filter(to),altBoundary:L},{refs:I,floatingStyles:O,placement:H,isPositioned:F,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:a,elements:{reference:l,floating:s}={},transform:u=!0,whileElementsMounted:c,open:d}=e,[f,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=r.useState(i);eI(h,i)||m(i);let[v,g]=r.useState(null),[y,w]=r.useState(null),x=r.useCallback(e=>{e!==k.current&&(k.current=e,g(e))},[]),b=r.useCallback(e=>{e!==A.current&&(A.current=e,w(e))},[]),S=l||v,E=s||y,k=r.useRef(null),A=r.useRef(null),C=r.useRef(f),R=null!=c,M=eF(c),j=eF(a),P=eF(d),T=r.useCallback(()=>{if(!k.current||!A.current)return;let e={placement:t,strategy:n,middleware:h};j.current&&(e.platform=j.current),eL(k.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};D.current&&!eI(C.current,t)&&(C.current=t,o.flushSync(()=>{p(t)}))})},[h,t,n,j,P]);eN(()=>{!1===d&&C.current.isPositioned&&(C.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let D=r.useRef(!1);eN(()=>(D.current=!0,()=>{D.current=!1}),[]),eN(()=>{if(S&&(k.current=S),E&&(A.current=E),S&&E){if(M.current)return M.current(S,E,T);T()}},[S,E,T,M,R]);let L=r.useMemo(()=>({reference:k,floating:A,setReference:x,setFloating:b}),[x,b]),N=r.useMemo(()=>({reference:S,floating:E}),[S,E]),I=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!N.floating)return e;let t=eH(N.floating,f.x),r=eH(N.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eO(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,N.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:T,refs:L,elements:N,floatingStyles:I}),[f,T,L,N,I])}({strategy:"fixed",placement:i+("center"!==l?"-"+l:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=eg(e),d=i||a?[...c?eh(c):[],...eh(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let f=c&&s?function(e,t){let n,r=null,o=U(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function a(l,s){void 0===l&&(l=!1),void 0===s&&(s=1),i();let u=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=u;if(l||t(),!f||!p)return;let h=w(d),m=w(o.clientWidth-(c+f)),y={rootMargin:-h+"px "+-m+"px "+-w(o.clientHeight-(d+p))+"px "+-w(c)+"px",threshold:g(0,v(1,s))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==s){if(!x)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||eT(u,e.getBoundingClientRect())||a(),x=!1}try{r=new IntersectionObserver(b,{...y,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,y)}r.observe(e)}(!0),i}(c,n):null,p=-1,h=null;l&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!u&&h.observe(c),h.observe(t));let m=u?eb(e):null;return u&&function t(){let r=eb(e);m&&!eT(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,u&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===y}),elements:{reference:S.anchor},middleware:[eB({mainAxis:a+P,alignmentAxis:u}),d&&eW({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?ez():void 0,...N}),d&&eq({...N}),eV({...N,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),C&&eK({element:C,padding:c}),ti({arrowWidth:j,arrowHeight:P}),m&&eG({strategy:"referenceHidden",...N})]}),[B,W]=ta(H),z=(0,eY.c)(x);(0,eZ.N)(()=>{F&&z?.()},[F,z]);let q=_.arrow?.x,V=_.arrow?.y,G=_.arrow?.centerOffset!==0,[K,$]=r.useState();return(0,eZ.N)(()=>{E&&$(window.getComputedStyle(E).zIndex)},[E]),(0,eU.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:F?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eU.jsx)(e9,{scope:n,placedSide:B,onArrowChange:R,arrowX:q,arrowY:V,shouldHideArrow:G,children:(0,eU.jsx)(e$.sG.div,{"data-side":B,"data-align":W,...b,ref:A,style:{...b.style,animation:F?void 0:"none"}})})})});te.displayName=e8;var tt="PopperArrow",tn={top:"bottom",right:"left",bottom:"top",left:"right"},tr=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e7(tt,n),i=tn[o.placedSide];return(0,eU.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eU.jsx)(eX,{...r,ref:t,style:{...r.style,display:"block"}})})});function to(e){return null!==e}tr.displayName=tt;var ti=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[s,u]=ta(n),c={start:"0%",center:"50%",end:"100%"}[u],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",h="";return"bottom"===s?(p=i?c:`${d}px`,h=`${-l}px`):"top"===s?(p=i?c:`${d}px`,h=`${r.floating.height+l}px`):"right"===s?(p=`${-l}px`,h=i?c:`${f}px`):"left"===s&&(p=`${r.floating.width+l}px`,h=i?c:`${f}px`),{data:{x:p,y:h}}}});function ta(e){let[t,n="center"]=e.split("-");return[t,n]}var tl=n(25028),ts=n(8730),tu=n(65551),tc=n(83721),td=n(69024),tf=n(63376),tp=n(42247),th=[" ","Enter","ArrowUp","ArrowDown"],tm=[" ","Enter"],tv="Select",[tg,ty,tw]=(0,l.N)(tv),[tx,tb]=(0,u.A)(tv,[tw,e1]),tS=e1(),[tE,tk]=tx(tv),[tA,tC]=tx(tv),tR=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:a,value:l,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:m,required:v,form:g}=e,y=tS(t),[w,x]=r.useState(null),[b,S]=r.useState(null),[E,k]=r.useState(!1),A=(0,c.jH)(d),[C,R]=(0,tu.i)({prop:o,defaultProp:i??!1,onChange:a,caller:tv}),[M,j]=(0,tu.i)({prop:l,defaultProp:s,onChange:u,caller:tv}),P=r.useRef(null),T=!w||g||!!w.closest("form"),[D,L]=r.useState(new Set),N=Array.from(D).map(e=>e.props.value).join(";");return(0,eU.jsx)(e4,{...y,children:(0,eU.jsxs)(tE,{required:v,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:S,valueNodeHasChildren:E,onValueNodeHasChildrenChange:k,contentId:(0,h.B)(),value:M,onValueChange:j,open:C,onOpenChange:R,dir:A,triggerPointerDownPosRef:P,disabled:m,children:[(0,eU.jsx)(tg.Provider,{scope:t,children:(0,eU.jsx)(tA,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{L(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{L(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),T?(0,eU.jsxs)(nr,{"aria-hidden":!0,required:v,tabIndex:-1,name:f,autoComplete:p,value:M,onChange:e=>j(e.target.value),disabled:m,form:g,children:[void 0===M?(0,eU.jsx)("option",{value:""}):null,Array.from(D)]},N):null]})})};tR.displayName=tv;var tM="SelectTrigger",tj=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,l=tS(n),u=tk(tM,n),c=u.disabled||o,d=(0,s.s)(t,u.onTriggerChange),f=ty(n),p=r.useRef("touch"),[h,m,v]=ni(e=>{let t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=na(t,e,n);void 0!==r&&u.onValueChange(r.value)}),g=e=>{c||(u.onOpenChange(!0),v()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,eU.jsx)(e3,{asChild:!0,...l,children:(0,eU.jsx)(e$.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":no(u.value)?"":void 0,...i,ref:d,onClick:(0,a.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:(0,a.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,a.m)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&th.includes(e.key)&&(g(),e.preventDefault())})})})});tj.displayName=tM;var tP="SelectValue",tT=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:a="",...l}=e,u=tk(tP,n),{onValueNodeHasChildrenChange:c}=u,d=void 0!==i,f=(0,s.s)(t,u.onValueNodeChange);return(0,eZ.N)(()=>{c(d)},[c,d]),(0,eU.jsx)(e$.sG.span,{...l,ref:f,style:{pointerEvents:"none"},children:no(u.value)?(0,eU.jsx)(eU.Fragment,{children:a}):i})});tT.displayName=tP;var tD=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,eU.jsx)(e$.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tD.displayName="SelectIcon";var tL=e=>(0,eU.jsx)(tl.Z,{asChild:!0,...e});tL.displayName="SelectPortal";var tN="SelectContent",tI=r.forwardRef((e,t)=>{let n=tk(tN,e.__scopeSelect),[i,a]=r.useState();return((0,eZ.N)(()=>{a(new DocumentFragment)},[]),n.open)?(0,eU.jsx)(t_,{...e,ref:t}):i?o.createPortal((0,eU.jsx)(tO,{scope:e.__scopeSelect,children:(0,eU.jsx)(tg.Slot,{scope:e.__scopeSelect,children:(0,eU.jsx)("div",{children:e.children})})}),i):null});tI.displayName=tN;var[tO,tH]=tx(tN),tF=(0,ts.TL)("SelectContent.RemoveScroll"),t_=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:l,onPointerDownOutside:u,side:c,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:S,...E}=e,k=tk(tN,n),[A,C]=r.useState(null),[R,M]=r.useState(null),j=(0,s.s)(t,e=>C(e)),[P,T]=r.useState(null),[D,L]=r.useState(null),N=ty(n),[I,O]=r.useState(!1),H=r.useRef(!1);r.useEffect(()=>{if(A)return(0,tf.Eq)(A)},[A]),(0,f.Oh)();let F=r.useCallback(e=>{let[t,...n]=N().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&R&&(R.scrollTop=0),n===r&&R&&(R.scrollTop=R.scrollHeight),n?.focus(),document.activeElement!==o))return},[N,R]),_=r.useCallback(()=>F([P,A]),[F,P,A]);r.useEffect(()=>{I&&_()},[I,_]);let{onOpenChange:B,triggerPointerDownPosRef:W}=k;r.useEffect(()=>{if(A){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(W.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(W.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():A.contains(n.target)||B(!1),document.removeEventListener("pointermove",t),W.current=null};return null!==W.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[A,B,W]),r.useEffect(()=>{let e=()=>B(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[B]);let[z,q]=ni(e=>{let t=N().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=na(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),V=r.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==k.value&&k.value===t||r)&&(T(e),r&&(H.current=!0))},[k.value]),G=r.useCallback(()=>A?.focus(),[A]),K=r.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==k.value&&k.value===t||r)&&L(e)},[k.value]),$="popper"===o?tW:tB,U=$===tW?{side:c,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:x,hideWhenDetached:b,avoidCollisions:S}:{};return(0,eU.jsx)(tO,{scope:n,content:A,viewport:R,onViewportChange:M,itemRefCallback:V,selectedItem:P,onItemLeave:G,itemTextRefCallback:K,focusSelectedItem:_,selectedItemText:D,position:o,isPositioned:I,searchRef:z,children:(0,eU.jsx)(tp.A,{as:tF,allowPinchZoom:!0,children:(0,eU.jsx)(p.n,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(i,e=>{k.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,eU.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,eU.jsx)($,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...E,...U,onPlaced:()=>O(!0),ref:j,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:(0,a.m)(E.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||q(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=N().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});t_.displayName="SelectContentImpl";var tB=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...a}=e,l=tk(tN,n),u=tH(tN,n),[c,d]=r.useState(null),[f,p]=r.useState(null),h=(0,s.s)(t,e=>p(e)),m=ty(n),v=r.useRef(!1),g=r.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:x,focusSelectedItem:b}=u,S=r.useCallback(()=>{if(l.trigger&&l.valueNode&&c&&f&&y&&w&&x){let e=l.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),r=x.getBoundingClientRect();if("rtl"!==l.dir){let o=r.left-t.left,a=n.left-o,l=e.left-a,s=e.width+l,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,i.q)(a,[10,Math.max(10,d-u)]);c.style.minWidth=s+"px",c.style.left=f+"px"}else{let o=t.right-r.right,a=window.innerWidth-n.right-o,l=window.innerWidth-e.right-a,s=e.width+l,u=Math.max(s,t.width),d=window.innerWidth-10,f=(0,i.q)(a,[10,Math.max(10,d-u)]);c.style.minWidth=s+"px",c.style.right=f+"px"}let a=m(),s=window.innerHeight-20,u=y.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),b=p+h+u+parseInt(d.paddingBottom,10)+g,S=Math.min(5*w.offsetHeight,b),E=window.getComputedStyle(y),k=parseInt(E.paddingTop,10),A=parseInt(E.paddingBottom,10),C=e.top+e.height/2-10,R=w.offsetHeight/2,M=p+h+(w.offsetTop+R);if(M<=C){let e=a.length>0&&w===a[a.length-1].ref.current;c.style.bottom="0px";let t=Math.max(s-C,R+(e?A:0)+(f.clientHeight-y.offsetTop-y.offsetHeight)+g);c.style.height=M+t+"px"}else{let e=a.length>0&&w===a[0].ref.current;c.style.top="0px";let t=Math.max(C,p+y.offsetTop+(e?k:0)+R);c.style.height=t+(b-M)+"px",y.scrollTop=M-C+y.offsetTop}c.style.margin="10px 0",c.style.minHeight=S+"px",c.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>v.current=!0)}},[m,l.trigger,l.valueNode,c,f,y,w,x,l.dir,o]);(0,eZ.N)(()=>S(),[S]);let[E,k]=r.useState();(0,eZ.N)(()=>{f&&k(window.getComputedStyle(f).zIndex)},[f]);let A=r.useCallback(e=>{e&&!0===g.current&&(S(),b?.(),g.current=!1)},[S,b]);return(0,eU.jsx)(tz,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:A,children:(0,eU.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,eU.jsx)(e$.sG.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});tB.displayName="SelectItemAlignedPosition";var tW=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,a=tS(n);return(0,eU.jsx)(te,{...a,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tW.displayName="SelectPopperPosition";var[tz,tq]=tx(tN,{}),tV="SelectViewport",tG=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,l=tH(tV,n),u=tq(tV,n),c=(0,s.s)(t,l.onViewportChange),d=r.useRef(0);return(0,eU.jsxs)(eU.Fragment,{children:[(0,eU.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,eU.jsx)(tg.Slot,{scope:n,children:(0,eU.jsx)(e$.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,a.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if(r?.current&&n){let e=Math.abs(d.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,a=Math.min(r,i),l=i-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=l>0?l:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});tG.displayName=tV;var tK="SelectGroup",[t$,tU]=tx(tK),tX=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,eU.jsx)(t$,{scope:n,id:o,children:(0,eU.jsx)(e$.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});tX.displayName=tK;var tY="SelectLabel",tZ=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tU(tY,n);return(0,eU.jsx)(e$.sG.div,{id:o.id,...r,ref:t})});tZ.displayName=tY;var tJ="SelectItem",[tQ,t0]=tx(tJ),t1=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:l,...u}=e,c=tk(tJ,n),d=tH(tJ,n),f=c.value===o,[p,m]=r.useState(l??""),[v,g]=r.useState(!1),y=(0,s.s)(t,e=>d.itemRefCallback?.(e,o,i)),w=(0,h.B)(),x=r.useRef("touch"),b=()=>{i||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,eU.jsx)(tQ,{scope:n,value:o,disabled:i,textId:w,isSelected:f,onItemTextChange:r.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,eU.jsx)(tg.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,eU.jsx)(e$.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...u,ref:y,onFocus:(0,a.m)(u.onFocus,()=>g(!0)),onBlur:(0,a.m)(u.onBlur,()=>g(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==x.current&&b()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===x.current&&b()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{x.current=e.pointerType,i?d.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{(d.searchRef?.current===""||" "!==e.key)&&(tm.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});t1.displayName=tJ;var t2="SelectItemText",t6=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:i,style:a,...l}=e,u=tk(t2,n),c=tH(t2,n),d=t0(t2,n),f=tC(t2,n),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e),d.onItemTextChange,e=>c.itemTextRefCallback?.(e,d.value,d.disabled)),v=p?.textContent,g=r.useMemo(()=>(0,eU.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=f;return(0,eZ.N)(()=>(y(g),()=>w(g)),[y,w,g]),(0,eU.jsxs)(eU.Fragment,{children:[(0,eU.jsx)(e$.sG.span,{id:d.textId,...l,ref:m}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(l.children,u.valueNode):null]})});t6.displayName=t2;var t4="SelectItemIndicator",t5=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return t0(t4,n).isSelected?(0,eU.jsx)(e$.sG.span,{"aria-hidden":!0,...r,ref:t}):null});t5.displayName=t4;var t3="SelectScrollUpButton",t8=r.forwardRef((e,t)=>{let n=tH(t3,e.__scopeSelect),o=tq(t3,e.__scopeSelect),[i,a]=r.useState(!1),l=(0,s.s)(t,o.onScrollButtonChange);return(0,eZ.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){a(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eU.jsx)(ne,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});t8.displayName=t3;var t9="SelectScrollDownButton",t7=r.forwardRef((e,t)=>{let n=tH(t9,e.__scopeSelect),o=tq(t9,e.__scopeSelect),[i,a]=r.useState(!1),l=(0,s.s)(t,o.onScrollButtonChange);return(0,eZ.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,eU.jsx)(ne,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});t7.displayName=t9;var ne=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,l=tH("SelectScrollButton",n),s=r.useRef(null),u=ty(n),c=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,eZ.N)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,eU.jsx)(e$.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,a.m)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,a.m)(i.onPointerMove,()=>{l.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,a.m)(i.onPointerLeave,()=>{c()})})}),nt=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,eU.jsx)(e$.sG.div,{"aria-hidden":!0,...r,ref:t})});nt.displayName="SelectSeparator";var nn="SelectArrow";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tS(n),i=tk(nn,n),a=tH(nn,n);return i.open&&"popper"===a.position?(0,eU.jsx)(tr,{...o,...r,ref:t}):null}).displayName=nn;var nr=r.forwardRef(({__scopeSelect:e,value:t,...n},o)=>{let i=r.useRef(null),a=(0,s.s)(o,i),l=(0,tc.Z)(t);return r.useEffect(()=>{let e=i.current;if(!e)return;let n=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(l!==t&&n){let r=new Event("change",{bubbles:!0});n.call(e,t),e.dispatchEvent(r)}},[l,t]),(0,eU.jsx)(e$.sG.select,{...n,style:{...td.Qg,...n.style},ref:a,defaultValue:t})});function no(e){return""===e||void 0===e}function ni(e){let t=(0,eY.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),a=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,a]}function na(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,l=(r=e,o=Math.max(a,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(l=l.filter(e=>e!==n));let s=l.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==n?s:void 0}nr.displayName="SelectBubbleInput";var nl=tR,ns=tj,nu=tT,nc=tD,nd=tL,nf=tI,np=tG,nh=tX,nm=tZ,nv=t1,ng=t6,ny=t5,nw=t8,nx=t7,nb=nt},29272:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("ListOrdered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},32547:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(43210),o=n(98599),i=n(14163),a=n(13495),l=n(60687),s="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,x]=r.useState(null),b=(0,a.c)(v),S=(0,a.c)(g),E=r.useRef(null),k=(0,o.s)(t,e=>x(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(A.paused||!w)return;let t=e.target;w.contains(t)?E.current=t:h(E.current,{select:!0})},t=function(e){if(A.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||h(E.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,A.paused]),r.useEffect(()=>{if(w){m.add(A);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(s,c);w.addEventListener(s,b),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(s,b),setTimeout(()=>{let t=new CustomEvent(u,c);w.addEventListener(u,S),w.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),w.removeEventListener(u,S),m.remove(A)},0)}}},[w,b,S,A]);let C=r.useCallback(e=>{if(!n&&!d||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,A.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:k,onKeyDown:C})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},33886:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},40196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("SquarePlus",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},40211:(e,t,n)=>{n.d(t,{C1:()=>E,bL:()=>b});var r=n(43210),o=n(98599),i=n(11273),a=n(70569),l=n(65551),s=n(83721),u=n(18853),c=n(46059),d=n(14163),f=n(60687),p="Checkbox",[h,m]=(0,i.A)(p),[v,g]=h(p);function y(e){let{__scopeCheckbox:t,checked:n,children:o,defaultChecked:i,disabled:a,form:s,name:u,onCheckedChange:c,required:d,value:h="on",internal_do_not_use_render:m}=e,[g,y]=(0,l.i)({prop:n,defaultProp:i??!1,onChange:c,caller:p}),[w,x]=r.useState(null),[b,S]=r.useState(null),E=r.useRef(!1),k=!w||!!s||!!w.closest("form"),A={checked:g,disabled:a,setChecked:y,control:w,setControl:x,name:u,form:s,value:h,hasConsumerStoppedPropagationRef:E,required:d,defaultChecked:!C(i)&&i,isFormControl:k,bubbleInput:b,setBubbleInput:S};return(0,f.jsx)(v,{scope:t,...A,children:"function"==typeof m?m(A):o})}var w="CheckboxTrigger",x=r.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...i},l)=>{let{control:s,value:u,disabled:c,checked:p,required:h,setControl:m,setChecked:v,hasConsumerStoppedPropagationRef:y,isFormControl:x,bubbleInput:b}=g(w,e),S=(0,o.s)(l,m),E=r.useRef(p);return r.useEffect(()=>{let e=s?.form;if(e){let t=()=>v(E.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[s,v]),(0,f.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":C(p)?"mixed":p,"aria-required":h,"data-state":R(p),"data-disabled":c?"":void 0,disabled:c,value:u,...i,ref:S,onKeyDown:(0,a.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,a.m)(n,e=>{v(e=>!!C(e)||!e),b&&x&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});x.displayName=w;var b=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:o,defaultChecked:i,required:a,disabled:l,value:s,onCheckedChange:u,form:c,...d}=e;return(0,f.jsx)(y,{__scopeCheckbox:n,checked:o,defaultChecked:i,disabled:l,required:a,onCheckedChange:u,name:r,form:c,value:s,internal_do_not_use_render:({isFormControl:e})=>(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(x,{...d,ref:t,__scopeCheckbox:n}),e&&(0,f.jsx)(A,{__scopeCheckbox:n})]})})});b.displayName=p;var S="CheckboxIndicator",E=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,i=g(S,n);return(0,f.jsx)(c.C,{present:r||C(i.checked)||!0===i.checked,children:(0,f.jsx)(d.sG.span,{"data-state":R(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});E.displayName=S;var k="CheckboxBubbleInput",A=r.forwardRef(({__scopeCheckbox:e,...t},n)=>{let{control:i,hasConsumerStoppedPropagationRef:a,checked:l,defaultChecked:c,required:p,disabled:h,name:m,value:v,form:y,bubbleInput:w,setBubbleInput:x}=g(k,e),b=(0,o.s)(n,x),S=(0,s.Z)(l),E=(0,u.X)(i);r.useEffect(()=>{if(!w)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!a.current;if(S!==l&&e){let n=new Event("click",{bubbles:t});w.indeterminate=C(l),e.call(w,!C(l)&&l),w.dispatchEvent(n)}},[w,S,l,a]);let A=r.useRef(!C(l)&&l);return(0,f.jsx)(d.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??A.current,required:p,disabled:h,name:m,value:v,form:y,...t,tabIndex:-1,ref:b,style:{...t.style,...E,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function R(e){return C(e)?"indeterminate":e?"checked":"unchecked"}A.displayName=k},41936:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},42247:(e,t,n)=>{n.d(t,{A:()=>G});var r,o=function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var a=("function"==typeof SuppressedError&&SuppressedError,n(43210)),l="right-scroll-bar-position",s="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?a.useLayoutEffect:a.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,i,a=(t=null,void 0===n&&(n=f),r=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,i);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){i=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var o=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(o)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=o({async:!0,ssr:!1},e),a}(),h=function(){},m=a.forwardRef(function(e,t){var n,r,l,s,f=a.useRef(null),m=a.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=m[0],g=m[1],y=e.forwardProps,w=e.children,x=e.className,b=e.removeScrollBar,S=e.enabled,E=e.shards,k=e.sideCar,A=e.noRelative,C=e.noIsolation,R=e.inert,M=e.allowPinchZoom,j=e.as,P=e.gapMode,T=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),D=(n=[f,t],r=function(e){return n.forEach(function(t){return u(t,e)})},(l=(0,a.useState)(function(){return{value:null,callback:r,facade:{get current(){return l.value},set current(value){var e=l.value;e!==value&&(l.value=value,l.callback(value,e))}}}})[0]).callback=r,s=l.facade,c(function(){var e=d.get(s);if(e){var t=new Set(e),r=new Set(n),o=s.current;t.forEach(function(e){r.has(e)||u(e,null)}),r.forEach(function(e){t.has(e)||u(e,o)})}d.set(s,n)},[n]),s),L=o(o({},T),v);return a.createElement(a.Fragment,null,S&&a.createElement(k,{sideCar:p,removeScrollBar:b,shards:E,noRelative:A,noIsolation:C,inert:R,setCallbacks:g,allowPinchZoom:!!M,lockRef:f,gapMode:P}),y?a.cloneElement(a.Children.only(w),o(o({},L),{ref:D})):a.createElement(void 0===j?"div":j,o({},L,{className:x,ref:D}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:s,zeroRight:l};var v=function(e){var t=e.sideCar,n=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return a.createElement(r,o({},n))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,n){a.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},b=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[b(n),b(r),b(o)]},E=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},k=w(),A="data-scroll-locked",C=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},R=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},M=function(){a.useEffect(function(){return document.body.setAttribute(A,(R()+1).toString()),function(){var e=R()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},j=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;M();var i=a.useMemo(function(){return E(o)},[o]);return a.createElement(k,{styles:C(i,!t,o,n?"":"!important")})},P=!1;if("undefined"!=typeof window)try{var T=Object.defineProperty({},"passive",{get:function(){return P=!0,!0}});window.addEventListener("test",T,T),window.removeEventListener("test",T,T)}catch(e){P=!1}var D=!!P&&{passive:!1},L=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},N=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=O(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?L(t,"overflowY"):L(t,"overflowX")},O=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},H=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,s=n.target,u=t.contains(s),c=!1,d=l>0,f=0,p=0;do{if(!s)break;var h=O(e,s),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&I(e,s)&&(f+=v,p+=m);var g=s.parentNode;s=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&s!==document.body||u&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&l>f)?c=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(c=!0),c},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},_=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},W=0,z=[];let q=(p.useMedium(function(e){var t=a.useRef([]),n=a.useRef([0,0]),r=a.useRef(),o=a.useState(W++)[0],i=a.useState(w)[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=F(e),a=n.current,s="deltaX"in e?e.deltaX:a[0]-i[0],u="deltaY"in e?e.deltaY:a[1]-i[1],c=e.target,d=Math.abs(s)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=N(d,c);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=N(d,c)),!f)return!1;if(!r.current&&"changedTouches"in e&&(s||u)&&(r.current=o),!o)return!0;var p=r.current||o;return H(p,t,e,"h"===p?s:u,!0)},[]),u=a.useCallback(function(e){if(z.length&&z[z.length-1]===i){var n="deltaY"in e?_(e):F(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=a.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=a.useCallback(function(e){n.current=F(e),r.current=void 0},[]),f=a.useCallback(function(t){c(t.type,_(t),t.target,s(t,e.lockRef.current))},[]),p=a.useCallback(function(t){c(t.type,F(t),t.target,s(t,e.lockRef.current))},[]);a.useEffect(function(){return z.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",u,D),document.addEventListener("touchmove",u,D),document.addEventListener("touchstart",d,D),function(){z=z.filter(function(e){return e!==i}),document.removeEventListener("wheel",u,D),document.removeEventListener("touchmove",u,D),document.removeEventListener("touchstart",d,D)}},[]);var h=e.removeScrollBar,m=e.inert;return a.createElement(a.Fragment,null,m?a.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?a.createElement(j,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),v);var V=a.forwardRef(function(e,t){return a.createElement(m,o({},e,{ref:t,sideCar:q}))});V.classNames=m.classNames;let G=V},44610:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},49497:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},58450:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},59892:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("Bitcoin",[["path",{d:"M11.767 19.089c4.924.868 6.14-6.025 1.216-6.894m-1.216 6.894L5.86 18.047m5.908 1.042-.347 1.97m1.563-8.864c4.924.869 6.14-6.025 1.215-6.893m-1.215 6.893-3.94-.694m5.155-6.2L8.29 4.26m5.908 1.042.348-1.97M7.48 20.364l3.126-17.727",key:"yr8idg"}]])},61662:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},62369:(e,t,n)=>{n.d(t,{b:()=>u});var r=n(43210),o=n(14163),i=n(60687),a="horizontal",l=["horizontal","vertical"],s=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:s=a,...u}=e,c=(n=s,l.includes(n))?s:a;return(0,i.jsx)(o.sG.div,{"data-orientation":c,...r?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...u,ref:t})});s.displayName="Separator";var u=s},63376:(e,t,n)=>{n.d(t,{Eq:()=>c});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,s=function(e){return e&&(e.host||s(e.parentNode))},u=function(e,t,n,r){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=s(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var c=a[n],d=[],f=new Set,p=new Set(u),h=function(e){!(!e||f.has(e))&&(f.add(e),h(e.parentNode))};u.forEach(h);var m=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,s=(c.get(e)||0)+1;o.set(e,l),c.set(e,s),d.push(e),1===l&&a&&i.set(e,!0),1===s&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=c.get(e)-1;o.set(e,t),c.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),u(o,i,n,"aria-hidden")):function(){return null}}},72963:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},76485:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},81950:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},83721:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(43210);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},85866:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},89743:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},91840:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},92375:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])},99196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(82614).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};