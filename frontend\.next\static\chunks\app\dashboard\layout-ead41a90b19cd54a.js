(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{7926:(e,a,t)=>{Promise.resolve().then(t.bind(t,98701))},17313:(e,a,t)=>{"use strict";t.d(a,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>i});var r=t(95155),s=t(12115),l=t(30064),n=t(59434);let i=l.bL,o=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.B8,{ref:a,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...s})});o.displayName=l.B8.displayName;let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.l9,{ref:a,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...s})});c.displayName=l.l9.displayName;let d=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.UC,{ref:a,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...s})});d.displayName=l.UC.displayName},22346:(e,a,t)=>{"use strict";t.d(a,{w:()=>i});var r=t(95155),s=t(12115),l=t(87489),n=t(59434);let i=s.forwardRef((e,a)=>{let{className:t,orientation:s="horizontal",decorative:i=!0,...o}=e;return(0,r.jsx)(l.b,{ref:a,decorative:i,orientation:s,className:(0,n.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",t),...o})});i.displayName=l.b.displayName},26126:(e,a,t)=>{"use strict";t.d(a,{E:()=>i});var r=t(95155);t(12115);var s=t(74466),l=t(59434);let n=(0,s.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:a,variant:t,...s}=e;return(0,r.jsx)("div",{className:(0,l.cn)(n({variant:t}),a),...s})}},30285:(e,a,t)=>{"use strict";t.d(a,{$:()=>c});var r=t(95155),s=t(12115),l=t(99708),n=t(74466),i=t(59434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,a)=>{let{className:t,variant:s,size:n,asChild:c=!1,...d}=e,m=c?l.DX:"button";return(0,r.jsx)(m,{className:(0,i.cn)(o({variant:s,size:n,className:t})),ref:a,...d})});c.displayName="Button"},40283:(e,a,t)=>{"use strict";t.d(a,{A:()=>d,AuthProvider:()=>c});var r=t(95155),s=t(12115),l=t(35695),n=t(50172),i=t(25731);let o=(0,s.createContext)(void 0),c=e=>{let{children:a}=e,[t,c]=(0,s.useState)(!1),[d,m]=(0,s.useState)(!0),p=(0,l.useRouter)(),u=(0,l.usePathname)();(0,s.useEffect)(()=>{let e=localStorage.getItem("plutoAuth"),a=localStorage.getItem("plutoAuthToken");"true"===e&&a&&c(!0),m(!1)},[]),(0,s.useEffect)(()=>{d||t||"/login"===u?!d&&t&&"/login"===u&&p.push("/dashboard"):p.push("/login")},[t,d,u,p]);let x=async(e,a)=>{m(!0);try{if(await i.ZQ.login(e,a))return c(!0),p.push("/dashboard"),!0;return c(!1),!1}catch(e){return console.error("Login failed:",e),c(!1),!1}finally{m(!1)}},h=async()=>{try{await i.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{c(!1),p.push("/login")}};return!d||(null==u?void 0:u.startsWith("/_next/static/"))?t||"/login"===u||(null==u?void 0:u.startsWith("/_next/static/"))?(0,r.jsx)(o.Provider,{value:{isAuthenticated:t,login:x,logout:h,isLoading:d},children:a}):(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,r.jsx)(n.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,r.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting to login..."})]}):(0,r.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,r.jsx)(n.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,r.jsx)("p",{className:"ml-4 text-xl",children:"Loading Pluto..."})]})},d=()=>{let e=(0,s.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},59409:(e,a,t)=>{"use strict";t.d(a,{bq:()=>p,eb:()=>f,gC:()=>h,l6:()=>d,yv:()=>m});var r=t(95155),s=t(12115),l=t(50663),n=t(79556),i=t(77381),o=t(10518),c=t(59434);let d=l.bL;l.YJ;let m=l.WT,p=s.forwardRef((e,a)=>{let{className:t,children:s,...i}=e;return(0,r.jsxs)(l.l9,{ref:a,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...i,children:[s,(0,r.jsx)(l.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});p.displayName=l.l9.displayName;let u=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.PP,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})});u.displayName=l.PP.displayName;let x=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.wn,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})});x.displayName=l.wn.displayName;let h=s.forwardRef((e,a)=>{let{className:t,children:s,position:n="popper",...i}=e;return(0,r.jsx)(l.ZL,{children:(0,r.jsxs)(l.UC,{ref:a,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,r.jsx)(u,{}),(0,r.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,r.jsx)(x,{})]})})});h.displayName=l.UC.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.JU,{ref:a,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...s})}).displayName=l.JU.displayName;let f=s.forwardRef((e,a)=>{let{className:t,children:s,...n}=e;return(0,r.jsxs)(l.q7,{ref:a,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(l.VF,{children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})}),(0,r.jsx)(l.p4,{children:s})]})});f.displayName=l.q7.displayName,s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.wv,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",t),...s})}).displayName=l.wv.displayName},59435:(e,a,t)=>{"use strict";t.d(a,{A:()=>n});var r=t(95155),s=t(12115),l=t(49502);let n=e=>{let{className:a,useFullName:t=!0}=e,[n,i]=(0,s.useState)(!1);return(0,r.jsxs)("div",{className:"flex items-center text-2xl font-bold text-primary ".concat(a),children:[n?(0,r.jsx)(l.A,{className:"mr-2 h-7 w-7"}):(0,r.jsx)("img",{src:"https://i.imgur.com/Q0HDcMH.png",alt:"Pluto Trading Bot Logo",className:"mr-2 h-7 w-7 rounded-full object-cover",onError:()=>{i(!0)},onLoad:()=>console.log("Pluto logo loaded successfully")}),(0,r.jsxs)("span",{children:["Pluto",t?" Trading Bot":""]})]})}},62523:(e,a,t)=>{"use strict";t.d(a,{p:()=>n});var r=t(95155),s=t(12115),l=t(59434);let n=s.forwardRef((e,a)=>{let{className:t,type:s,...n}=e;return(0,r.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...n})});n.displayName="Input"},66424:(e,a,t)=>{"use strict";t.d(a,{$:()=>o,F:()=>i});var r=t(95155),s=t(12115),l=t(47655),n=t(59434);let i=s.forwardRef((e,a)=>{let{className:t,children:s,...i}=e;return(0,r.jsxs)(l.bL,{ref:a,className:(0,n.cn)("relative overflow-hidden",t),...i,children:[(0,r.jsx)(l.LM,{className:"h-full w-full rounded-[inherit]",children:s}),(0,r.jsx)(o,{}),(0,r.jsx)(l.OK,{})]})});i.displayName=l.bL.displayName;let o=s.forwardRef((e,a)=>{let{className:t,orientation:s="vertical",...i}=e;return(0,r.jsx)(l.VM,{ref:a,orientation:s,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===s&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===s&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...i,children:(0,r.jsx)(l.lr,{className:"relative flex-1 rounded-full bg-border"})})});o.displayName=l.VM.displayName},66695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i});var r=t(95155),s=t(12115),l=t(59434);let n=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,l.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",t),...s})});n.displayName="Card";let i=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,l.cn)("flex flex-col space-y-1.5 p-4 md:p-6",t),...s})});i.displayName="CardHeader";let o=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("h3",{ref:a,className:(0,l.cn)("text-xl font-semibold leading-none tracking-tight",t),...s})});o.displayName="CardTitle";let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("p",{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",t),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,l.cn)("p-4 md:p-6 pt-0",t),...s})});d.displayName="CardContent",s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,l.cn)("flex items-center p-4 md:p-6 pt-0",t),...s})}).displayName="CardFooter"},85057:(e,a,t)=>{"use strict";t.d(a,{J:()=>c});var r=t(95155),s=t(12115),l=t(40968),n=t(74466),i=t(59434);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(l.b,{ref:a,className:(0,i.cn)(o(),t),...s})});c.displayName=l.b.displayName},98701:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>eT});var r=t(95155),s=t(12115),l=t(6874),n=t.n(l),i=t(35695),o=t(59435),c=t(30285),d=t(40283),m=t(18186),p=t(57082),u=t(29532),x=t(33349),h=t(26126),f=t(66695),g=t(48639),b=t(32087),y=t(37648);function j(e){let{className:a=""}=e,[t,l]=(0,s.useState)(navigator.onLine),[n,i]=(0,s.useState)(new Date);return(0,s.useEffect)(()=>{let e=()=>l(!0),a=()=>l(!1);window.addEventListener("online",e),window.addEventListener("offline",a);let t=setInterval(()=>{i(new Date)},1e3);return()=>{window.removeEventListener("online",e),window.removeEventListener("offline",a),clearInterval(t)}},[]),(0,r.jsxs)("div",{className:"flex items-center gap-2 ".concat(a),children:[(0,r.jsxs)(h.E,{variant:t?"default":"destructive",className:"flex items-center gap-1 ".concat(t?"bg-green-600 hover:bg-green-600/90 text-white":""),children:[t?(0,r.jsx)(g.A,{className:"h-3 w-3"}):(0,r.jsx)(b.A,{className:"h-3 w-3"}),t?"Online":"Offline"]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,r.jsx)(y.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:n.toLocaleTimeString()})]})]})}function v(){let{logout:e}=(0,d.A)();(0,i.useRouter)();let a=(0,i.usePathname)(),t=[{href:"/dashboard",label:"Home",icon:(0,r.jsx)(m.A,{})},{href:"/admin",label:"Admin Panel",icon:(0,r.jsx)(p.A,{})}];return(0,r.jsxs)("header",{className:"sticky top-0 z-50 flex items-center justify-between h-16 px-4 md:px-6 bg-card border-b-2 border-border",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(o.A,{useFullName:!1}),(0,r.jsx)(j,{})]}),(0,r.jsxs)("nav",{className:"flex items-center gap-2 sm:gap-3",children:[t.map(e=>(0,r.jsx)(c.$,{variant:a===e.href?"default":"ghost",size:"sm",asChild:!0,className:"".concat(a===e.href?"btn-neo":"hover:bg-accent/50"),children:(0,r.jsxs)(n(),{href:e.href,className:"flex items-center gap-2",children:[e.icon,(0,r.jsx)("span",{className:"hidden sm:inline",children:e.label})]})},e.label)),(0,r.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:()=>{window.open("/dashboard?newSession=true","_blank")},className:"hover:bg-accent/50 flex items-center gap-2",title:"Open a new independent trading session in a new tab",children:[(0,r.jsx)(u.A,{}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"New Session"})]}),(0,r.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:()=>{e()},className:"hover:bg-destructive/80 hover:text-destructive-foreground flex items-center gap-2",children:[(0,r.jsx)(x.A,{}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Logout"})]})]})]})}var N=t(77213),S=t(62523),T=t(85057),w=t(76981),A=t(10518),D=t(59434);let C=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(w.bL,{ref:a,className:(0,D.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...s,children:(0,r.jsx)(w.C1,{className:(0,D.cn)("flex items-center justify-center text-current"),children:(0,r.jsx)(A.A,{className:"h-4 w-4"})})})});C.displayName=w.bL.displayName;var E=t(59409),U=t(66424),k=t(22346),M=t(15452),O=t(25318);let B=M.bL;M.l9;let R=M.ZL,P=M.bm,I=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(M.hJ,{ref:a,className:(0,D.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...s})});I.displayName=M.hJ.displayName;let _=s.forwardRef((e,a)=>{let{className:t,children:s,...l}=e;return(0,r.jsxs)(R,{children:[(0,r.jsx)(I,{}),(0,r.jsxs)(M.UC,{ref:a,className:(0,D.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...l,children:[s,(0,r.jsxs)(M.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(O.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});_.displayName=M.UC.displayName;let L=e=>{let{className:a,...t}=e;return(0,r.jsx)("div",{className:(0,D.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...t})};L.displayName="DialogHeader";let F=e=>{let{className:a,...t}=e;return(0,r.jsx)("div",{className:(0,D.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};F.displayName="DialogFooter";let V=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(M.hE,{ref:a,className:(0,D.cn)("text-lg font-semibold leading-none tracking-tight",t),...s})});V.displayName=M.hE.displayName;let G=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)(M.VY,{ref:a,className:(0,D.cn)("text-sm text-muted-foreground",t),...s})});G.displayName=M.VY.displayName;let z=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("textarea",{className:(0,D.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:a,...s})});z.displayName="Textarea";var H=t(17313),J=t(87481);function Z(e){var a;let t,{isOpen:l,onClose:n,onSetTargetPrices:i}=e,[o,d]=(0,s.useState)("manual"),[m,p]=(0,s.useState)(""),{toast:u}=(0,J.dj)();try{t=(0,N.U)()}catch(e){console.warn("Trading context not available:",e),t=null}let[x,h]=(0,s.useState)("8"),[f,g]=(0,s.useState)("5"),[b,y]=(0,s.useState)("even"),j=(null==t?void 0:t.currentMarketPrice)||1e5,v=(null==t?void 0:null===(a=t.config)||void 0===a?void 0:a.slippagePercent)||.2,S=()=>{let e=parseInt(x),a=parseFloat(f);if(!e||e<2||e>20||!a||a<=0)return[];let t=[],r=j*(1-a/100),s=j*(1+a/100);if("even"===b)for(let a=0;a<e;a++){let l=r+a/(e-1)*(s-r);t.push(Math.round(l))}else if("fibonacci"===b){let a=[0,.236,.382,.5,.618,.764,.854,.927,1];for(let l=0;l<e;l++){let n=r+(s-r)*(a[Math.min(l,a.length-1)]||l/(e-1));t.push(Math.round(n))}}else if("exponential"===b)for(let a=0;a<e;a++){let l=r+(s-r)*Math.pow(a/(e-1),1.5);t.push(Math.round(l))}let l=3*v/100*j,n=t.sort((e,a)=>e-a),i=[];for(let e=0;e<n.length;e++){let a=n[e];if(i.length>0){let e=i[i.length-1];a-e<l&&(a=e+l)}i.push(Math.round(a))}return i},w=()=>{let e=m.split("\n").filter(e=>""!==e.trim()).map(e=>parseFloat(e.trim())).filter(e=>!isNaN(e)&&e>0).sort((e,a)=>e-a);if(e.length<2)return{hasOverlap:!1,message:""};let a=v/100*j;for(let t=0;t<e.length-1;t++)if(e[t]+a>=e[t+1]-a){let r=2*a,s=e[t+1]-e[t];return{hasOverlap:!0,message:"Overlap detected between ".concat(e[t]," and ").concat(e[t+1],". Minimum gap needed: ").concat(r.toFixed(0),", actual gap: ").concat(s.toFixed(0))}}return{hasOverlap:!1,message:"No slippage zone overlaps detected ✓"}},A=w();return(0,r.jsx)(B,{open:l,onOpenChange:n,children:(0,r.jsxs)(_,{className:"sm:max-w-2xl bg-card border-2 border-border",children:[(0,r.jsxs)(L,{children:[(0,r.jsx)(V,{className:"text-primary",children:"Set Target Prices"}),(0,r.jsx)(G,{children:"Set target prices manually or generate them automatically with optimal spacing to avoid slippage zone overlaps."})]}),(0,r.jsxs)(H.tU,{value:o,onValueChange:d,className:"w-full",children:[(0,r.jsxs)(H.j7,{className:"grid w-full grid-cols-2",children:[(0,r.jsx)(H.Xi,{value:"manual",children:"Manual Entry"}),(0,r.jsx)(H.Xi,{value:"automatic",children:"Automatic Generation"})]}),(0,r.jsx)(H.av,{value:"manual",className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(T.J,{htmlFor:"target-prices-input",className:"text-left",children:"Target Prices"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Paste target prices from Excel or enter manually, one price per line. Invalid entries will be ignored."}),(0,r.jsx)(z,{id:"target-prices-input",value:m,onChange:e=>p(e.target.value),placeholder:"50000 50500 49800",className:"min-h-[200px] bg-input border-2 border-border focus:border-primary font-mono"}),A.message&&(0,r.jsx)("p",{className:"text-sm ".concat(A.hasOverlap?"text-red-500":"text-green-500"),children:A.message})]})}),(0,r.jsxs)(H.av,{value:"automatic",className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(T.J,{htmlFor:"targetCount",children:"Number of Targets"}),(0,r.jsxs)(E.l6,{value:x,onValueChange:h,children:[(0,r.jsx)(E.bq,{children:(0,r.jsx)(E.yv,{})}),(0,r.jsx)(E.gC,{children:[4,6,8,10,12,15,20].map(e=>(0,r.jsxs)(E.eb,{value:e.toString(),children:[e," targets"]},e))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(T.J,{htmlFor:"priceRange",children:"Price Range (%)"}),(0,r.jsxs)(E.l6,{value:f,onValueChange:g,children:[(0,r.jsx)(E.bq,{children:(0,r.jsx)(E.yv,{})}),(0,r.jsx)(E.gC,{children:[2,2.5,3,3.5,4,4.5,5,6,7,8,10].map(e=>(0,r.jsxs)(E.eb,{value:e.toString(),children:["\xb1",e,"%"]},e))})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(T.J,{htmlFor:"distribution",children:"Distribution Pattern"}),(0,r.jsxs)(E.l6,{value:b,onValueChange:y,children:[(0,r.jsx)(E.bq,{children:(0,r.jsx)(E.yv,{})}),(0,r.jsxs)(E.gC,{children:[(0,r.jsx)(E.eb,{value:"even",children:"Even Distribution"}),(0,r.jsx)(E.eb,{value:"fibonacci",children:"Fibonacci (More near current price)"}),(0,r.jsx)(E.eb,{value:"exponential",children:"Exponential (Wider spread)"})]})]})]}),(0,r.jsx)("div",{className:"bg-muted p-3 rounded-md",children:(0,r.jsxs)("p",{className:"text-sm",children:[(0,r.jsx)("strong",{children:"Current Market Price:"})," $",j.toLocaleString(),(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Slippage:"})," \xb1",v,"% ($",(j*v/100).toFixed(0),")",(0,r.jsx)("br",{}),(0,r.jsx)("strong",{children:"Range:"})," $",(j*(1-parseFloat(f)/100)).toLocaleString()," - $",(j*(1+parseFloat(f)/100)).toLocaleString()]})}),(0,r.jsxs)(c.$,{onClick:()=>{p(S().join("\n"))},className:"w-full btn-neo",children:["Generate ",x," Target Prices"]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(T.J,{children:"Generated Prices (Preview)"}),(0,r.jsx)(z,{value:m,onChange:e=>p(e.target.value),className:"min-h-[150px] bg-input border-2 border-border focus:border-primary font-mono",placeholder:"Click 'Generate' to create automatic target prices..."}),A.message&&(0,r.jsx)("p",{className:"text-sm ".concat(A.hasOverlap?"text-red-500":"text-green-500"),children:A.message})]})]})]}),(0,r.jsxs)(F,{children:[(0,r.jsx)(P,{asChild:!0,children:(0,r.jsx)(c.$,{type:"button",variant:"outline",className:"btn-outline-neo",children:"Cancel"})}),(0,r.jsx)(c.$,{type:"button",onClick:()=>{let e=m.split("\n").map(e=>e.trim()).filter(e=>""!==e),a=e.map(e=>parseFloat(e)).filter(e=>!isNaN(e)&&e>0);if(0===a.length&&e.length>0){u({title:"Invalid Input",description:"No valid prices found. Please enter numbers, one per line.",variant:"destructive"});return}let t=w();if(t.hasOverlap){u({title:"Slippage Zone Overlap",description:t.message,variant:"destructive"});return}i(a),u({title:"Target Prices Updated",description:"".concat(a.length," target prices have been set.")}),p(""),n()},disabled:A.hasOverlap,className:"btn-neo",children:"Save Prices"})]})]})})}var $=t(29348);let W=["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"],X=["USDC","DAI","TUSD","FDUSD","USDT","EUR"];var q=t(11133);function Y(e){let{label:a,value:t,allowedCryptos:l,onValidCrypto:n,placeholder:i="Enter crypto symbol",description:o,className:d}=e,[m,p]=(0,s.useState)(""),[u,x]=(0,s.useState)("idle"),[h,f]=(0,s.useState)(""),[g,b]=(0,s.useState)(!1);(0,s.useEffect)(()=>{t&&t!==m?(p(t),x("valid"),b(!0)):t||b(!1)},[t]);let y=()=>{let e=m.toUpperCase().trim();if(!e){x("invalid"),f("Please enter a crypto symbol");return}if(!l||!Array.isArray(l)){x("invalid"),f("No allowed cryptocurrencies configured");return}l.includes(e)?(x("valid"),f(""),b(!0),n(e)):(x("invalid"),f("".concat(e," is not available. Allowed: ").concat(l.join(", "))))};return(0,r.jsxs)("div",{className:(0,D.cn)("space-y-2",d),children:[(0,r.jsx)(T.J,{htmlFor:"crypto-input-".concat(a),children:a}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[(0,r.jsx)(S.p,{id:"crypto-input-".concat(a),value:m||t,onChange:e=>{p(e.target.value),x("idle"),f("")},onKeyPress:e=>{"Enter"===e.key&&y()},placeholder:i,className:(0,D.cn)("pr-8",(()=>{switch(u){case"valid":return"border-green-500";case"invalid":return"border-red-500";default:return""}})())}),"idle"!==u&&(0,r.jsx)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:(()=>{switch(u){case"valid":return(0,r.jsx)(A.A,{className:"h-4 w-4 text-green-500"});case"invalid":return(0,r.jsx)(O.A,{className:"h-4 w-4 text-red-500"});default:return null}})()})]}),(0,r.jsx)(c.$,{onClick:y,variant:"outline",className:"btn-neo",disabled:!m.trim(),children:"Check"})]}),t&&g&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(A.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsxs)("span",{className:"text-green-600 font-medium",children:["Selected: ",t]})]}),"invalid"===u&&h&&(0,r.jsxs)("div",{className:"flex items-start space-x-2 text-sm text-red-600",children:[(0,r.jsx)(q.A,{className:"h-4 w-4 mt-0.5 flex-shrink-0"}),(0,r.jsx)("span",{children:h})]}),o&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:o}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[l&&Array.isArray(l)?l.length:0," cryptocurrencies available"]})]})}var K=t(54073);let Q=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsxs)(K.bL,{ref:a,className:(0,D.cn)("relative flex w-full touch-none select-none items-center",t),...s,children:[(0,r.jsx)(K.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,r.jsx)(K.Q6,{className:"absolute h-full bg-primary"})}),(0,r.jsx)(K.zi,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})});Q.displayName=K.bL.displayName;var ee=t(18271),ea=t(60620),et=t(13300);let er=[{value:"G_hades_curse.wav",label:"Hades Curse"},{value:"G_hades_demat.wav",label:"Hades Demat"},{value:"G_hades_mat.wav",label:"Hades Mat"},{value:"G_hades_sanctify.wav",label:"Hades Sanctify"},{value:"S_mon1.mp3",label:"Monster 1"},{value:"S_mon2.mp3",label:"Monster 2"},{value:"Satyr_atk4.wav",label:"Satyr Attack"},{value:"bells.wav",label:"Bells"},{value:"bird1.wav",label:"Bird 1"},{value:"bird7.wav",label:"Bird 7"},{value:"cheer.wav",label:"Cheer"},{value:"chest1.wav",label:"Chest"},{value:"chime2.wav",label:"Chime"},{value:"dark2.wav",label:"Dark"},{value:"foundry2.wav",label:"Foundry"},{value:"goatherd1.wav",label:"Goatherd"},{value:"marble1.wav",label:"Marble"},{value:"sanctuary1.wav",label:"Sanctuary"},{value:"space_bells4a.wav",label:"Space Bells"},{value:"sparrow1.wav",label:"Sparrow"},{value:"tax3.wav",label:"Tax"},{value:"wolf4.wav",label:"Wolf"}];function es(e){let{isOpen:a,onClose:t}=e,{sessionAlarmConfig:l,dispatch:n,playSessionAlarm:i}=(0,N.U)(),{toast:o}=(0,J.dj)(),[d,m]=(0,s.useState)(l),p=e=>{try{let a=new Audio("/ringtones/".concat(e));a.volume=d.volume/100,a.play().catch(console.error)}catch(e){console.error("Failed to play test sound:",e),o({title:"Sound Test Failed",description:"Could not play the selected sound file.",variant:"destructive"})}};return(0,r.jsx)(B,{open:a,onOpenChange:t,children:(0,r.jsxs)(_,{className:"sm:max-w-[500px] max-h-[80vh]",children:[(0,r.jsxs)(L,{children:[(0,r.jsxs)(V,{className:"flex items-center gap-2",children:[(0,r.jsx)(ee.A,{className:"h-5 w-5"}),"Session Alarm Settings"]}),(0,r.jsx)(G,{children:"Configure custom alarm sounds and settings for this trading session."})]}),(0,r.jsxs)("div",{className:"space-y-6 overflow-y-auto max-h-[60vh] pr-2",children:[(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"text-sm flex items-center gap-2",children:[(0,r.jsx)(ea.A,{className:"h-4 w-4"}),"Volume Control"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(T.J,{children:["Volume: ",d.volume,"%"]}),(0,r.jsx)(Q,{value:[d.volume],onValueChange:e=>m({...d,volume:e[0]}),max:100,min:0,step:5,className:"w-full"})]})})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-sm text-green-600",children:"Order Execution Success Alarms"})}),(0,r.jsxs)(f.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(C,{id:"buyAlarmEnabled",checked:d.buyAlarmEnabled,onCheckedChange:e=>m({...d,buyAlarmEnabled:e})}),(0,r.jsx)(T.J,{htmlFor:"buyAlarmEnabled",children:"Enable alerts on successful order execution"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(T.J,{children:"Success Alarm Sound"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(E.l6,{value:d.buyAlarmSound,onValueChange:e=>m({...d,buyAlarmSound:e}),children:[(0,r.jsx)(E.bq,{className:"flex-1",children:(0,r.jsx)(E.yv,{})}),(0,r.jsx)(E.gC,{className:"max-h-[200px] overflow-y-auto",children:er.map(e=>(0,r.jsx)(E.eb,{value:e.value,children:e.label},e.value))})]}),(0,r.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>p(d.buyAlarmSound),disabled:!d.buyAlarmEnabled,children:(0,r.jsx)(et.A,{className:"h-4 w-4"})})]})]})]})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-sm text-red-600",children:"Error/Failure Alarms"})}),(0,r.jsxs)(f.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(C,{id:"sellAlarmEnabled",checked:d.sellAlarmEnabled,onCheckedChange:e=>m({...d,sellAlarmEnabled:e})}),(0,r.jsx)(T.J,{htmlFor:"sellAlarmEnabled",children:"Enable alerts on errors/failures"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(T.J,{children:"Error Alarm Sound"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(E.l6,{value:d.sellAlarmSound,onValueChange:e=>m({...d,sellAlarmSound:e}),children:[(0,r.jsx)(E.bq,{className:"flex-1",children:(0,r.jsx)(E.yv,{})}),(0,r.jsx)(E.gC,{className:"max-h-[200px] overflow-y-auto",children:er.map(e=>(0,r.jsx)(E.eb,{value:e.value,children:e.label},e.value))})]}),(0,r.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>p(d.sellAlarmSound),disabled:!d.sellAlarmEnabled,children:(0,r.jsx)(et.A,{className:"h-4 w-4"})})]})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,r.jsx)(c.$,{variant:"outline",onClick:t,children:"Cancel"}),(0,r.jsx)(c.$,{onClick:()=>{n({type:"SET_SESSION_ALARM_CONFIG",payload:d}),o({title:"Alarm Settings Saved",description:"Your session-specific alarm settings have been updated.",duration:3e3}),t()},className:"btn-neo",children:"Save Settings"})]})]})]})})}var el=t(94063),en=t(73672);async function ei(e,a,t,r){try{if(!t.enabled||!t.botToken||!t.chatId)return console.warn("Telegram notifications disabled or not configured"),!1;let s={error:"\uD83D\uDEA8",success:"✅",info:"ℹ️",warning:"⚠️",trade:"\uD83D\uDCB0",connection:"\uD83C\uDF10"}[a]||"ℹ️",l=function(e,a,t,r){let s=new Date().toLocaleString(),l="".concat(t," <b>PLUTO TRADING BOT</b>\n\n").concat(e,"\n\n⏰ ").concat(s);return r&&Object.keys(r).length>0&&(l+="\n\n<i>Additional Info:</i>",Object.entries(r).forEach(e=>{let[a,t]=e;l+="\n• ".concat(a,": ").concat(t)})),l}(e,0,s,r),n=await fetch("https://api.telegram.org/bot".concat(t.botToken,"/sendMessage"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:t.chatId,text:l,parse_mode:"HTML",disable_web_page_preview:!0})});if(!n.ok)throw Error("Telegram API error: ".concat(n.status," ").concat(n.statusText));return console.log("\uD83D\uDCF1 Telegram notification sent: ".concat(a)),!0}catch(e){return console.error("Failed to send Telegram notification:",e),!1}}async function eo(e){return await ei("Telegram notifications are working correctly! \uD83C\uDF89","success",e,{test:!0,timestamp:new Date().toISOString()})}function ec(e){let a=[];return e.enabled?(e.botToken?e.botToken.match(/^\d+:[A-Za-z0-9_-]+$/)||a.push("Invalid bot token format"):a.push("Bot token is required"),e.chatId?e.chatId.match(/^-?\d+$/)||a.push("Invalid chat ID format"):a.push("Chat ID is required"),{valid:0===a.length,errors:a}):{valid:!0,errors:[]}}function ed(e){let{isOpen:a,onClose:t}=e,{telegramConfig:l,dispatch:n}=(0,N.U)(),{toast:i}=(0,J.dj)(),[o,d]=(0,s.useState)(l),[m,p]=(0,s.useState)(!1),u=async()=>{let e=ec(o);if(!e.valid){i({title:"Configuration Error",description:e.errors.join(", "),variant:"destructive"});return}p(!0);try{await eo(o)?i({title:"Test Successful",description:"Test message sent successfully! Check your Telegram chat.",duration:5e3}):i({title:"Test Failed",description:"Could not send test message. Please check your configuration.",variant:"destructive"})}catch(e){i({title:"Test Failed",description:"An error occurred while testing the connection.",variant:"destructive"})}finally{p(!1)}};return(0,r.jsx)(B,{open:a,onOpenChange:t,children:(0,r.jsxs)(_,{className:"sm:max-w-[600px] max-h-[90vh] flex flex-col",children:[(0,r.jsxs)(L,{className:"flex-shrink-0",children:[(0,r.jsxs)(V,{className:"flex items-center gap-2",children:[(0,r.jsx)(el.A,{className:"h-5 w-5"}),"Telegram Notifications"]}),(0,r.jsx)(G,{children:"Configure Telegram notifications for trading alerts, errors, and system events."})]}),(0,r.jsxs)("div",{className:"space-y-6 overflow-y-auto flex-1 pr-2",children:[(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-sm",children:"Notification Settings"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(C,{id:"telegramEnabled",checked:o.enabled,onCheckedChange:e=>d({...o,enabled:e})}),(0,r.jsx)(T.J,{htmlFor:"telegramEnabled",children:"Enable Telegram notifications"})]})})]}),o.enabled&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(f.Zp,{children:[(0,r.jsxs)(f.aR,{children:[(0,r.jsx)(f.ZB,{className:"text-sm",children:"Bot Configuration"}),(0,r.jsx)(f.BT,{children:"Create a Telegram bot via @BotFather and get your bot token."})]}),(0,r.jsxs)(f.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(T.J,{htmlFor:"botToken",children:"Bot Token"}),(0,r.jsx)(S.p,{id:"botToken",type:"password",value:o.botToken,onChange:e=>d({...o,botToken:e.target.value}),placeholder:"123456789:ABCdefGHIjklMNOpqrsTUVwxyz"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Format: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(T.J,{htmlFor:"chatId",children:"Chat ID"}),(0,r.jsx)(S.p,{id:"chatId",value:o.chatId,onChange:e=>d({...o,chatId:e.target.value}),placeholder:"-1001234567890 or 123456789"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Your personal chat ID or group chat ID (starts with -)"})]})]})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-sm",children:"Setup Instructions"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"font-medium text-primary",children:"1."}),(0,r.jsx)("span",{children:"Message @BotFather on Telegram and create a new bot with /newbot"})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"font-medium text-primary",children:"2."}),(0,r.jsx)("span",{children:"Copy the bot token and paste it above"})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"font-medium text-primary",children:"3."}),(0,r.jsx)("span",{children:"Message @userinfobot to get your chat ID"})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"font-medium text-primary",children:"4."}),(0,r.jsx)("span",{children:"Start a conversation with your bot first"})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)("span",{className:"font-medium text-primary",children:"5."}),(0,r.jsx)("span",{children:"Test the connection using the button below"})]})]})})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-sm",children:"Test Connection"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)(c.$,{onClick:u,disabled:m||!o.botToken||!o.chatId,className:"w-full",variant:"outline",children:m?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"}),"Sending Test Message..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(en.A,{className:"h-4 w-4 mr-2"}),"Send Test Message"]})})})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-border flex-shrink-0",children:[(0,r.jsx)(c.$,{variant:"outline",onClick:t,children:"Cancel"}),(0,r.jsx)(c.$,{onClick:()=>{let e=ec(o);if(!e.valid){i({title:"Configuration Error",description:e.errors.join(", "),variant:"destructive"});return}n({type:"SET_TELEGRAM_CONFIG",payload:o}),i({title:"Telegram Settings Saved",description:"Your Telegram notification settings have been updated.",duration:3e3}),t()},className:"btn-neo",children:"Save Settings"})]})]})})}var em=t(80659),ep=t(75074),eu=t(50594);let ex=[{id:"BTC_USDT",crypto1:"BTC",crypto2:"USDT",displayName:"Bitcoin / Tether",category:"major",minTradeAmount:1e-4,maxTradeAmount:10,priceDecimals:2,amountDecimals:6,description:"The most popular cryptocurrency pair",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"ETH_USDT",crypto1:"ETH",crypto2:"USDT",displayName:"Ethereum / Tether",category:"major",minTradeAmount:.001,maxTradeAmount:100,priceDecimals:2,amountDecimals:6,description:"Second largest cryptocurrency by market cap",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"BNB_USDT",crypto1:"BNB",crypto2:"USDT",displayName:"Binance Coin / Tether",category:"major",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Binance exchange native token",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"ADA_USDT",crypto1:"ADA",crypto2:"USDT",displayName:"Cardano / Tether",category:"major",minTradeAmount:1,maxTradeAmount:1e5,priceDecimals:4,amountDecimals:2,description:"Proof-of-stake blockchain platform",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"SOL_USDT",crypto1:"SOL",crypto2:"USDT",displayName:"Solana / Tether",category:"major",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"High-performance blockchain for DeFi and NFTs",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"DOT_USDT",crypto1:"DOT",crypto2:"USDT",displayName:"Polkadot / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:1e4,priceDecimals:3,amountDecimals:3,description:"Multi-chain interoperability protocol",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"LINK_USDT",crypto1:"LINK",crypto2:"USDT",displayName:"Chainlink / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:5e3,priceDecimals:3,amountDecimals:3,description:"Decentralized oracle network",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"MATIC_USDT",crypto1:"MATIC",crypto2:"USDT",displayName:"Polygon / Tether",category:"altcoin",minTradeAmount:1,maxTradeAmount:5e4,priceDecimals:4,amountDecimals:2,description:"Ethereum scaling solution",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"AVAX_USDT",crypto1:"AVAX",crypto2:"USDT",displayName:"Avalanche / Tether",category:"altcoin",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Fast and eco-friendly blockchain platform",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"ATOM_USDT",crypto1:"ATOM",crypto2:"USDT",displayName:"Cosmos / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:5e3,priceDecimals:3,amountDecimals:3,description:"Internet of blockchains",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"UNI_USDT",crypto1:"UNI",crypto2:"USDT",displayName:"Uniswap / Tether",category:"defi",minTradeAmount:.1,maxTradeAmount:5e3,priceDecimals:3,amountDecimals:3,description:"Leading decentralized exchange token",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"AAVE_USDT",crypto1:"AAVE",crypto2:"USDT",displayName:"Aave / Tether",category:"defi",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Decentralized lending protocol",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"COMP_USDT",crypto1:"COMP",crypto2:"USDT",displayName:"Compound / Tether",category:"defi",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Algorithmic money market protocol",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"SUSHI_USDT",crypto1:"SUSHI",crypto2:"USDT",displayName:"SushiSwap / Tether",category:"defi",minTradeAmount:.1,maxTradeAmount:1e4,priceDecimals:4,amountDecimals:3,description:"Community-driven DEX and DeFi platform",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"CRV_USDT",crypto1:"CRV",crypto2:"USDT",displayName:"Curve DAO / Tether",category:"defi",minTradeAmount:1,maxTradeAmount:5e4,priceDecimals:4,amountDecimals:2,description:"Decentralized exchange for stablecoins",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"DOGE_USDT",crypto1:"DOGE",crypto2:"USDT",displayName:"Dogecoin / Tether",category:"meme",minTradeAmount:10,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:0,description:"The original meme cryptocurrency",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"SHIB_USDT",crypto1:"SHIB",crypto2:"USDT",displayName:"Shiba Inu / Tether",category:"meme",minTradeAmount:1e5,maxTradeAmount:1e8,priceDecimals:8,amountDecimals:0,description:"Dogecoin killer meme token",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"USDC_USDT",crypto1:"USDC",crypto2:"USDT",displayName:"USD Coin / Tether",category:"stablecoin",minTradeAmount:1,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:2,description:"Stablecoin arbitrage pair",supportedModes:["StablecoinSwap"]},{id:"DAI_USDT",crypto1:"DAI",crypto2:"USDT",displayName:"Dai / Tether",category:"stablecoin",minTradeAmount:1,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:2,description:"Decentralized stablecoin pair",supportedModes:["StablecoinSwap"]},{id:"BTC_ETH",crypto1:"BTC",crypto2:"ETH",displayName:"Bitcoin / Ethereum",category:"major",minTradeAmount:1e-4,maxTradeAmount:10,priceDecimals:4,amountDecimals:6,description:"Top two cryptocurrencies pair",supportedModes:["StablecoinSwap"]},{id:"ETH_BNB",crypto1:"ETH",crypto2:"BNB",displayName:"Ethereum / Binance Coin",category:"major",minTradeAmount:.001,maxTradeAmount:100,priceDecimals:4,amountDecimals:6,description:"Ethereum vs Binance ecosystem",supportedModes:["StablecoinSwap"]},{id:"XRP_USDT",crypto1:"XRP",crypto2:"USDT",displayName:"Ripple / Tether",category:"major",minTradeAmount:1,maxTradeAmount:1e5,priceDecimals:4,amountDecimals:2,description:"Cross-border payment cryptocurrency",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"LTC_USDT",crypto1:"LTC",crypto2:"USDT",displayName:"Litecoin / Tether",category:"major",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Silver to Bitcoin's gold",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"BCH_USDT",crypto1:"BCH",crypto2:"USDT",displayName:"Bitcoin Cash / Tether",category:"major",minTradeAmount:.001,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:5,description:"Bitcoin fork with larger blocks",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"TRX_USDT",crypto1:"TRX",crypto2:"USDT",displayName:"TRON / Tether",category:"altcoin",minTradeAmount:10,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:0,description:"Decentralized entertainment ecosystem",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"EOS_USDT",crypto1:"EOS",crypto2:"USDT",displayName:"EOS / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:1e4,priceDecimals:4,amountDecimals:3,description:"Delegated proof-of-stake blockchain",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"XLM_USDT",crypto1:"XLM",crypto2:"USDT",displayName:"Stellar / Tether",category:"altcoin",minTradeAmount:1,maxTradeAmount:1e5,priceDecimals:5,amountDecimals:2,description:"Fast and low-cost cross-border payments",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"VET_USDT",crypto1:"VET",crypto2:"USDT",displayName:"VeChain / Tether",category:"altcoin",minTradeAmount:10,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:0,description:"Supply chain management blockchain",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"FIL_USDT",crypto1:"FIL",crypto2:"USDT",displayName:"Filecoin / Tether",category:"altcoin",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:3,amountDecimals:4,description:"Decentralized storage network",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"THETA_USDT",crypto1:"THETA",crypto2:"USDT",displayName:"Theta Network / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:1e4,priceDecimals:4,amountDecimals:3,description:"Decentralized video streaming network",supportedModes:["SimpleSpot","StablecoinSwap"]}],eh=[{value:"major",label:"Major Cryptocurrencies",description:"Top market cap cryptocurrencies"},{value:"altcoin",label:"Altcoins",description:"Alternative cryptocurrencies"},{value:"defi",label:"DeFi Tokens",description:"Decentralized finance tokens"},{value:"meme",label:"Meme Coins",description:"Community-driven meme tokens"},{value:"stablecoin",label:"Stablecoins",description:"Price-stable cryptocurrencies"}];function ef(e){let{isOpen:a,onClose:t}=e,{config:l,dispatch:n}=(0,N.U)(),{toast:i}=(0,J.dj)(),[o,d]=(0,s.useState)(""),[m,p]=(0,s.useState)("all"),[u,x]=(0,s.useState)(null),g=(0,s.useMemo)(()=>{var e;let a=(e=l.tradingMode||"SimpleSpot",ex.filter(a=>a.supportedModes.includes(e)));return"all"!==m&&(a=a.filter(e=>e.category===m)),o.trim()&&(a=(function(e){let a=e.toLowerCase();return ex.filter(e=>e.crypto1.toLowerCase().includes(a)||e.crypto2.toLowerCase().includes(a)||e.displayName.toLowerCase().includes(a)||e.description.toLowerCase().includes(a))})(o).filter(e=>e.supportedModes.includes(l.tradingMode||"SimpleSpot"))),a},[l.tradingMode,m,o]),b=e=>{x(e)},y=e=>({major:"bg-blue-100 text-blue-800",altcoin:"bg-green-100 text-green-800",defi:"bg-purple-100 text-purple-800",meme:"bg-orange-100 text-orange-800",stablecoin:"bg-gray-100 text-gray-800"})[e]||"bg-gray-100 text-gray-800";return(0,r.jsx)(B,{open:a,onOpenChange:t,children:(0,r.jsxs)(_,{className:"sm:max-w-[700px] max-h-[80vh]",children:[(0,r.jsxs)(L,{children:[(0,r.jsxs)(V,{className:"flex items-center gap-2",children:[(0,r.jsx)(em.A,{className:"h-5 w-5"}),"Select Trading Pair"]}),(0,r.jsxs)(G,{children:["Choose from 50+ supported trading pairs for ",l.tradingMode||"SimpleSpot"," mode."]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)(T.J,{htmlFor:"search",children:"Search Pairs"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(ep.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(S.p,{id:"search",placeholder:"Search by symbol or name...",value:o,onChange:e=>d(e.target.value),className:"pl-10"})]})]}),(0,r.jsxs)("div",{className:"w-48",children:[(0,r.jsx)(T.J,{htmlFor:"category",children:"Category"}),(0,r.jsxs)(E.l6,{value:m,onValueChange:p,children:[(0,r.jsx)(E.bq,{id:"category",children:(0,r.jsx)(E.yv,{})}),(0,r.jsxs)(E.gC,{children:[(0,r.jsx)(E.eb,{value:"all",children:"All Categories"}),eh.map(e=>(0,r.jsx)(E.eb,{value:e.value,children:e.label},e.value))]})]})]})]}),(0,r.jsx)(U.F,{className:"h-[400px] border rounded-lg",children:(0,r.jsx)("div",{className:"p-4 space-y-2",children:0===g.length?(0,r.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,r.jsx)(em.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{children:"No trading pairs found matching your criteria."})]}):g.map(e=>(0,r.jsx)(f.Zp,{className:"cursor-pointer transition-all hover:shadow-md ".concat((null==u?void 0:u.id)===e.id?"ring-2 ring-primary":""),onClick:()=>b(e),children:(0,r.jsx)(f.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsxs)("h3",{className:"font-semibold text-lg",children:[e.crypto1,"/",e.crypto2]}),(0,r.jsx)(h.E,{className:y(e.category),children:e.category})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,r.jsxs)("span",{children:["Min: ",e.minTradeAmount," ",e.crypto1]}),(0,r.jsxs)("span",{children:["Max: ",e.maxTradeAmount," ",e.crypto1]}),(0,r.jsxs)("span",{children:["Decimals: ",e.priceDecimals]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-sm font-medium",children:e.displayName}),(0,r.jsx)("div",{className:"flex gap-1 mt-1",children:e.supportedModes.map(e=>(0,r.jsx)(h.E,{variant:"outline",className:"text-xs",children:e},e))})]})]})})},e.id))})}),u&&(0,r.jsxs)(f.Zp,{className:"bg-muted/50",children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"text-sm flex items-center gap-2",children:[(0,r.jsx)(eu.A,{className:"h-4 w-4"}),"Selected Pair Details"]})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Pair:"})," ",u.displayName]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Category:"})," ",u.category]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Min Trade:"})," ",u.minTradeAmount," ",u.crypto1]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Max Trade:"})," ",u.maxTradeAmount," ",u.crypto1]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Price Decimals:"})," ",u.priceDecimals]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Amount Decimals:"})," ",u.amountDecimals]})]})})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,r.jsx)(c.$,{variant:"outline",onClick:t,children:"Cancel"}),(0,r.jsx)(c.$,{onClick:()=>{if(!u){i({title:"No Pair Selected",description:"Please select a trading pair first.",variant:"destructive"});return}n({type:"SET_CONFIG",payload:{crypto1:u.crypto1,crypto2:u.crypto2}}),i({title:"Trading Pair Updated",description:"Now trading ".concat(u.displayName),duration:3e3}),t()},disabled:!u,className:"btn-neo",children:"Apply Trading Pair"})]})]})]})})}var eg=t(8803),eb=t(50172),ey=t(30955),ej=t(8531);let ev=["USDT","USDC","BTC"],eN=["USDC","DAI","TUSD","FDUSD","USDT","EUR"];function eS(){let e;try{e=(0,N.U)()}catch(e){return console.error("Trading context not available:",e),(0,r.jsx)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("p",{className:"text-red-500",children:"Trading context not available. Please refresh the page."})})})}let{config:a,dispatch:t,botSystemStatus:l,appSettings:n,setTargetPrices:i}=e,o="Running"===l,d="WarmingUp"===l,{toast:m}=(0,J.dj)(),[p,u]=(0,s.useState)(!1),[x,h]=(0,s.useState)(!1),[g,b]=(0,s.useState)(!1),[y,j]=(0,s.useState)(!1),v=e=>{let a;let{name:r,value:s,type:l,checked:n}=e.target;if("checkbox"===l)a=n;else if("number"===l){if(""===s||null==s)a=0;else{let e=parseFloat(s);a=isNaN(e)?0:e}}else a=s;t({type:"SET_CONFIG",payload:{[r]:a}})},w=(e,r)=>{if(t({type:"SET_CONFIG",payload:{[e]:r}}),"crypto1"===e){let e=$.vA[r]||ev||["USDT","USDC","BTC"];a.crypto2&&Array.isArray(e)&&e.includes(a.crypto2)||t({type:"SET_CONFIG",payload:{crypto2:e[0]||"USDT"}})}},A=(e,a)=>{let r=parseFloat(a);isNaN(r)&&(r=0),r<0&&(r=0),r>100&&(r=100),"incomeSplitCrypto1Percent"===e?t({type:"SET_CONFIG",payload:{incomeSplitCrypto1Percent:r,incomeSplitCrypto2Percent:100-r}}):t({type:"SET_CONFIG",payload:{incomeSplitCrypto2Percent:r,incomeSplitCrypto1Percent:100-r}})},M=$.hg||[];return"SimpleSpot"===a.tradingMode?$.vA[a.crypto1]:($.hg||[]).filter(e=>e!==a.crypto1),console.log("\uD83D\uDD0D DEBUG: AVAILABLE_CRYPTOS length:",null===$.hg||void 0===$.hg?void 0:$.hg.length),console.log("\uD83D\uDD0D DEBUG: crypto1Options length:",M.length),console.log("\uD83D\uDD0D DEBUG: First 20 cryptos:",null===$.hg||void 0===$.hg?void 0:$.hg.slice(0,20)),console.log("\uD83D\uDD0D DEBUG: ALLOWED_CRYPTO1:",W),console.log("\uD83D\uDD0D DEBUG: ALLOWED_CRYPTO2:",X),console.log("\uD83D\uDD0D DEBUG: AVAILABLE_STABLECOINS:",$.Ql),(0,r.jsxs)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,r.jsx)("h2",{className:"text-2xl font-bold text-sidebar-primary",children:"Trading Configuration"})}),(0,r.jsx)(U.F,{className:"flex-1 pr-2",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(f.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Mode"})}),(0,r.jsxs)(f.Wu,{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(C,{id:"enableStablecoinSwap",checked:"StablecoinSwap"===a.tradingMode,onCheckedChange:e=>{let r;let s=e?"StablecoinSwap":"SimpleSpot";r="StablecoinSwap"===s?(W||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(e=>e!==a.crypto1)[0]:(X||["USDC","DAI","TUSD","FDUSD","USDT","EUR"])[0],t({type:"SET_CONFIG",payload:{tradingMode:s,crypto2:r}})}}),(0,r.jsx)(T.J,{htmlFor:"enableStablecoinSwap",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Enable Stablecoin Swap Mode"})]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Current mode: ","SimpleSpot"===a.tradingMode?"Simple Spot":"Stablecoin Swap"]}),"StablecoinSwap"===a.tradingMode&&(0,r.jsxs)("div",{children:[(0,r.jsx)(T.J,{htmlFor:"preferredStablecoin",children:"Preferred Stablecoin"}),(0,r.jsxs)(E.l6,{name:"preferredStablecoin",value:a.preferredStablecoin,onValueChange:e=>w("preferredStablecoin",e),children:[(0,r.jsx)(E.bq,{id:"preferredStablecoin",children:(0,r.jsx)(E.yv,{placeholder:"Select stablecoin"})}),(0,r.jsx)(E.gC,{className:"max-h-[300px] overflow-y-auto",children:eN.map(e=>(0,r.jsx)(E.eb,{value:e,children:e},e))})]})]})]})]}),(0,r.jsxs)(f.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Pair"})}),(0,r.jsxs)(f.Wu,{className:"space-y-4",children:[(0,r.jsx)(Y,{label:"Crypto 1 (Base)",value:a.crypto1,allowedCryptos:W||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"],onValidCrypto:e=>{if(t({type:"SET_CONFIG",payload:{crypto1:e}}),"StablecoinSwap"===a.tradingMode){let r=(W||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(a=>a!==e);r.includes(a.crypto2)&&a.crypto2!==e||t({type:"SET_CONFIG",payload:{crypto2:r[0]}})}else{let e=X||["USDC","DAI","TUSD","FDUSD","USDT","EUR"];e.includes(a.crypto2)||t({type:"SET_CONFIG",payload:{crypto2:e[0]}})}},placeholder:"e.g., BTC, ETH, SOL",description:"Enter the base cryptocurrency symbol"}),(0,r.jsx)(Y,{label:"StablecoinSwap"===a.tradingMode?"Crypto 2":"Crypto 2 (Stablecoin)",value:a.crypto2,allowedCryptos:"StablecoinSwap"===a.tradingMode?(W||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(e=>e!==a.crypto1):X||["USDC","DAI","TUSD","FDUSD","USDT","EUR"],onValidCrypto:e=>{("StablecoinSwap"!==a.tradingMode||e!==a.crypto1)&&t({type:"SET_CONFIG",payload:{crypto2:e}})},placeholder:"StablecoinSwap"===a.tradingMode?"e.g., BTC, ETH, SOL":"e.g., USDT, USDC, DAI",description:"StablecoinSwap"===a.tradingMode?"Enter the second cryptocurrency symbol":"Enter the quote/stablecoin symbol"})]})]}),(0,r.jsxs)(f.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-sidebar-accent-foreground",children:"Parameters"})}),(0,r.jsxs)(f.Wu,{className:"space-y-3",children:[[{name:"baseBid",label:"Base Bid (Crypto 2)",type:"number",step:"0.01"},{name:"multiplier",label:"Multiplier",type:"number",step:"0.001"},{name:"numDigits",label:"Display Digits",type:"number",step:"1"},{name:"slippagePercent",label:"Slippage %",type:"number",step:"0.01"}].map(e=>(0,r.jsxs)("div",{children:[(0,r.jsx)(T.J,{htmlFor:e.name,children:e.label}),(0,r.jsx)(S.p,{id:e.name,name:e.name,type:e.type,value:a[e.name],onChange:v,step:e.step,min:"0"})]},e.name)),(0,r.jsxs)("div",{children:[(0,r.jsx)(T.J,{children:"Couple Income % Split (must sum to 100)"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(T.J,{htmlFor:"incomeSplitCrypto1Percent",className:"text-xs",children:[a.crypto1||"Crypto 1","%"]}),(0,r.jsx)(S.p,{id:"incomeSplitCrypto1Percent",type:"number",value:a.incomeSplitCrypto1Percent,onChange:e=>A("incomeSplitCrypto1Percent",e.target.value),min:"0",max:"100"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)(T.J,{htmlFor:"incomeSplitCrypto2Percent",className:"text-xs",children:[a.crypto2||"Crypto 2","%"]}),(0,r.jsx)(S.p,{id:"incomeSplitCrypto2Percent",type:"number",value:a.incomeSplitCrypto2Percent,onChange:e=>A("incomeSplitCrypto2Percent",e.target.value),min:"0",max:"100"})]})]})]})]})]})]})}),(0,r.jsxs)("div",{className:"flex-shrink-0 mt-4",children:[(0,r.jsx)(k.w,{className:"mb-4 bg-sidebar-border"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:(0,D.cn)("text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border",o?"bg-green-600 text-primary-foreground":"bg-muted text-muted-foreground"),children:[o?(0,r.jsx)(eg.A,{className:"h-4 w-4"}):d?(0,r.jsx)(eb.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,r.jsx)(ey.A,{className:"h-4 w-4"}),"Bot Status: ",o?"Running":d?"Warming Up":"Stopped"]}),(0,r.jsx)(c.$,{onClick:()=>u(!0),className:"w-full btn-outline-neo",children:"Set Target Prices"}),(0,r.jsx)(c.$,{onClick:()=>h(!0),className:"w-full btn-outline-neo",children:"Session Alarms"}),(0,r.jsx)(c.$,{onClick:()=>b(!0),className:"w-full btn-outline-neo",children:"Telegram Settings"}),(0,r.jsx)(c.$,{onClick:()=>j(!0),className:"w-full btn-outline-neo",children:"Select Trading Pair"}),(0,r.jsxs)(c.$,{onClick:()=>{o?t({type:"SYSTEM_STOP_BOT"}):t({type:"SYSTEM_START_BOT_INITIATE"})},className:(0,D.cn)("w-full btn-neo",o||d?"bg-destructive hover:bg-destructive/90":"bg-green-600 hover:bg-green-600/90"),disabled:d,children:[o?(0,r.jsx)(eg.A,{className:"h-4 w-4"}):d?(0,r.jsx)(eb.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,r.jsx)(ey.A,{className:"h-4 w-4"}),o?"Stop Bot":d?"Warming Up...":"Start Bot"]}),(0,r.jsxs)(c.$,{onClick:()=>{t({type:"SYSTEM_RESET_BOT"}),m({title:"Bot Reset",description:"Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.",duration:4e3})},variant:"outline",className:"w-full btn-outline-neo",disabled:d,children:[(0,r.jsx)(ej.A,{className:"h-4 w-4 mr-2"}),"Reset Bot"]})]})]}),(0,r.jsx)(Z,{isOpen:p,onClose:()=>u(!1),onSetTargetPrices:i}),(0,r.jsx)(es,{isOpen:x,onClose:()=>h(!1)}),(0,r.jsx)(ed,{isOpen:g,onClose:()=>b(!1)}),(0,r.jsx)(ef,{isOpen:y,onClose:()=>j(!1)})]})}function eT(e){let{children:a}=e,{isAuthenticated:t,isLoading:s}=(0,d.A)(),l=(0,i.useRouter)();return s?(0,r.jsx)("div",{className:"flex items-center justify-center h-screen bg-background",children:(0,r.jsx)(eb.A,{className:"h-12 w-12 animate-spin text-primary"})}):t?(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-background text-foreground",children:[(0,r.jsx)(v,{}),(0,r.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,r.jsx)(eS,{}),(0,r.jsx)("main",{className:"flex-1 overflow-y-auto p-4 md:p-6 lg:p-8",children:a})]})]}):(l.push("/login"),null)}}},e=>{var a=a=>e(e.s=a);e.O(0,[823,297,875,631,655,831,318,441,684,358],()=>a(7926)),_N_E=e.O()}]);