exports.id=12,exports.ids=[12],exports.modules={8417:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});var s=t(60687),r=t(43210),l=t(60240);let i=({className:e,useFullName:a=!0})=>{let[t,i]=(0,r.useState)(!1);return(0,s.jsxs)("div",{className:`flex items-center text-2xl font-bold text-primary ${e}`,children:[t?(0,s.jsx)(l.A,{className:"mr-2 h-7 w-7"}):(0,s.jsx)("img",{src:"https://i.imgur.com/Q0HDcMH.png",alt:"Pluto Trading Bot Logo",className:"mr-2 h-7 w-7 rounded-full object-cover",onError:()=>{i(!0)},onLoad:()=>console.log("Pluto logo loaded successfully")}),(0,s.jsxs)("span",{children:["Pluto",a?" Trading Bot":""]})]})}},15079:(e,a,t)=>{"use strict";t.d(a,{bq:()=>p,eb:()=>g,gC:()=>h,l6:()=>d,yv:()=>m});var s=t(60687),r=t(43210),l=t(28850),i=t(61662),n=t(89743),o=t(58450),c=t(4780);let d=l.bL;l.YJ;let m=l.WT,p=r.forwardRef(({className:e,children:a,...t},r)=>(0,s.jsxs)(l.l9,{ref:r,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[a,(0,s.jsx)(l.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=l.l9.displayName;let u=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(l.PP,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}));u.displayName=l.PP.displayName;let x=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(l.wn,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}));x.displayName=l.wn.displayName;let h=r.forwardRef(({className:e,children:a,position:t="popper",...r},i)=>(0,s.jsx)(l.ZL,{children:(0,s.jsxs)(l.UC,{ref:i,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[(0,s.jsx)(u,{}),(0,s.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,s.jsx)(x,{})]})}));h.displayName=l.UC.displayName,r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(l.JU,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=l.JU.displayName;let g=r.forwardRef(({className:e,children:a,...t},r)=>(0,s.jsxs)(l.q7,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}),(0,s.jsx)(l.p4,{children:a})]}));g.displayName=l.q7.displayName,r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(l.wv,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=l.wv.displayName},20674:(e,a,t)=>{Promise.resolve().then(t.bind(t,63144))},29523:(e,a,t)=>{"use strict";t.d(a,{$:()=>c});var s=t(60687),r=t(43210),l=t(8730),i=t(24224),n=t(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef(({className:e,variant:a,size:t,asChild:r=!1,...i},c)=>{let d=r?l.DX:"button";return(0,s.jsx)(d,{className:(0,n.cn)(o({variant:a,size:t,className:e})),ref:c,...i})});c.displayName="Button"},35950:(e,a,t)=>{"use strict";t.d(a,{w:()=>n});var s=t(60687),r=t(43210),l=t(62369),i=t(4780);let n=r.forwardRef(({className:e,orientation:a="horizontal",decorative:t=!0,...r},n)=>(0,s.jsx)(l.b,{ref:n,decorative:t,orientation:a,className:(0,i.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",e),...r}));n.displayName=l.b.displayName},37079:(e,a,t)=>{"use strict";t.d(a,{A:()=>d});var s=t(60687);t(43210);var r=t(85763),l=t(16189),i=t(29272),n=t(44610),o=t(3341);let c=[{value:"orders",label:"Orders",href:"/dashboard",icon:(0,s.jsx)(i.A,{})},{value:"history",label:"History",href:"/dashboard/history",icon:(0,s.jsx)(n.A,{})},{value:"analytics",label:"Analytics",href:"/dashboard/analytics",icon:(0,s.jsx)(o.A,{})}];function d(){let e=(0,l.useRouter)(),a=(0,l.usePathname)(),t="orders";return"/dashboard/history"===a?t="history":"/dashboard/analytics"===a&&(t="analytics"),(0,s.jsx)(r.tU,{value:t,onValueChange:a=>{let t=c.find(e=>e.value===a);t&&e.push(t.href)},className:"w-full mb-6",children:(0,s.jsx)(r.j7,{className:"grid w-full grid-cols-3 bg-card border-2 border-border",children:c.map(e=>(0,s.jsx)(r.Xi,{value:e.value,className:"text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,e.label]})},e.value))})})}},38826:(e,a,t)=>{Promise.resolve().then(t.bind(t,90653))},42692:(e,a,t)=>{"use strict";t.d(a,{$:()=>o,F:()=>n});var s=t(60687),r=t(43210),l=t(68123),i=t(4780);let n=r.forwardRef(({className:e,children:a,...t},r)=>(0,s.jsxs)(l.bL,{ref:r,className:(0,i.cn)("relative overflow-hidden",e),...t,children:[(0,s.jsx)(l.LM,{className:"h-full w-full rounded-[inherit]",children:a}),(0,s.jsx)(o,{}),(0,s.jsx)(l.OK,{})]}));n.displayName=l.bL.displayName;let o=r.forwardRef(({className:e,orientation:a="vertical",...t},r)=>(0,s.jsx)(l.VM,{ref:r,orientation:a,className:(0,i.cn)("flex touch-none select-none transition-colors","vertical"===a&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...t,children:(0,s.jsx)(l.lr,{className:"relative flex-1 rounded-full bg-border"})}));o.displayName=l.VM.displayName},44493:(e,a,t)=>{"use strict";t.d(a,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var s=t(60687),r=t(43210),l=t(4780);let i=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",e),...a}));i.displayName="Card";let n=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-4 md:p-6",e),...a}));n.displayName="CardHeader";let o=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("h3",{ref:t,className:(0,l.cn)("text-xl font-semibold leading-none tracking-tight",e),...a}));o.displayName="CardTitle";let c=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...a}));c.displayName="CardDescription";let d=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("p-4 md:p-6 pt-0",e),...a}));d.displayName="CardContent",r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-4 md:p-6 pt-0",e),...a})).displayName="CardFooter"},63144:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx","default")},70428:(e,a,t)=>{"use strict";t.d(a,{A:()=>y});var s=t(60687),r=t(43210),l=t(78895),i=t(44493),n=t(89667),o=t(29523),c=t(8751),d=t(81950),m=t(58450),p=t(78726),u=t(91840),x=t(1305),h=t(59892),g=t(21277),f=t(9812),b=t(29867);function y(){let{crypto1Balance:e,crypto2Balance:a,stablecoinBalance:t,config:y,dispatch:j,calculateTotalPL:v,resetGlobalBalances:N}=(0,l.U)(),[S,T]=(0,r.useState)(null),{toast:w}=(0,b.dj)(),[A,C]=(0,r.useState)({crypto1:e.toString(),crypto2:a.toString(),stablecoin:t.toString()}),D=e=>e.toFixed(y.numDigits),E=s=>{T(s),C({crypto1:e.toString(),crypto2:a.toString(),stablecoin:t.toString()})},k=t=>{let s=parseFloat(A[t]);!isNaN(s)&&s>=0&&("crypto1"===t?j({type:"UPDATE_BALANCES",payload:{crypto1:s,crypto2:a}}):"crypto2"===t?j({type:"UPDATE_BALANCES",payload:{crypto1:e,crypto2:s}}):"stablecoin"===t&&j({type:"UPDATE_STABLECOIN_BALANCE",payload:s})),T(null)},U=()=>{T(null),C({crypto1:e.toString(),crypto2:a.toString(),stablecoin:t.toString()})},M=(e,a,t,r,l)=>(0,s.jsxs)(i.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ZB,{className:"text-sm font-medium text-muted-foreground",children:e}),r]}),(0,s.jsx)(i.Wu,{children:S===t?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(n.p,{type:"number",value:A[t],onChange:e=>C(a=>({...a,[t]:e.target.value})),className:"text-lg font-bold",step:"any",min:"0"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(o.$,{size:"sm",onClick:()=>k(t),className:"flex-1",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-1"}),"Save"]}),(0,s.jsxs)(o.$,{size:"sm",variant:"outline",onClick:U,className:"flex-1",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Cancel"]})]})]}):(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-foreground",children:D(a)}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Available ",l]})]}),(0,s.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>E(t),className:"ml-2",children:(0,s.jsx)(u.A,{className:"h-4 w-4"})})]})})]});return(0,s.jsxs)("div",{className:"space-y-4 mb-6",children:[(()=>{let e=v(),a="StablecoinSwap"===y.tradingMode?y.crypto2:"USD",t="StablecoinSwap"===y.tradingMode?"":"$",r=e>=0?"text-green-600":"text-red-600",l=e>=0?(0,s.jsx)(c.A,{className:"h-5 w-5 text-green-600"}):(0,s.jsx)(d.A,{className:"h-5 w-5 text-red-600"}),n=e>=0?"\uD83D\uDCC8":"\uD83D\uDCC9";return(0,s.jsxs)(i.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(i.ZB,{className:"text-sm font-medium text-muted-foreground",children:"Total Realized P/L"}),l]}),(0,s.jsx)(i.Wu,{children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:`text-2xl font-bold ${r} flex items-center gap-2`,children:[(0,s.jsx)("span",{children:n}),(0,s.jsxs)("span",{children:[t,e>=0?"+":"",e.toFixed(y.numDigits)," ",a]})]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"StablecoinSwap"===y.tradingMode?"StablecoinSwap Mode":"SimpleSpot Mode"})]})})})]})})(),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)(o.$,{size:"sm",variant:"outline",onClick:()=>{let e=N();w({title:"Global Balances Reset",description:`Balances reset to defaults: ${e.crypto1Balance} ${y.crypto1||"Crypto1"}, ${e.crypto2Balance} ${y.crypto2||"Crypto2"}, ${e.stablecoinBalance} ${y.preferredStablecoin||"Stablecoin"}`})},className:"text-orange-600 border-orange-600 hover:bg-orange-50",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Reset Global Balances"]})}),(0,s.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[M(`${y.crypto1||"Crypto 1"} Balance`,e,"crypto1",(0,s.jsx)(h.A,{className:"h-5 w-5 text-primary"}),y.crypto1||"Crypto 1"),M(`${y.crypto2||"Crypto 2"} Balance`,a,"crypto2",(0,s.jsx)(g.A,{className:"h-5 w-5 text-primary"}),y.crypto2||"Crypto 2"),M(`Stablecoin Balance (${y.preferredStablecoin||"N/A"})`,t,"stablecoin",(0,s.jsx)(f.A,{className:"h-5 w-5 text-primary"}),"Stablecoins")]})]})}},80013:(e,a,t)=>{"use strict";t.d(a,{J:()=>c});var s=t(60687),r=t(43210),l=t(78148),i=t(24224),n=t(4780);let o=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(l.b,{ref:t,className:(0,n.cn)(o(),e),...a}));c.displayName=l.b.displayName},85763:(e,a,t)=>{"use strict";t.d(a,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>n});var s=t(60687),r=t(43210),l=t(41360),i=t(4780);let n=l.bL,o=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(l.B8,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...a}));o.displayName=l.B8.displayName;let c=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(l.l9,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));c.displayName=l.l9.displayName;let d=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(l.UC,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));d.displayName=l.UC.displayName},89667:(e,a,t)=>{"use strict";t.d(a,{p:()=>i});var s=t(60687),r=t(43210),l=t(4780);let i=r.forwardRef(({className:e,type:a,...t},r)=>(0,s.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...t}));i.displayName="Input"},90653:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>eT});var s=t(60687),r=t(43210),l=t(85814),i=t.n(l),n=t(16189),o=t(8417),c=t(29523),d=t(63213),m=t(24026),p=t(33886),u=t(40196),x=t(49497),h=t(96834),g=t(44493),f=t(67857),b=t(8158),y=t(15036);function j({className:e=""}){let[a,t]=(0,r.useState)(navigator.onLine),[l,i]=(0,r.useState)(new Date);return(0,s.jsxs)("div",{className:`flex items-center gap-2 ${e}`,children:[(0,s.jsxs)(h.E,{variant:a?"default":"destructive",className:`flex items-center gap-1 ${a?"bg-green-600 hover:bg-green-600/90 text-white":""}`,children:[a?(0,s.jsx)(f.A,{className:"h-3 w-3"}):(0,s.jsx)(b.A,{className:"h-3 w-3"}),a?"Online":"Offline"]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 text-sm text-muted-foreground",children:[(0,s.jsx)(y.A,{className:"h-3 w-3"}),(0,s.jsx)("span",{children:l.toLocaleTimeString()})]})]})}function v(){let{logout:e}=(0,d.A)();(0,n.useRouter)();let a=(0,n.usePathname)(),t=[{href:"/dashboard",label:"Home",icon:(0,s.jsx)(m.A,{})},{href:"/admin",label:"Admin Panel",icon:(0,s.jsx)(p.A,{})}];return(0,s.jsxs)("header",{className:"sticky top-0 z-50 flex items-center justify-between h-16 px-4 md:px-6 bg-card border-b-2 border-border",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(o.A,{useFullName:!1}),(0,s.jsx)(j,{})]}),(0,s.jsxs)("nav",{className:"flex items-center gap-2 sm:gap-3",children:[t.map(e=>(0,s.jsx)(c.$,{variant:a===e.href?"default":"ghost",size:"sm",asChild:!0,className:`${a===e.href?"btn-neo":"hover:bg-accent/50"}`,children:(0,s.jsxs)(i(),{href:e.href,className:"flex items-center gap-2",children:[e.icon,(0,s.jsx)("span",{className:"hidden sm:inline",children:e.label})]})},e.label)),(0,s.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:()=>{window.open("/dashboard?newSession=true","_blank")},className:"hover:bg-accent/50 flex items-center gap-2",title:"Open a new independent trading session in a new tab",children:[(0,s.jsx)(u.A,{}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"New Session"})]}),(0,s.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:()=>{e()},className:"hover:bg-destructive/80 hover:text-destructive-foreground flex items-center gap-2",children:[(0,s.jsx)(x.A,{}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Logout"})]})]})]})}var N=t(78895),S=t(76663),T=t(89667),w=t(80013),A=t(40211),C=t(58450),D=t(4780);let E=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(A.bL,{ref:t,className:(0,D.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...a,children:(0,s.jsx)(A.C1,{className:(0,D.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(C.A,{className:"h-4 w-4"})})}));E.displayName=A.bL.displayName;var k=t(15079),U=t(42692),M=t(35950),O=t(26134),B=t(78726);let R=O.bL;O.l9;let P=O.ZL,L=O.bm,_=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(O.hJ,{ref:t,className:(0,D.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));_.displayName=O.hJ.displayName;let I=r.forwardRef(({className:e,children:a,...t},r)=>(0,s.jsxs)(P,{children:[(0,s.jsx)(_,{}),(0,s.jsxs)(O.UC,{ref:r,className:(0,D.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[a,(0,s.jsxs)(O.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(B.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));I.displayName=O.UC.displayName;let F=({className:e,...a})=>(0,s.jsx)("div",{className:(0,D.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});F.displayName="DialogHeader";let $=({className:e,...a})=>(0,s.jsx)("div",{className:(0,D.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});$.displayName="DialogFooter";let G=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(O.hE,{ref:t,className:(0,D.cn)("text-lg font-semibold leading-none tracking-tight",e),...a}));G.displayName=O.hE.displayName;let V=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)(O.VY,{ref:t,className:(0,D.cn)("text-sm text-muted-foreground",e),...a}));V.displayName=O.VY.displayName;let z=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("textarea",{className:(0,D.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...a}));z.displayName="Textarea";var H=t(85763),J=t(29867);function Z({isOpen:e,onClose:a,onSetTargetPrices:t}){let l;let[i,n]=(0,r.useState)("manual"),[o,d]=(0,r.useState)(""),{toast:m}=(0,J.dj)();try{l=(0,N.U)()}catch(e){console.warn("Trading context not available:",e),l=null}let[p,u]=(0,r.useState)("8"),[x,h]=(0,r.useState)("5"),[g,f]=(0,r.useState)("even"),b=l?.currentMarketPrice||l?.state?.currentMarketPrice||1e5,y=l?.config?.slippagePercent||l?.state?.config?.slippagePercent||.2,j=()=>{let e=parseInt(p),a=parseFloat(x);if(!e||e<2||e>20||!a||a<=0)return[];let t=[],s=b*(1-a/100),r=b*(1+a/100);if("even"===g)for(let a=0;a<e;a++){let l=s+a/(e-1)*(r-s);t.push(Math.round(l))}else if("fibonacci"===g){let a=[0,.236,.382,.5,.618,.764,.854,.927,1];for(let l=0;l<e;l++){let i=s+(r-s)*(a[Math.min(l,a.length-1)]||l/(e-1));t.push(Math.round(i))}}else if("exponential"===g)for(let a=0;a<e;a++){let l=s+(r-s)*Math.pow(a/(e-1),1.5);t.push(Math.round(l))}let l=3*y/100*b,i=t.sort((e,a)=>e-a),n=[];for(let e=0;e<i.length;e++){let a=i[e];if(n.length>0){let e=n[n.length-1];a-e<l&&(a=e+l)}n.push(Math.round(a))}return n},v=()=>{let e=o.split("\n").filter(e=>""!==e.trim()).map(e=>parseFloat(e.trim())).filter(e=>!isNaN(e)&&e>0).sort((e,a)=>e-a);if(e.length<2)return{hasOverlap:!1,message:""};let a=y/100*b;for(let t=0;t<e.length-1;t++)if(e[t]+a>=e[t+1]-a){let s=2*a,r=e[t+1]-e[t];return{hasOverlap:!0,message:`Overlap detected between ${e[t]} and ${e[t+1]}. Minimum gap needed: ${s.toFixed(0)}, actual gap: ${r.toFixed(0)}`}}return{hasOverlap:!1,message:"No slippage zone overlaps detected ✓"}},S=v();return(0,s.jsx)(R,{open:e,onOpenChange:a,children:(0,s.jsxs)(I,{className:"sm:max-w-2xl bg-card border-2 border-border",children:[(0,s.jsxs)(F,{children:[(0,s.jsx)(G,{className:"text-primary",children:"Set Target Prices"}),(0,s.jsx)(V,{children:"Set target prices manually or generate them automatically with optimal spacing to avoid slippage zone overlaps."})]}),(0,s.jsxs)(H.tU,{value:i,onValueChange:n,className:"w-full",children:[(0,s.jsxs)(H.j7,{className:"grid w-full grid-cols-2",children:[(0,s.jsx)(H.Xi,{value:"manual",children:"Manual Entry"}),(0,s.jsx)(H.Xi,{value:"automatic",children:"Automatic Generation"})]}),(0,s.jsx)(H.av,{value:"manual",className:"space-y-4",children:(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(w.J,{htmlFor:"target-prices-input",className:"text-left",children:"Target Prices"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Paste target prices from Excel or enter manually, one price per line. Invalid entries will be ignored."}),(0,s.jsx)(z,{id:"target-prices-input",value:o,onChange:e=>d(e.target.value),placeholder:"50000 50500 49800",className:"min-h-[200px] bg-input border-2 border-border focus:border-primary font-mono"}),S.message&&(0,s.jsx)("p",{className:`text-sm ${S.hasOverlap?"text-red-500":"text-green-500"}`,children:S.message})]})}),(0,s.jsxs)(H.av,{value:"automatic",className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:"targetCount",children:"Number of Targets"}),(0,s.jsxs)(k.l6,{value:p,onValueChange:u,children:[(0,s.jsx)(k.bq,{children:(0,s.jsx)(k.yv,{})}),(0,s.jsx)(k.gC,{children:[4,6,8,10,12,15,20].map(e=>(0,s.jsxs)(k.eb,{value:e.toString(),children:[e," targets"]},e))})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:"priceRange",children:"Price Range (%)"}),(0,s.jsxs)(k.l6,{value:x,onValueChange:h,children:[(0,s.jsx)(k.bq,{children:(0,s.jsx)(k.yv,{})}),(0,s.jsx)(k.gC,{children:[2,2.5,3,3.5,4,4.5,5,6,7,8,10].map(e=>(0,s.jsxs)(k.eb,{value:e.toString(),children:["\xb1",e,"%"]},e))})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:"distribution",children:"Distribution Pattern"}),(0,s.jsxs)(k.l6,{value:g,onValueChange:f,children:[(0,s.jsx)(k.bq,{children:(0,s.jsx)(k.yv,{})}),(0,s.jsxs)(k.gC,{children:[(0,s.jsx)(k.eb,{value:"even",children:"Even Distribution"}),(0,s.jsx)(k.eb,{value:"fibonacci",children:"Fibonacci (More near current price)"}),(0,s.jsx)(k.eb,{value:"exponential",children:"Exponential (Wider spread)"})]})]})]}),(0,s.jsx)("div",{className:"bg-muted p-3 rounded-md",children:(0,s.jsxs)("p",{className:"text-sm",children:[(0,s.jsx)("strong",{children:"Current Market Price:"})," $",b.toLocaleString(),(0,s.jsx)("br",{}),(0,s.jsx)("strong",{children:"Slippage:"})," \xb1",y,"% ($",(b*y/100).toFixed(0),")",(0,s.jsx)("br",{}),(0,s.jsx)("strong",{children:"Range:"})," $",(b*(1-parseFloat(x)/100)).toLocaleString()," - $",(b*(1+parseFloat(x)/100)).toLocaleString()]})}),(0,s.jsxs)(c.$,{onClick:()=>{d(j().join("\n"))},className:"w-full btn-neo",children:["Generate ",p," Target Prices"]}),(0,s.jsxs)("div",{className:"grid gap-2",children:[(0,s.jsx)(w.J,{children:"Generated Prices (Preview)"}),(0,s.jsx)(z,{value:o,onChange:e=>d(e.target.value),className:"min-h-[150px] bg-input border-2 border-border focus:border-primary font-mono",placeholder:"Click 'Generate' to create automatic target prices..."}),S.message&&(0,s.jsx)("p",{className:`text-sm ${S.hasOverlap?"text-red-500":"text-green-500"}`,children:S.message})]})]})]}),(0,s.jsxs)($,{children:[(0,s.jsx)(L,{asChild:!0,children:(0,s.jsx)(c.$,{type:"button",variant:"outline",className:"btn-outline-neo",children:"Cancel"})}),(0,s.jsx)(c.$,{type:"button",onClick:()=>{let e=o.split("\n").map(e=>e.trim()).filter(e=>""!==e),s=e.map(e=>parseFloat(e)).filter(e=>!isNaN(e)&&e>0);if(0===s.length&&e.length>0){m({title:"Invalid Input",description:"No valid prices found. Please enter numbers, one per line.",variant:"destructive"});return}let r=v();if(r.hasOverlap){m({title:"Slippage Zone Overlap",description:r.message,variant:"destructive"});return}t(s),m({title:"Target Prices Updated",description:`${s.length} target prices have been set.`}),d(""),a()},disabled:S.hasOverlap,className:"btn-neo",children:"Save Prices"})]})]})})}var W=t(55280),X=t(72963);function Y({label:e,value:a,allowedCryptos:t,onValidCrypto:l,placeholder:i="Enter crypto symbol",description:n,className:o}){let[d,m]=(0,r.useState)(""),[p,u]=(0,r.useState)("idle"),[x,h]=(0,r.useState)(""),[g,f]=(0,r.useState)(!1),b=()=>{let e=d.toUpperCase().trim();if(!e){u("invalid"),h("Please enter a crypto symbol");return}if(!t||!Array.isArray(t)){u("invalid"),h("No allowed cryptocurrencies configured");return}t.includes(e)?(u("valid"),h(""),f(!0),l(e)):(u("invalid"),h(`${e} is not available. Allowed: ${t.join(", ")}`))};return(0,s.jsxs)("div",{className:(0,D.cn)("space-y-2",o),children:[(0,s.jsx)(w.J,{htmlFor:`crypto-input-${e}`,children:e}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[(0,s.jsx)(T.p,{id:`crypto-input-${e}`,value:d||a,onChange:e=>{m(e.target.value),u("idle"),h("")},onKeyPress:e=>{"Enter"===e.key&&b()},placeholder:i,className:(0,D.cn)("pr-8",(()=>{switch(p){case"valid":return"border-green-500";case"invalid":return"border-red-500";default:return""}})())}),"idle"!==p&&(0,s.jsx)("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:(()=>{switch(p){case"valid":return(0,s.jsx)(C.A,{className:"h-4 w-4 text-green-500"});case"invalid":return(0,s.jsx)(B.A,{className:"h-4 w-4 text-red-500"});default:return null}})()})]}),(0,s.jsx)(c.$,{onClick:b,variant:"outline",className:"btn-neo",disabled:!d.trim(),children:"Check"})]}),a&&g&&(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,s.jsx)(C.A,{className:"h-4 w-4 text-green-500"}),(0,s.jsxs)("span",{className:"text-green-600 font-medium",children:["Selected: ",a]})]}),"invalid"===p&&x&&(0,s.jsxs)("div",{className:"flex items-start space-x-2 text-sm text-red-600",children:[(0,s.jsx)(X.A,{className:"h-4 w-4 mt-0.5 flex-shrink-0"}),(0,s.jsx)("span",{children:x})]}),n&&(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:n}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[t&&Array.isArray(t)?t.length:0," cryptocurrencies available"]})]})}var q=t(24851);let K=r.forwardRef(({className:e,...a},t)=>(0,s.jsxs)(q.bL,{ref:t,className:(0,D.cn)("relative flex w-full touch-none select-none items-center",e),...a,children:[(0,s.jsx)(q.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,s.jsx)(q.Q6,{className:"absolute h-full bg-primary"})}),(0,s.jsx)(q.zi,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));K.displayName=q.bL.displayName;var Q=t(58369),ee=t(1833),ea=t(19422);let et=[{value:"G_hades_curse.wav",label:"Hades Curse"},{value:"G_hades_demat.wav",label:"Hades Demat"},{value:"G_hades_mat.wav",label:"Hades Mat"},{value:"G_hades_sanctify.wav",label:"Hades Sanctify"},{value:"S_mon1.mp3",label:"Monster 1"},{value:"S_mon2.mp3",label:"Monster 2"},{value:"Satyr_atk4.wav",label:"Satyr Attack"},{value:"bells.wav",label:"Bells"},{value:"bird1.wav",label:"Bird 1"},{value:"bird7.wav",label:"Bird 7"},{value:"cheer.wav",label:"Cheer"},{value:"chest1.wav",label:"Chest"},{value:"chime2.wav",label:"Chime"},{value:"dark2.wav",label:"Dark"},{value:"foundry2.wav",label:"Foundry"},{value:"goatherd1.wav",label:"Goatherd"},{value:"marble1.wav",label:"Marble"},{value:"sanctuary1.wav",label:"Sanctuary"},{value:"space_bells4a.wav",label:"Space Bells"},{value:"sparrow1.wav",label:"Sparrow"},{value:"tax3.wav",label:"Tax"},{value:"wolf4.wav",label:"Wolf"}];function es({isOpen:e,onClose:a}){let{sessionAlarmConfig:t,dispatch:l,playSessionAlarm:i}=(0,N.U)(),{toast:n}=(0,J.dj)(),[o,d]=(0,r.useState)(t),m=e=>{try{let a=new Audio(`/ringtones/${e}`);a.volume=o.volume/100,a.play().catch(console.error)}catch(e){console.error("Failed to play test sound:",e),n({title:"Sound Test Failed",description:"Could not play the selected sound file.",variant:"destructive"})}};return(0,s.jsx)(R,{open:e,onOpenChange:a,children:(0,s.jsxs)(I,{className:"sm:max-w-[500px] max-h-[80vh]",children:[(0,s.jsxs)(F,{children:[(0,s.jsxs)(G,{className:"flex items-center gap-2",children:[(0,s.jsx)(Q.A,{className:"h-5 w-5"}),"Session Alarm Settings"]}),(0,s.jsx)(V,{children:"Configure custom alarm sounds and settings for this trading session."})]}),(0,s.jsxs)("div",{className:"space-y-6 overflow-y-auto max-h-[60vh] pr-2",children:[(0,s.jsxs)(g.Zp,{children:[(0,s.jsx)(g.aR,{children:(0,s.jsxs)(g.ZB,{className:"text-sm flex items-center gap-2",children:[(0,s.jsx)(ee.A,{className:"h-4 w-4"}),"Volume Control"]})}),(0,s.jsx)(g.Wu,{children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(w.J,{children:["Volume: ",o.volume,"%"]}),(0,s.jsx)(K,{value:[o.volume],onValueChange:e=>d({...o,volume:e[0]}),max:100,min:0,step:5,className:"w-full"})]})})]}),(0,s.jsxs)(g.Zp,{children:[(0,s.jsx)(g.aR,{children:(0,s.jsx)(g.ZB,{className:"text-sm text-green-600",children:"Order Execution Success Alarms"})}),(0,s.jsxs)(g.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(E,{id:"buyAlarmEnabled",checked:o.buyAlarmEnabled,onCheckedChange:e=>d({...o,buyAlarmEnabled:e})}),(0,s.jsx)(w.J,{htmlFor:"buyAlarmEnabled",children:"Enable alerts on successful order execution"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(w.J,{children:"Success Alarm Sound"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(k.l6,{value:o.buyAlarmSound,onValueChange:e=>d({...o,buyAlarmSound:e}),children:[(0,s.jsx)(k.bq,{className:"flex-1",children:(0,s.jsx)(k.yv,{})}),(0,s.jsx)(k.gC,{className:"max-h-[200px] overflow-y-auto",children:et.map(e=>(0,s.jsx)(k.eb,{value:e.value,children:e.label},e.value))})]}),(0,s.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>m(o.buyAlarmSound),disabled:!o.buyAlarmEnabled,children:(0,s.jsx)(ea.A,{className:"h-4 w-4"})})]})]})]})]}),(0,s.jsxs)(g.Zp,{children:[(0,s.jsx)(g.aR,{children:(0,s.jsx)(g.ZB,{className:"text-sm text-red-600",children:"Error/Failure Alarms"})}),(0,s.jsxs)(g.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(E,{id:"sellAlarmEnabled",checked:o.sellAlarmEnabled,onCheckedChange:e=>d({...o,sellAlarmEnabled:e})}),(0,s.jsx)(w.J,{htmlFor:"sellAlarmEnabled",children:"Enable alerts on errors/failures"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(w.J,{children:"Error Alarm Sound"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(k.l6,{value:o.sellAlarmSound,onValueChange:e=>d({...o,sellAlarmSound:e}),children:[(0,s.jsx)(k.bq,{className:"flex-1",children:(0,s.jsx)(k.yv,{})}),(0,s.jsx)(k.gC,{className:"max-h-[200px] overflow-y-auto",children:et.map(e=>(0,s.jsx)(k.eb,{value:e.value,children:e.label},e.value))})]}),(0,s.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>m(o.sellAlarmSound),disabled:!o.sellAlarmEnabled,children:(0,s.jsx)(ea.A,{className:"h-4 w-4"})})]})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,s.jsx)(c.$,{variant:"outline",onClick:a,children:"Cancel"}),(0,s.jsx)(c.$,{onClick:()=>{l({type:"SET_SESSION_ALARM_CONFIG",payload:o}),n({title:"Alarm Settings Saved",description:"Your session-specific alarm settings have been updated.",duration:3e3}),a()},className:"btn-neo",children:"Save Settings"})]})]})]})})}var er=t(18329),el=t(85866);async function ei(e,a,t,s){try{if(!t.enabled||!t.botToken||!t.chatId)return console.warn("Telegram notifications disabled or not configured"),!1;let r={error:"\uD83D\uDEA8",success:"✅",info:"ℹ️",warning:"⚠️",trade:"\uD83D\uDCB0",connection:"\uD83C\uDF10"}[a]||"ℹ️",l=function(e,a,t,s){let r=new Date().toLocaleString(),l=`${t} <b>PLUTO TRADING BOT</b>

${e}

⏰ ${r}`;return s&&Object.keys(s).length>0&&(l+="\n\n<i>Additional Info:</i>",Object.entries(s).forEach(([e,a])=>{l+=`
• ${e}: ${a}`})),l}(e,0,r,s),i=await fetch(`https://api.telegram.org/bot${t.botToken}/sendMessage`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:t.chatId,text:l,parse_mode:"HTML",disable_web_page_preview:!0})});if(!i.ok)throw Error(`Telegram API error: ${i.status} ${i.statusText}`);return console.log(`📱 Telegram notification sent: ${a}`),!0}catch(e){return console.error("Failed to send Telegram notification:",e),!1}}async function en(e){return await ei("Telegram notifications are working correctly! \uD83C\uDF89","success",e,{test:!0,timestamp:new Date().toISOString()})}function eo(e){let a=[];return e.enabled?(e.botToken?e.botToken.match(/^\d+:[A-Za-z0-9_-]+$/)||a.push("Invalid bot token format"):a.push("Bot token is required"),e.chatId?e.chatId.match(/^-?\d+$/)||a.push("Invalid chat ID format"):a.push("Chat ID is required"),{valid:0===a.length,errors:a}):{valid:!0,errors:[]}}function ec({isOpen:e,onClose:a}){let{telegramConfig:t,dispatch:l}=(0,N.U)(),{toast:i}=(0,J.dj)(),[n,o]=(0,r.useState)(t),[d,m]=(0,r.useState)(!1),p=async()=>{let e=eo(n);if(!e.valid){i({title:"Configuration Error",description:e.errors.join(", "),variant:"destructive"});return}m(!0);try{await en(n)?i({title:"Test Successful",description:"Test message sent successfully! Check your Telegram chat.",duration:5e3}):i({title:"Test Failed",description:"Could not send test message. Please check your configuration.",variant:"destructive"})}catch(e){i({title:"Test Failed",description:"An error occurred while testing the connection.",variant:"destructive"})}finally{m(!1)}};return(0,s.jsx)(R,{open:e,onOpenChange:a,children:(0,s.jsxs)(I,{className:"sm:max-w-[600px] max-h-[90vh] flex flex-col",children:[(0,s.jsxs)(F,{className:"flex-shrink-0",children:[(0,s.jsxs)(G,{className:"flex items-center gap-2",children:[(0,s.jsx)(er.A,{className:"h-5 w-5"}),"Telegram Notifications"]}),(0,s.jsx)(V,{children:"Configure Telegram notifications for trading alerts, errors, and system events."})]}),(0,s.jsxs)("div",{className:"space-y-6 overflow-y-auto flex-1 pr-2",children:[(0,s.jsxs)(g.Zp,{children:[(0,s.jsx)(g.aR,{children:(0,s.jsx)(g.ZB,{className:"text-sm",children:"Notification Settings"})}),(0,s.jsx)(g.Wu,{children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(E,{id:"telegramEnabled",checked:n.enabled,onCheckedChange:e=>o({...n,enabled:e})}),(0,s.jsx)(w.J,{htmlFor:"telegramEnabled",children:"Enable Telegram notifications"})]})})]}),n.enabled&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(g.Zp,{children:[(0,s.jsxs)(g.aR,{children:[(0,s.jsx)(g.ZB,{className:"text-sm",children:"Bot Configuration"}),(0,s.jsx)(g.BT,{children:"Create a Telegram bot via @BotFather and get your bot token."})]}),(0,s.jsxs)(g.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(w.J,{htmlFor:"botToken",children:"Bot Token"}),(0,s.jsx)(T.p,{id:"botToken",type:"password",value:n.botToken,onChange:e=>o({...n,botToken:e.target.value}),placeholder:"123456789:ABCdefGHIjklMNOpqrsTUVwxyz"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Format: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(w.J,{htmlFor:"chatId",children:"Chat ID"}),(0,s.jsx)(T.p,{id:"chatId",value:n.chatId,onChange:e=>o({...n,chatId:e.target.value}),placeholder:"-1001234567890 or 123456789"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Your personal chat ID or group chat ID (starts with -)"})]})]})]}),(0,s.jsxs)(g.Zp,{children:[(0,s.jsx)(g.aR,{children:(0,s.jsx)(g.ZB,{className:"text-sm",children:"Setup Instructions"})}),(0,s.jsx)(g.Wu,{children:(0,s.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)("span",{className:"font-medium text-primary",children:"1."}),(0,s.jsx)("span",{children:"Message @BotFather on Telegram and create a new bot with /newbot"})]}),(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)("span",{className:"font-medium text-primary",children:"2."}),(0,s.jsx)("span",{children:"Copy the bot token and paste it above"})]}),(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)("span",{className:"font-medium text-primary",children:"3."}),(0,s.jsx)("span",{children:"Message @userinfobot to get your chat ID"})]}),(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)("span",{className:"font-medium text-primary",children:"4."}),(0,s.jsx)("span",{children:"Start a conversation with your bot first"})]}),(0,s.jsxs)("div",{className:"flex items-start gap-2",children:[(0,s.jsx)("span",{className:"font-medium text-primary",children:"5."}),(0,s.jsx)("span",{children:"Test the connection using the button below"})]})]})})]}),(0,s.jsxs)(g.Zp,{children:[(0,s.jsx)(g.aR,{children:(0,s.jsx)(g.ZB,{className:"text-sm",children:"Test Connection"})}),(0,s.jsx)(g.Wu,{children:(0,s.jsx)(c.$,{onClick:p,disabled:d||!n.botToken||!n.chatId,className:"w-full",variant:"outline",children:d?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"}),"Sending Test Message..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(el.A,{className:"h-4 w-4 mr-2"}),"Send Test Message"]})})})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t border-border flex-shrink-0",children:[(0,s.jsx)(c.$,{variant:"outline",onClick:a,children:"Cancel"}),(0,s.jsx)(c.$,{onClick:()=>{let e=eo(n);if(!e.valid){i({title:"Configuration Error",description:e.errors.join(", "),variant:"destructive"});return}l({type:"SET_TELEGRAM_CONFIG",payload:n}),i({title:"Telegram Settings Saved",description:"Your Telegram notification settings have been updated.",duration:3e3}),a()},className:"btn-neo",children:"Save Settings"})]})]})})}var ed=t(8751),em=t(41936),ep=t(99196);let eu=[{id:"BTC_USDT",crypto1:"BTC",crypto2:"USDT",displayName:"Bitcoin / Tether",category:"major",minTradeAmount:1e-4,maxTradeAmount:10,priceDecimals:2,amountDecimals:6,description:"The most popular cryptocurrency pair",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"ETH_USDT",crypto1:"ETH",crypto2:"USDT",displayName:"Ethereum / Tether",category:"major",minTradeAmount:.001,maxTradeAmount:100,priceDecimals:2,amountDecimals:6,description:"Second largest cryptocurrency by market cap",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"BNB_USDT",crypto1:"BNB",crypto2:"USDT",displayName:"Binance Coin / Tether",category:"major",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Binance exchange native token",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"ADA_USDT",crypto1:"ADA",crypto2:"USDT",displayName:"Cardano / Tether",category:"major",minTradeAmount:1,maxTradeAmount:1e5,priceDecimals:4,amountDecimals:2,description:"Proof-of-stake blockchain platform",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"SOL_USDT",crypto1:"SOL",crypto2:"USDT",displayName:"Solana / Tether",category:"major",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"High-performance blockchain for DeFi and NFTs",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"DOT_USDT",crypto1:"DOT",crypto2:"USDT",displayName:"Polkadot / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:1e4,priceDecimals:3,amountDecimals:3,description:"Multi-chain interoperability protocol",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"LINK_USDT",crypto1:"LINK",crypto2:"USDT",displayName:"Chainlink / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:5e3,priceDecimals:3,amountDecimals:3,description:"Decentralized oracle network",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"MATIC_USDT",crypto1:"MATIC",crypto2:"USDT",displayName:"Polygon / Tether",category:"altcoin",minTradeAmount:1,maxTradeAmount:5e4,priceDecimals:4,amountDecimals:2,description:"Ethereum scaling solution",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"AVAX_USDT",crypto1:"AVAX",crypto2:"USDT",displayName:"Avalanche / Tether",category:"altcoin",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Fast and eco-friendly blockchain platform",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"ATOM_USDT",crypto1:"ATOM",crypto2:"USDT",displayName:"Cosmos / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:5e3,priceDecimals:3,amountDecimals:3,description:"Internet of blockchains",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"UNI_USDT",crypto1:"UNI",crypto2:"USDT",displayName:"Uniswap / Tether",category:"defi",minTradeAmount:.1,maxTradeAmount:5e3,priceDecimals:3,amountDecimals:3,description:"Leading decentralized exchange token",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"AAVE_USDT",crypto1:"AAVE",crypto2:"USDT",displayName:"Aave / Tether",category:"defi",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Decentralized lending protocol",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"COMP_USDT",crypto1:"COMP",crypto2:"USDT",displayName:"Compound / Tether",category:"defi",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Algorithmic money market protocol",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"SUSHI_USDT",crypto1:"SUSHI",crypto2:"USDT",displayName:"SushiSwap / Tether",category:"defi",minTradeAmount:.1,maxTradeAmount:1e4,priceDecimals:4,amountDecimals:3,description:"Community-driven DEX and DeFi platform",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"CRV_USDT",crypto1:"CRV",crypto2:"USDT",displayName:"Curve DAO / Tether",category:"defi",minTradeAmount:1,maxTradeAmount:5e4,priceDecimals:4,amountDecimals:2,description:"Decentralized exchange for stablecoins",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"DOGE_USDT",crypto1:"DOGE",crypto2:"USDT",displayName:"Dogecoin / Tether",category:"meme",minTradeAmount:10,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:0,description:"The original meme cryptocurrency",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"SHIB_USDT",crypto1:"SHIB",crypto2:"USDT",displayName:"Shiba Inu / Tether",category:"meme",minTradeAmount:1e5,maxTradeAmount:1e8,priceDecimals:8,amountDecimals:0,description:"Dogecoin killer meme token",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"USDC_USDT",crypto1:"USDC",crypto2:"USDT",displayName:"USD Coin / Tether",category:"stablecoin",minTradeAmount:1,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:2,description:"Stablecoin arbitrage pair",supportedModes:["StablecoinSwap"]},{id:"DAI_USDT",crypto1:"DAI",crypto2:"USDT",displayName:"Dai / Tether",category:"stablecoin",minTradeAmount:1,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:2,description:"Decentralized stablecoin pair",supportedModes:["StablecoinSwap"]},{id:"BTC_ETH",crypto1:"BTC",crypto2:"ETH",displayName:"Bitcoin / Ethereum",category:"major",minTradeAmount:1e-4,maxTradeAmount:10,priceDecimals:4,amountDecimals:6,description:"Top two cryptocurrencies pair",supportedModes:["StablecoinSwap"]},{id:"ETH_BNB",crypto1:"ETH",crypto2:"BNB",displayName:"Ethereum / Binance Coin",category:"major",minTradeAmount:.001,maxTradeAmount:100,priceDecimals:4,amountDecimals:6,description:"Ethereum vs Binance ecosystem",supportedModes:["StablecoinSwap"]},{id:"XRP_USDT",crypto1:"XRP",crypto2:"USDT",displayName:"Ripple / Tether",category:"major",minTradeAmount:1,maxTradeAmount:1e5,priceDecimals:4,amountDecimals:2,description:"Cross-border payment cryptocurrency",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"LTC_USDT",crypto1:"LTC",crypto2:"USDT",displayName:"Litecoin / Tether",category:"major",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:4,description:"Silver to Bitcoin's gold",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"BCH_USDT",crypto1:"BCH",crypto2:"USDT",displayName:"Bitcoin Cash / Tether",category:"major",minTradeAmount:.001,maxTradeAmount:1e3,priceDecimals:2,amountDecimals:5,description:"Bitcoin fork with larger blocks",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"TRX_USDT",crypto1:"TRX",crypto2:"USDT",displayName:"TRON / Tether",category:"altcoin",minTradeAmount:10,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:0,description:"Decentralized entertainment ecosystem",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"EOS_USDT",crypto1:"EOS",crypto2:"USDT",displayName:"EOS / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:1e4,priceDecimals:4,amountDecimals:3,description:"Delegated proof-of-stake blockchain",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"XLM_USDT",crypto1:"XLM",crypto2:"USDT",displayName:"Stellar / Tether",category:"altcoin",minTradeAmount:1,maxTradeAmount:1e5,priceDecimals:5,amountDecimals:2,description:"Fast and low-cost cross-border payments",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"VET_USDT",crypto1:"VET",crypto2:"USDT",displayName:"VeChain / Tether",category:"altcoin",minTradeAmount:10,maxTradeAmount:1e6,priceDecimals:6,amountDecimals:0,description:"Supply chain management blockchain",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"FIL_USDT",crypto1:"FIL",crypto2:"USDT",displayName:"Filecoin / Tether",category:"altcoin",minTradeAmount:.01,maxTradeAmount:1e3,priceDecimals:3,amountDecimals:4,description:"Decentralized storage network",supportedModes:["SimpleSpot","StablecoinSwap"]},{id:"THETA_USDT",crypto1:"THETA",crypto2:"USDT",displayName:"Theta Network / Tether",category:"altcoin",minTradeAmount:.1,maxTradeAmount:1e4,priceDecimals:4,amountDecimals:3,description:"Decentralized video streaming network",supportedModes:["SimpleSpot","StablecoinSwap"]}],ex=[{value:"major",label:"Major Cryptocurrencies",description:"Top market cap cryptocurrencies"},{value:"altcoin",label:"Altcoins",description:"Alternative cryptocurrencies"},{value:"defi",label:"DeFi Tokens",description:"Decentralized finance tokens"},{value:"meme",label:"Meme Coins",description:"Community-driven meme tokens"},{value:"stablecoin",label:"Stablecoins",description:"Price-stable cryptocurrencies"}];function eh({isOpen:e,onClose:a}){let{config:t,dispatch:l}=(0,N.U)(),{toast:i}=(0,J.dj)(),[n,o]=(0,r.useState)(""),[d,m]=(0,r.useState)("all"),[p,u]=(0,r.useState)(null),x=(0,r.useMemo)(()=>{var e;let a=(e=t.tradingMode||"SimpleSpot",eu.filter(a=>a.supportedModes.includes(e)));return"all"!==d&&(a=a.filter(e=>e.category===d)),n.trim()&&(a=(function(e){let a=e.toLowerCase();return eu.filter(e=>e.crypto1.toLowerCase().includes(a)||e.crypto2.toLowerCase().includes(a)||e.displayName.toLowerCase().includes(a)||e.description.toLowerCase().includes(a))})(n).filter(e=>e.supportedModes.includes(t.tradingMode||"SimpleSpot"))),a},[t.tradingMode,d,n]),f=e=>{u(e)},b=e=>({major:"bg-blue-100 text-blue-800",altcoin:"bg-green-100 text-green-800",defi:"bg-purple-100 text-purple-800",meme:"bg-orange-100 text-orange-800",stablecoin:"bg-gray-100 text-gray-800"})[e]||"bg-gray-100 text-gray-800";return(0,s.jsx)(R,{open:e,onOpenChange:a,children:(0,s.jsxs)(I,{className:"sm:max-w-[700px] max-h-[80vh]",children:[(0,s.jsxs)(F,{children:[(0,s.jsxs)(G,{className:"flex items-center gap-2",children:[(0,s.jsx)(ed.A,{className:"h-5 w-5"}),"Select Trading Pair"]}),(0,s.jsxs)(V,{children:["Choose from 50+ supported trading pairs for ",t.tradingMode||"SimpleSpot"," mode."]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)(w.J,{htmlFor:"search",children:"Search Pairs"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(em.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(T.p,{id:"search",placeholder:"Search by symbol or name...",value:n,onChange:e=>o(e.target.value),className:"pl-10"})]})]}),(0,s.jsxs)("div",{className:"w-48",children:[(0,s.jsx)(w.J,{htmlFor:"category",children:"Category"}),(0,s.jsxs)(k.l6,{value:d,onValueChange:m,children:[(0,s.jsx)(k.bq,{id:"category",children:(0,s.jsx)(k.yv,{})}),(0,s.jsxs)(k.gC,{children:[(0,s.jsx)(k.eb,{value:"all",children:"All Categories"}),ex.map(e=>(0,s.jsx)(k.eb,{value:e.value,children:e.label},e.value))]})]})]})]}),(0,s.jsx)(U.F,{className:"h-[400px] border rounded-lg",children:(0,s.jsx)("div",{className:"p-4 space-y-2",children:0===x.length?(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,s.jsx)(ed.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{children:"No trading pairs found matching your criteria."})]}):x.map(e=>(0,s.jsx)(g.Zp,{className:`cursor-pointer transition-all hover:shadow-md ${p?.id===e.id?"ring-2 ring-primary":""}`,onClick:()=>f(e),children:(0,s.jsx)(g.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,s.jsxs)("h3",{className:"font-semibold text-lg",children:[e.crypto1,"/",e.crypto2]}),(0,s.jsx)(h.E,{className:b(e.category),children:e.category})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:e.description}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-xs text-muted-foreground",children:[(0,s.jsxs)("span",{children:["Min: ",e.minTradeAmount," ",e.crypto1]}),(0,s.jsxs)("span",{children:["Max: ",e.maxTradeAmount," ",e.crypto1]}),(0,s.jsxs)("span",{children:["Decimals: ",e.priceDecimals]})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("div",{className:"text-sm font-medium",children:e.displayName}),(0,s.jsx)("div",{className:"flex gap-1 mt-1",children:e.supportedModes.map(e=>(0,s.jsx)(h.E,{variant:"outline",className:"text-xs",children:e},e))})]})]})})},e.id))})}),p&&(0,s.jsxs)(g.Zp,{className:"bg-muted/50",children:[(0,s.jsx)(g.aR,{children:(0,s.jsxs)(g.ZB,{className:"text-sm flex items-center gap-2",children:[(0,s.jsx)(ep.A,{className:"h-4 w-4"}),"Selected Pair Details"]})}),(0,s.jsx)(g.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Pair:"})," ",p.displayName]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Category:"})," ",p.category]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Min Trade:"})," ",p.minTradeAmount," ",p.crypto1]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Max Trade:"})," ",p.maxTradeAmount," ",p.crypto1]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Price Decimals:"})," ",p.priceDecimals]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Amount Decimals:"})," ",p.amountDecimals]})]})})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,s.jsx)(c.$,{variant:"outline",onClick:a,children:"Cancel"}),(0,s.jsx)(c.$,{onClick:()=>{if(!p){i({title:"No Pair Selected",description:"Please select a trading pair first.",variant:"destructive"});return}l({type:"SET_CONFIG",payload:{crypto1:p.crypto1,crypto2:p.crypto2}}),i({title:"Trading Pair Updated",description:`Now trading ${p.displayName}`,duration:3e3}),a()},disabled:!p,className:"btn-neo",children:"Apply Trading Pair"})]})]})]})})}var eg=t(76485),ef=t(11516),eb=t(92375),ey=t(25371),ej=t(1305);let ev=["USDT","USDC","BTC"],eN=["USDC","DAI","TUSD","FDUSD","USDT","EUR"];function eS(){let e,a;try{e=(0,N.U)()}catch(e){return console.error("Trading context not available:",e),(0,s.jsx)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col",children:(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsx)("p",{className:"text-red-500",children:"Trading context not available. Please refresh the page."})})})}let{config:t,dispatch:l,botSystemStatus:i,appSettings:n,setTargetPrices:o}=e,d="Running"===i,m="WarmingUp"===i;try{a=(0,S.f)()}catch(e){console.warn("AI context not available:",e),a={suggestion:null,isLoading:!1,error:null,getTradingModeSuggestion:()=>Promise.resolve()}}let{suggestion:p,isLoading:u,error:x,getTradingModeSuggestion:h}=a,{toast:f}=(0,J.dj)(),[b,y]=(0,r.useState)(!1),[j,v]=(0,r.useState)(!1),[A,C]=(0,r.useState)(!1),[O,B]=(0,r.useState)(!1),[R,P]=(0,r.useState)(!1),[L,_]=(0,r.useState)("medium"),[I,F]=(0,r.useState)(""),[$,G]=(0,r.useState)(""),V=e=>{let a;let{name:t,value:s,type:r,checked:i}=e.target;if("checkbox"===r)a=i;else if("number"===r){if(""===s||null==s)a=0;else{let e=parseFloat(s);a=isNaN(e)?0:e}}else a=s;l({type:"SET_CONFIG",payload:{[t]:a}})},z=(e,a)=>{if(l({type:"SET_CONFIG",payload:{[e]:a}}),"crypto1"===e){let e=W.vA[a]||ev||["USDT","USDC","BTC"];t.crypto2&&Array.isArray(e)&&e.includes(t.crypto2)||l({type:"SET_CONFIG",payload:{crypto2:e[0]||"USDT"}})}},H=(e,a)=>{let t=parseFloat(a);isNaN(t)&&(t=0),t<0&&(t=0),t>100&&(t=100),"incomeSplitCrypto1Percent"===e?l({type:"SET_CONFIG",payload:{incomeSplitCrypto1Percent:t,incomeSplitCrypto2Percent:100-t}}):l({type:"SET_CONFIG",payload:{incomeSplitCrypto2Percent:t,incomeSplitCrypto1Percent:100-t}})},X=async()=>{if(!L||!I||!$){f({title:"AI Suggestion Error",description:"Please fill all AI suggestion fields.",variant:"destructive"});return}await h({riskTolerance:L,preferredCryptocurrencies:I,investmentGoals:$})},q=W.hg||[];return"SimpleSpot"===t.tradingMode?W.vA[t.crypto1]:(W.hg||[]).filter(e=>e!==t.crypto1),console.log("\uD83D\uDD0D DEBUG: AVAILABLE_CRYPTOS length:",W.hg?.length),console.log("\uD83D\uDD0D DEBUG: crypto1Options length:",q.length),console.log("\uD83D\uDD0D DEBUG: First 20 cryptos:",W.hg?.slice(0,20)),console.log("\uD83D\uDD0D DEBUG: ALLOWED_CRYPTO1:",W.ALLOWED_CRYPTO1),console.log("\uD83D\uDD0D DEBUG: ALLOWED_CRYPTO2:",W.ALLOWED_CRYPTO2),console.log("\uD83D\uDD0D DEBUG: AVAILABLE_STABLECOINS:",W.Ql),(0,s.jsxs)("aside",{className:"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-sidebar-primary",children:"Trading Configuration"}),(0,s.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>P(!R),className:"text-sidebar-accent-foreground hover:bg-sidebar-accent",title:R?"Hide AI Suggestions":"Show AI Suggestions",children:(0,s.jsx)(eg.A,{className:`h-4 w-4 ${R?"text-primary":"text-muted-foreground"}`})})]}),(0,s.jsx)(U.F,{className:"flex-1 pr-2",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(g.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,s.jsx)(g.aR,{children:(0,s.jsx)(g.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Mode"})}),(0,s.jsxs)(g.Wu,{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(E,{id:"enableStablecoinSwap",checked:"StablecoinSwap"===t.tradingMode,onCheckedChange:e=>{let a;let s=e?"StablecoinSwap":"SimpleSpot";a="StablecoinSwap"===s?(W.ALLOWED_CRYPTO1||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(e=>e!==t.crypto1)[0]:(W.ALLOWED_CRYPTO2||["USDC","DAI","TUSD","FDUSD","USDT","EUR"])[0],l({type:"SET_CONFIG",payload:{tradingMode:s,crypto2:a}})}}),(0,s.jsx)(w.J,{htmlFor:"enableStablecoinSwap",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Enable Stablecoin Swap Mode"})]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Current mode: ","SimpleSpot"===t.tradingMode?"Simple Spot":"Stablecoin Swap"]}),"StablecoinSwap"===t.tradingMode&&(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:"preferredStablecoin",children:"Preferred Stablecoin"}),(0,s.jsxs)(k.l6,{name:"preferredStablecoin",value:t.preferredStablecoin,onValueChange:e=>z("preferredStablecoin",e),children:[(0,s.jsx)(k.bq,{id:"preferredStablecoin",children:(0,s.jsx)(k.yv,{placeholder:"Select stablecoin"})}),(0,s.jsx)(k.gC,{className:"max-h-[300px] overflow-y-auto",children:eN.map(e=>(0,s.jsx)(k.eb,{value:e,children:e},e))})]})]})]})]}),R&&(0,s.jsxs)(g.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,s.jsx)(g.aR,{children:(0,s.jsxs)(g.ZB,{className:"text-sidebar-accent-foreground flex items-center",children:[(0,s.jsx)(eg.A,{className:"mr-2 h-5 w-5 text-primary"})," AI Mode Suggestion"]})}),(0,s.jsxs)(g.Wu,{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:"riskTolerance",children:"Risk Tolerance"}),(0,s.jsxs)(k.l6,{value:L,onValueChange:_,children:[(0,s.jsx)(k.bq,{id:"riskTolerance",children:(0,s.jsx)(k.yv,{placeholder:"Select risk tolerance"})}),(0,s.jsxs)(k.gC,{children:[(0,s.jsx)(k.eb,{value:"low",children:"Low"}),(0,s.jsx)(k.eb,{value:"medium",children:"Medium"}),(0,s.jsx)(k.eb,{value:"high",children:"High"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:"preferredCryptos",children:"Preferred Cryptocurrencies (comma-separated)"}),(0,s.jsx)(T.p,{id:"preferredCryptos",value:I,onChange:e=>F(e.target.value),placeholder:"e.g., BTC, ETH"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:"investmentGoals",children:"Investment Goals"}),(0,s.jsx)(T.p,{id:"investmentGoals",value:$,onChange:e=>G(e.target.value),placeholder:"e.g., Long term, Short term profit"})]}),(0,s.jsxs)(c.$,{onClick:X,disabled:u,className:"w-full btn-neo",children:[u&&(0,s.jsx)(ef.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Get AI Suggestion"]})]})]}),(0,s.jsxs)(g.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,s.jsx)(g.aR,{children:(0,s.jsx)(g.ZB,{className:"text-sidebar-accent-foreground",children:"Trading Pair"})}),(0,s.jsxs)(g.Wu,{className:"space-y-4",children:[(0,s.jsx)(Y,{label:"Crypto 1 (Base)",value:t.crypto1,allowedCryptos:W.ALLOWED_CRYPTO1||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"],onValidCrypto:e=>{if(l({type:"SET_CONFIG",payload:{crypto1:e}}),"StablecoinSwap"===t.tradingMode){let a=(W.ALLOWED_CRYPTO1||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(a=>a!==e);a.includes(t.crypto2)&&t.crypto2!==e||l({type:"SET_CONFIG",payload:{crypto2:a[0]}})}else{let e=W.ALLOWED_CRYPTO2||["USDC","DAI","TUSD","FDUSD","USDT","EUR"];e.includes(t.crypto2)||l({type:"SET_CONFIG",payload:{crypto2:e[0]}})}},placeholder:"e.g., BTC, ETH, SOL",description:"Enter the base cryptocurrency symbol"}),(0,s.jsx)(Y,{label:"StablecoinSwap"===t.tradingMode?"Crypto 2":"Crypto 2 (Stablecoin)",value:t.crypto2,allowedCryptos:"StablecoinSwap"===t.tradingMode?(W.ALLOWED_CRYPTO1||["BTC","ETH","BNB","SOL","LINK","AVAX","DOT","UNI","NEAR","AAVE","ATOM","VET","RENDER","POL","ALGO","ARB","FET","PAXG","GALA","CRV","COMP","ENJ"]).filter(e=>e!==t.crypto1):W.ALLOWED_CRYPTO2||["USDC","DAI","TUSD","FDUSD","USDT","EUR"],onValidCrypto:e=>{("StablecoinSwap"!==t.tradingMode||e!==t.crypto1)&&l({type:"SET_CONFIG",payload:{crypto2:e}})},placeholder:"StablecoinSwap"===t.tradingMode?"e.g., BTC, ETH, SOL":"e.g., USDT, USDC, DAI",description:"StablecoinSwap"===t.tradingMode?"Enter the second cryptocurrency symbol":"Enter the quote/stablecoin symbol"})]})]}),(0,s.jsxs)(g.Zp,{className:"bg-sidebar-accent border-sidebar-border",children:[(0,s.jsx)(g.aR,{children:(0,s.jsx)(g.ZB,{className:"text-sidebar-accent-foreground",children:"Parameters"})}),(0,s.jsxs)(g.Wu,{className:"space-y-3",children:[[{name:"baseBid",label:"Base Bid (Crypto 2)",type:"number",step:"0.01"},{name:"multiplier",label:"Multiplier",type:"number",step:"0.001"},{name:"numDigits",label:"Display Digits",type:"number",step:"1"},{name:"slippagePercent",label:"Slippage %",type:"number",step:"0.01"}].map(e=>(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{htmlFor:e.name,children:e.label}),(0,s.jsx)(T.p,{id:e.name,name:e.name,type:e.type,value:t[e.name],onChange:V,step:e.step,min:"0"})]},e.name)),(0,s.jsxs)("div",{children:[(0,s.jsx)(w.J,{children:"Couple Income % Split (must sum to 100)"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(w.J,{htmlFor:"incomeSplitCrypto1Percent",className:"text-xs",children:[t.crypto1||"Crypto 1","%"]}),(0,s.jsx)(T.p,{id:"incomeSplitCrypto1Percent",type:"number",value:t.incomeSplitCrypto1Percent,onChange:e=>H("incomeSplitCrypto1Percent",e.target.value),min:"0",max:"100"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)(w.J,{htmlFor:"incomeSplitCrypto2Percent",className:"text-xs",children:[t.crypto2||"Crypto 2","%"]}),(0,s.jsx)(T.p,{id:"incomeSplitCrypto2Percent",type:"number",value:t.incomeSplitCrypto2Percent,onChange:e=>H("incomeSplitCrypto2Percent",e.target.value),min:"0",max:"100"})]})]})]})]})]})]})}),(0,s.jsxs)("div",{className:"flex-shrink-0 mt-4",children:[(0,s.jsx)(M.w,{className:"mb-4 bg-sidebar-border"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:(0,D.cn)("text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border",d?"bg-green-600 text-primary-foreground":"bg-muted text-muted-foreground"),children:[d?(0,s.jsx)(eb.A,{className:"h-4 w-4"}):m?(0,s.jsx)(ef.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(ey.A,{className:"h-4 w-4"}),"Bot Status: ",d?"Running":m?"Warming Up":"Stopped"]}),(0,s.jsx)(c.$,{onClick:()=>y(!0),className:"w-full btn-outline-neo",children:"Set Target Prices"}),(0,s.jsx)(c.$,{onClick:()=>v(!0),className:"w-full btn-outline-neo",children:"Session Alarms"}),(0,s.jsx)(c.$,{onClick:()=>C(!0),className:"w-full btn-outline-neo",children:"Telegram Settings"}),(0,s.jsx)(c.$,{onClick:()=>B(!0),className:"w-full btn-outline-neo",children:"Select Trading Pair"}),(0,s.jsxs)(c.$,{onClick:()=>{d?l({type:"SYSTEM_STOP_BOT"}):l({type:"SYSTEM_START_BOT_INITIATE"})},className:(0,D.cn)("w-full btn-neo",d||m?"bg-destructive hover:bg-destructive/90":"bg-green-600 hover:bg-green-600/90"),disabled:m,children:[d?(0,s.jsx)(eb.A,{className:"h-4 w-4"}):m?(0,s.jsx)(ef.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,s.jsx)(ey.A,{className:"h-4 w-4"}),d?"Stop Bot":m?"Warming Up...":"Start Bot"]}),(0,s.jsxs)(c.$,{onClick:()=>{l({type:"SYSTEM_RESET_BOT"}),f({title:"Bot Reset",description:"Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.",duration:4e3})},variant:"outline",className:"w-full btn-outline-neo",disabled:m,children:[(0,s.jsx)(ej.A,{className:"h-4 w-4 mr-2"}),"Reset Bot"]})]})]}),(0,s.jsx)(Z,{isOpen:b,onClose:()=>y(!1),onSetTargetPrices:o}),(0,s.jsx)(es,{isOpen:j,onClose:()=>v(!1)}),(0,s.jsx)(ec,{isOpen:A,onClose:()=>C(!1)}),(0,s.jsx)(eh,{isOpen:O,onClose:()=>B(!1)})]})}function eT({children:e}){let{isAuthenticated:a,isLoading:t}=(0,d.A)(),r=(0,n.useRouter)();return t?(0,s.jsx)("div",{className:"flex items-center justify-center h-screen bg-background",children:(0,s.jsx)(ef.A,{className:"h-12 w-12 animate-spin text-primary"})}):a?(0,s.jsxs)("div",{className:"flex flex-col min-h-screen bg-background text-foreground",children:[(0,s.jsx)(v,{}),(0,s.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,s.jsx)(eS,{}),(0,s.jsx)("main",{className:"flex-1 overflow-y-auto p-4 md:p-6 lg:p-8",children:e})]})]}):(r.push("/login"),null)}},96834:(e,a,t)=>{"use strict";t.d(a,{E:()=>n});var s=t(60687);t(43210);var r=t(24224),l=t(4780);let i=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:a,...t}){return(0,s.jsx)("div",{className:(0,l.cn)(i({variant:a}),e),...t})}}};