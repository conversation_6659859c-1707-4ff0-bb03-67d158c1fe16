"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/history/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/BalancesDisplay.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BalancesDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bitcoin.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction BalancesDisplay() {\n    _s();\n    const { crypto1Balance, crypto2Balance, stablecoinBalance, config, dispatch, calculateTotalPL, resetGlobalBalances } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const [editingBalance, setEditingBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [tempValues, setTempValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        crypto1: crypto1Balance.toString(),\n        crypto2: crypto2Balance.toString(),\n        stablecoin: stablecoinBalance.toString()\n    });\n    const formatBalance = (balance)=>balance.toFixed(config.numDigits);\n    const handleEdit = (balanceType)=>{\n        setEditingBalance(balanceType);\n        setTempValues({\n            crypto1: crypto1Balance.toString(),\n            crypto2: crypto2Balance.toString(),\n            stablecoin: stablecoinBalance.toString()\n        });\n    };\n    const handleSave = (balanceType)=>{\n        const newValue = parseFloat(tempValues[balanceType]);\n        if (!isNaN(newValue) && newValue >= 0) {\n            if (balanceType === 'crypto1') {\n                dispatch({\n                    type: 'UPDATE_BALANCES',\n                    payload: {\n                        crypto1: newValue,\n                        crypto2: crypto2Balance\n                    }\n                });\n            } else if (balanceType === 'crypto2') {\n                dispatch({\n                    type: 'UPDATE_BALANCES',\n                    payload: {\n                        crypto1: crypto1Balance,\n                        crypto2: newValue\n                    }\n                });\n            } else if (balanceType === 'stablecoin') {\n                dispatch({\n                    type: 'UPDATE_STABLECOIN_BALANCE',\n                    payload: newValue\n                });\n            }\n        }\n        setEditingBalance(null);\n    };\n    const handleCancel = ()=>{\n        setEditingBalance(null);\n        setTempValues({\n            crypto1: crypto1Balance.toString(),\n            crypto2: crypto2Balance.toString(),\n            stablecoin: stablecoinBalance.toString()\n        });\n    };\n    // Enhanced Total P/L display with StablecoinSwap support\n    const renderTotalPL = ()=>{\n        const totalPL = calculateTotalPL();\n        const currency = config.tradingMode === 'StablecoinSwap' ? config.crypto2 : 'USD';\n        const symbol = config.tradingMode === 'StablecoinSwap' ? '' : '$';\n        const colorClass = totalPL >= 0 ? 'text-green-600' : 'text-red-600';\n        const icon = totalPL >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5 text-green-600\"\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n            lineNumber: 62,\n            columnNumber: 33\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5 text-red-600\"\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n            lineNumber: 62,\n            columnNumber: 85\n        }, this);\n        const profitIcon = totalPL >= 0 ? '📈' : '📉';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-sm font-medium text-muted-foreground\",\n                            children: \"Total Realized P/L\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, this),\n                        icon\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold \".concat(colorClass, \" flex items-center gap-2\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: profitIcon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                symbol,\n                                                totalPL >= 0 ? '+' : '',\n                                                totalPL.toFixed(config.numDigits),\n                                                \" \",\n                                                currency\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: config.tradingMode === 'StablecoinSwap' ? 'StablecoinSwap Mode' : 'SimpleSpot Mode'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this);\n    };\n    const renderBalanceCard = (title, balance, balanceType, icon, currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-sm font-medium text-muted-foreground\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 9\n                        }, this),\n                        icon\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: editingBalance === balanceType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                type: \"number\",\n                                value: tempValues[balanceType],\n                                onChange: (e)=>setTempValues((prev)=>({\n                                            ...prev,\n                                            [balanceType]: e.target.value\n                                        })),\n                                className: \"text-lg font-bold\",\n                                step: \"any\",\n                                min: \"0\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        size: \"sm\",\n                                        onClick: ()=>handleSave(balanceType),\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Save\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleCancel,\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cancel\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-foreground\",\n                                        children: formatBalance(balance)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"Available \",\n                                            currency\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                size: \"sm\",\n                                variant: \"ghost\",\n                                onClick: ()=>handleEdit(balanceType),\n                                className: \"ml-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n            lineNumber: 97,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 mb-6\",\n        children: [\n            renderTotalPL(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-3\",\n                children: [\n                    renderBalanceCard(\"\".concat(config.crypto1 || \"Crypto 1\", \" Balance\"), crypto1Balance, 'crypto1', /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                        className: \"h-5 w-5 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 11\n                    }, this), config.crypto1 || \"Crypto 1\"),\n                    renderBalanceCard(\"\".concat(config.crypto2 || \"Crypto 2\", \" Balance\"), crypto2Balance, 'crypto2', /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-5 w-5 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this), config.crypto2 || \"Crypto 2\"),\n                    renderBalanceCard(\"Stablecoin Balance (\".concat(config.preferredStablecoin || 'N/A', \")\"), stablecoinBalance, 'stablecoin', /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-5 w-5 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this), 'Stablecoins')\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n_s(BalancesDisplay, \"6GMfjJ84uDg2u8xN//yOfKGDDnI=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = BalancesDisplay;\nvar _c;\n$RefreshReg$(_c, \"BalancesDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\n"));

/***/ })

});