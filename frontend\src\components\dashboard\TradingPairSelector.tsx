"use client";

import React, { useState, useMemo } from 'react';
import { useTradingContext } from '@/contexts/TradingContext';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Search, TrendingUp, Info } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { 
  TRADING_PAIRS, 
  TRADING_PAIR_CATEGORIES, 
  getTradingPairsByMode, 
  getTradingPairsByCategory, 
  searchTradingPairs,
  type TradingPair 
} from '@/config/tradingPairs';

interface TradingPairSelectorProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function TradingPairSelector({ isOpen, onClose }: TradingPairSelectorProps) {
  const { config, dispatch } = useTradingContext();
  const { toast } = useToast();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPair, setSelectedPair] = useState<TradingPair | null>(null);

  // Filter pairs based on current trading mode
  const availablePairs = useMemo(() => {
    let pairs = getTradingPairsByMode(config.tradingMode || 'SimpleSpot');
    
    // Apply category filter
    if (selectedCategory !== 'all') {
      pairs = pairs.filter(pair => pair.category === selectedCategory);
    }
    
    // Apply search filter
    if (searchQuery.trim()) {
      pairs = searchTradingPairs(searchQuery).filter(pair => 
        pair.supportedModes.includes(config.tradingMode || 'SimpleSpot')
      );
    }
    
    return pairs;
  }, [config.tradingMode, selectedCategory, searchQuery]);

  const handleSelectPair = (pair: TradingPair) => {
    setSelectedPair(pair);
  };

  const handleApplyPair = () => {
    if (!selectedPair) {
      toast({
        title: "No Pair Selected",
        description: "Please select a trading pair first.",
        variant: "destructive"
      });
      return;
    }

    dispatch({
      type: 'SET_CONFIG',
      payload: {
        crypto1: selectedPair.crypto1,
        crypto2: selectedPair.crypto2
      }
    });

    toast({
      title: "Trading Pair Updated",
      description: `Now trading ${selectedPair.displayName}`,
      duration: 3000
    });

    onClose();
  };

  const getCategoryColor = (category: TradingPair['category']) => {
    const colors = {
      major: 'bg-blue-100 text-blue-800',
      altcoin: 'bg-green-100 text-green-800',
      defi: 'bg-purple-100 text-purple-800',
      meme: 'bg-orange-100 text-orange-800',
      stablecoin: 'bg-gray-100 text-gray-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Select Trading Pair
          </DialogTitle>
          <DialogDescription>
            Choose from 50+ supported trading pairs for {config.tradingMode || 'SimpleSpot'} mode.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Search and Filter Controls */}
          <div className="flex gap-4">
            <div className="flex-1">
              <Label htmlFor="search">Search Pairs</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by symbol or name..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-48">
              <Label htmlFor="category">Category</Label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger id="category">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {TRADING_PAIR_CATEGORIES.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Trading Pairs List */}
          <ScrollArea className="h-[400px] border rounded-lg">
            <div className="p-4 space-y-2">
              {availablePairs.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No trading pairs found matching your criteria.</p>
                </div>
              ) : (
                availablePairs.map((pair) => (
                  <Card
                    key={pair.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedPair?.id === pair.id ? 'ring-2 ring-primary' : ''
                    }`}
                    onClick={() => handleSelectPair(pair)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold text-lg">
                              {pair.crypto1}/{pair.crypto2}
                            </h3>
                            <Badge className={getCategoryColor(pair.category)}>
                              {pair.category}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            {pair.description}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span>Min: {pair.minTradeAmount} {pair.crypto1}</span>
                            <span>Max: {pair.maxTradeAmount} {pair.crypto1}</span>
                            <span>Decimals: {pair.priceDecimals}</span>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">
                            {pair.displayName}
                          </div>
                          <div className="flex gap-1 mt-1">
                            {pair.supportedModes.map((mode) => (
                              <Badge key={mode} variant="outline" className="text-xs">
                                {mode}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>

          {/* Selected Pair Info */}
          {selectedPair && (
            <Card className="bg-muted/50">
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Info className="h-4 w-4" />
                  Selected Pair Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Pair:</span> {selectedPair.displayName}
                  </div>
                  <div>
                    <span className="font-medium">Category:</span> {selectedPair.category}
                  </div>
                  <div>
                    <span className="font-medium">Min Trade:</span> {selectedPair.minTradeAmount} {selectedPair.crypto1}
                  </div>
                  <div>
                    <span className="font-medium">Max Trade:</span> {selectedPair.maxTradeAmount} {selectedPair.crypto1}
                  </div>
                  <div>
                    <span className="font-medium">Price Decimals:</span> {selectedPair.priceDecimals}
                  </div>
                  <div>
                    <span className="font-medium">Amount Decimals:</span> {selectedPair.amountDecimals}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleApplyPair} 
              disabled={!selectedPair}
              className="btn-neo"
            >
              Apply Trading Pair
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
