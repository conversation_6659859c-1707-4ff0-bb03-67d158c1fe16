"use strict";exports.id=288,exports.ids=[288],exports.modules={43:(e,t,r)=>{r.d(t,{jH:()=>i});var n=r(43210);r(60687);var o=n.createContext(void 0);function i(e){let t=n.useContext(o);return e||t||"ltr"}},1305:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},1359:(e,t,r)=>{r.d(t,{Oh:()=>i});var n=r(43210),o=0;function i(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??l()),document.body.insertAdjacentElement("beforeend",e[1]??l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},1833:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},3341:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("ChartLine",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},9812:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]])},15036:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},18329:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},18853:(e,t,r)=>{r.d(t,{X:()=>i});var n=r(43210),o=r(66156);function i(e){let[t,r]=n.useState(void 0);return(0,o.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},19422:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},21277:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("CircleDollarSign",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 18V6",key:"zqpxq5"}]])},24026:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},24851:(e,t,r)=>{r.d(t,{CC:()=>G,Q6:()=>V,bL:()=>z,zi:()=>K});var n=r(43210),o=r(67969),i=r(70569),l=r(98599),a=r(11273),s=r(65551),c=r(43),u=r(83721),d=r(18853),f=r(14163),p=r(9510),h=r(60687),m=["PageUp","PageDown"],v=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],g={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},y="Slider",[w,b,x]=(0,p.N)(y),[S,E]=(0,a.A)(y,[x]),[C,R]=S(y),A=n.forwardRef((e,t)=>{let{name:r,min:l=0,max:a=100,step:c=1,orientation:u="horizontal",disabled:d=!1,minStepsBetweenThumbs:f=0,defaultValue:p=[l],value:g,onValueChange:y=()=>{},onValueCommit:b=()=>{},inverted:x=!1,form:S,...E}=e,R=n.useRef(new Set),A=n.useRef(0),k="horizontal"===u,[T=[],D]=(0,s.i)({prop:g,defaultProp:p,onChange:e=>{let t=[...R.current];t[A.current]?.focus(),y(e)}}),L=n.useRef(T);function M(e,t,{commit:r}={commit:!1}){let n=(String(c).split(".")[1]||"").length,i=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-l)/c)*c+l,n),s=(0,o.q)(i,[l,a]);D((e=[])=>{let n=function(e=[],t,r){let n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,s,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,f*c))return e;{A.current=n.indexOf(s);let t=String(n)!==String(e);return t&&r&&b(n),t?n:e}})}return(0,h.jsx)(C,{scope:e.__scopeSlider,name:r,disabled:d,min:l,max:a,valueIndexToChangeRef:A,thumbs:R.current,values:T,orientation:u,form:S,children:(0,h.jsx)(w.Provider,{scope:e.__scopeSlider,children:(0,h.jsx)(w.Slot,{scope:e.__scopeSlider,children:(0,h.jsx)(k?j:P,{"aria-disabled":d,"data-disabled":d?"":void 0,...E,ref:t,onPointerDown:(0,i.m)(E.onPointerDown,()=>{d||(L.current=T)}),min:l,max:a,inverted:x,onSlideStart:d?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(T,e);M(e,t)},onSlideMove:d?void 0:function(e){M(e,A.current)},onSlideEnd:d?void 0:function(){let e=L.current[A.current];T[A.current]!==e&&b(T)},onHomeKeyDown:()=>!d&&M(l,0,{commit:!0}),onEndKeyDown:()=>!d&&M(a,T.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!d){let r=m.includes(e.key)||e.shiftKey&&v.includes(e.key),n=A.current;M(T[n]+c*(r?10:1)*t,n,{commit:!0})}}})})})})});A.displayName=y;var[k,T]=S(y,{startEdge:"left",endEdge:"right",size:"width",direction:1}),j=n.forwardRef((e,t)=>{let{min:r,max:o,dir:i,inverted:a,onSlideStart:s,onSlideMove:u,onSlideEnd:d,onStepKeyDown:f,...p}=e,[m,v]=n.useState(null),y=(0,l.s)(t,e=>v(e)),w=n.useRef(void 0),b=(0,c.jH)(i),x="ltr"===b,S=x&&!a||!x&&a;function E(e){let t=w.current||m.getBoundingClientRect(),n=W([0,t.width],S?[r,o]:[o,r]);return w.current=t,n(e-t.left)}return(0,h.jsx)(k,{scope:e.__scopeSlider,startEdge:S?"left":"right",endEdge:S?"right":"left",direction:S?1:-1,size:"width",children:(0,h.jsx)(D,{dir:b,"data-orientation":"horizontal",...p,ref:y,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=E(e.clientX);s?.(t)},onSlideMove:e=>{let t=E(e.clientX);u?.(t)},onSlideEnd:()=>{w.current=void 0,d?.()},onStepKeyDown:e=>{let t=g[S?"from-left":"from-right"].includes(e.key);f?.({event:e,direction:t?-1:1})}})})}),P=n.forwardRef((e,t)=>{let{min:r,max:o,inverted:i,onSlideStart:a,onSlideMove:s,onSlideEnd:c,onStepKeyDown:u,...d}=e,f=n.useRef(null),p=(0,l.s)(t,f),m=n.useRef(void 0),v=!i;function y(e){let t=m.current||f.current.getBoundingClientRect(),n=W([0,t.height],v?[o,r]:[r,o]);return m.current=t,n(e-t.top)}return(0,h.jsx)(k,{scope:e.__scopeSlider,startEdge:v?"bottom":"top",endEdge:v?"top":"bottom",size:"height",direction:v?1:-1,children:(0,h.jsx)(D,{"data-orientation":"vertical",...d,ref:p,style:{...d.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=y(e.clientY);a?.(t)},onSlideMove:e=>{let t=y(e.clientY);s?.(t)},onSlideEnd:()=>{m.current=void 0,c?.()},onStepKeyDown:e=>{let t=g[v?"from-bottom":"from-top"].includes(e.key);u?.({event:e,direction:t?-1:1})}})})}),D=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:l,onHomeKeyDown:a,onEndKeyDown:s,onStepKeyDown:c,...u}=e,d=R(y,r);return(0,h.jsx)(f.sG.span,{...u,ref:t,onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Home"===e.key?(a(e),e.preventDefault()):"End"===e.key?(s(e),e.preventDefault()):m.concat(v).includes(e.key)&&(c(e),e.preventDefault())}),onPointerDown:(0,i.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),d.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,i.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&o(e)}),onPointerUp:(0,i.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),l(e))})})}),L="SliderTrack",M=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,o=R(L,r);return(0,h.jsx)(f.sG.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:t})});M.displayName=L;var N="SliderRange",I=n.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,i=R(N,r),a=T(N,r),s=n.useRef(null),c=(0,l.s)(t,s),u=i.values.length,d=i.values.map(e=>B(e,i.min,i.max)),p=u>1?Math.min(...d):0,m=100-Math.max(...d);return(0,h.jsx)(f.sG.span,{"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,...o,ref:c,style:{...e.style,[a.startEdge]:p+"%",[a.endEdge]:m+"%"}})});I.displayName=N;var O="SliderThumb",_=n.forwardRef((e,t)=>{let r=b(e.__scopeSlider),[o,i]=n.useState(null),a=(0,l.s)(t,e=>i(e)),s=n.useMemo(()=>o?r().findIndex(e=>e.ref.current===o):-1,[r,o]);return(0,h.jsx)(F,{...e,ref:a,index:s})}),F=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:o,name:a,...s}=e,c=R(O,r),u=T(O,r),[p,m]=n.useState(null),v=(0,l.s)(t,e=>m(e)),g=!p||c.form||!!p.closest("form"),y=(0,d.X)(p),b=c.values[o],x=void 0===b?0:B(b,c.min,c.max),S=function(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}(o,c.values.length),E=y?.[u.size],C=E?function(e,t,r){let n=e/2,o=W([0,50],[0,n]);return(n-o(t)*r)*r}(E,x,u.direction):0;return n.useEffect(()=>{if(p)return c.thumbs.add(p),()=>{c.thumbs.delete(p)}},[p,c.thumbs]),(0,h.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[u.startEdge]:`calc(${x}% + ${C}px)`},children:[(0,h.jsx)(w.ItemSlot,{scope:e.__scopeSlider,children:(0,h.jsx)(f.sG.span,{role:"slider","aria-label":e["aria-label"]||S,"aria-valuemin":c.min,"aria-valuenow":b,"aria-valuemax":c.max,"aria-orientation":c.orientation,"data-orientation":c.orientation,"data-disabled":c.disabled?"":void 0,tabIndex:c.disabled?void 0:0,...s,ref:v,style:void 0===b?{display:"none"}:e.style,onFocus:(0,i.m)(e.onFocus,()=>{c.valueIndexToChangeRef.current=o})})}),g&&(0,h.jsx)(H,{name:a??(c.name?c.name+(c.values.length>1?"[]":""):void 0),form:c.form,value:b},o)]})});_.displayName=O;var H=n.forwardRef(({__scopeSlider:e,value:t,...r},o)=>{let i=n.useRef(null),a=(0,l.s)(i,o),s=(0,u.Z)(t);return n.useEffect(()=>{let e=i.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(s!==t&&r){let n=new Event("input",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[s,t]),(0,h.jsx)(f.sG.input,{style:{display:"none"},...r,ref:a,defaultValue:t})});function B(e,t,r){return(0,o.q)(100/(r-t)*(e-t),[0,100])}function W(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}H.displayName="RadioBubbleInput";var z=A,G=M,V=I,K=_},25371:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("PowerOff",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},26134:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>J,bm:()=>ei,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(43210),o=r(70569),i=r(98599),l=r(11273),a=r(96963),s=r(65551),c=r(31355),u=r(32547),d=r(25028),f=r(46059),p=r(14163),h=r(1359),m=r(42247),v=r(63376),g=r(8730),y=r(60687),w="Dialog",[b,x]=(0,l.A)(w),[S,E]=b(w),C=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:i,onOpenChange:l,modal:c=!0}=e,u=n.useRef(null),d=n.useRef(null),[f,p]=(0,s.i)({prop:o,defaultProp:i??!1,onChange:l,caller:w});return(0,y.jsx)(S,{scope:t,triggerRef:u,contentRef:d,contentId:(0,a.B)(),titleId:(0,a.B)(),descriptionId:(0,a.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:c,children:r})};C.displayName=w;var R="DialogTrigger",A=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=E(R,r),a=(0,i.s)(t,l.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":q(l.open),...n,ref:a,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});A.displayName=R;var k="DialogPortal",[T,j]=b(k,{forceMount:void 0}),P=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:i}=e,l=E(k,t);return(0,y.jsx)(T,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,y.jsx)(f.C,{present:r||l.open,children:(0,y.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};P.displayName=k;var D="DialogOverlay",L=n.forwardRef((e,t)=>{let r=j(D,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=E(D,e.__scopeDialog);return i.modal?(0,y.jsx)(f.C,{present:n||i.open,children:(0,y.jsx)(N,{...o,ref:t})}):null});L.displayName=D;var M=(0,g.TL)("DialogOverlay.RemoveScroll"),N=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=E(D,r);return(0,y.jsx)(m.A,{as:M,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":q(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),I="DialogContent",O=n.forwardRef((e,t)=>{let r=j(I,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=E(I,e.__scopeDialog);return(0,y.jsx)(f.C,{present:n||i.open,children:i.modal?(0,y.jsx)(_,{...o,ref:t}):(0,y.jsx)(F,{...o,ref:t})})});O.displayName=I;var _=n.forwardRef((e,t)=>{let r=E(I,e.__scopeDialog),l=n.useRef(null),a=(0,i.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(H,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=n.forwardRef((e,t)=>{let r=E(I,e.__scopeDialog),o=n.useRef(!1),i=n.useRef(!1);return(0,y.jsx)(H,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),H=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:a,...s}=e,d=E(I,r),f=n.useRef(null),p=(0,i.s)(t,f);return(0,h.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:a,children:(0,y.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":q(d.open),...s,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Y,{titleId:d.titleId}),(0,y.jsx)(Z,{contentRef:f,descriptionId:d.descriptionId})]})]})}),B="DialogTitle",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=E(B,r);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});W.displayName=B;var z="DialogDescription",G=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=E(z,r);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});G.displayName=z;var V="DialogClose",K=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=E(V,r);return(0,y.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function q(e){return e?"open":"closed"}K.displayName=V;var U="DialogTitleWarning",[X,$]=(0,l.q)(U,{contentName:I,titleName:B,docsSlug:"dialog"}),Y=({titleId:e})=>{let t=$(U),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},Z=({contentRef:e,descriptionId:t})=>{let r=$("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},J=C,Q=A,ee=P,et=L,er=O,en=W,eo=G,ei=K},28850:(e,t,r)=>{r.d(t,{UC:()=>rf,YJ:()=>rh,In:()=>ru,q7:()=>rv,VF:()=>ry,p4:()=>rg,JU:()=>rm,ZL:()=>rd,bL:()=>ra,wn:()=>rb,PP:()=>rw,wv:()=>rx,l9:()=>rs,WT:()=>rc,LM:()=>rp});var n=r(43210),o=r(51215),i=r(67969),l=r(70569),a=r(9510),s=r(98599),c=r(11273),u=r(43),d=r(31355),f=r(1359),p=r(32547),h=r(96963);let m=["top","right","bottom","left"],v=Math.min,g=Math.max,y=Math.round,w=Math.floor,b=e=>({x:e,y:e}),x={left:"right",right:"left",bottom:"top",top:"bottom"},S={start:"end",end:"start"};function E(e,t){return"function"==typeof e?e(t):e}function C(e){return e.split("-")[0]}function R(e){return e.split("-")[1]}function A(e){return"x"===e?"y":"x"}function k(e){return"y"===e?"height":"width"}let T=new Set(["top","bottom"]);function j(e){return T.has(C(e))?"y":"x"}function P(e){return e.replace(/start|end/g,e=>S[e])}let D=["left","right"],L=["right","left"],M=["top","bottom"],N=["bottom","top"];function I(e){return e.replace(/left|right|bottom|top/g,e=>x[e])}function O(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function _(e){let{x:t,y:r,width:n,height:o}=e;return{width:n,height:o,top:r,left:t,right:t+n,bottom:r+o,x:t,y:r}}function F(e,t,r){let n,{reference:o,floating:i}=e,l=j(t),a=A(j(t)),s=k(a),c=C(t),u="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(c){case"top":n={x:d,y:o.y-i.height};break;case"bottom":n={x:d,y:o.y+o.height};break;case"right":n={x:o.x+o.width,y:f};break;case"left":n={x:o.x-i.width,y:f};break;default:n={x:o.x,y:o.y}}switch(R(t)){case"start":n[a]-=p*(r&&u?-1:1);break;case"end":n[a]+=p*(r&&u?-1:1)}return n}let H=async(e,t,r)=>{let{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:l}=r,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=F(c,n,s),f=n,p={},h=0;for(let r=0;r<a.length;r++){let{name:i,fn:m}=a[r],{x:v,y:g,data:y,reset:w}=await m({x:u,y:d,initialPlacement:n,placement:f,strategy:o,middlewareData:p,rects:c,platform:l,elements:{reference:e,floating:t}});u=null!=v?v:u,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:u,y:d}=F(c,f,s)),r=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}};async function B(e,t){var r;void 0===t&&(t={});let{x:n,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=E(t,e),h=O(p),m=a[f?"floating"===d?"reference":"floating":d],v=_(await i.getClippingRect({element:null==(r=await (null==i.isElement?void 0:i.isElement(m)))||r?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:c,rootBoundary:u,strategy:s})),g="floating"===d?{x:n,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=_(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:s}):g);return{top:(v.top-b.top+h.top)/w.y,bottom:(b.bottom-v.bottom+h.bottom)/w.y,left:(v.left-b.left+h.left)/w.x,right:(b.right-v.right+h.right)/w.x}}function W(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function z(e){return m.some(t=>e[t]>=0)}let G=new Set(["left","top"]);async function V(e,t){let{placement:r,platform:n,elements:o}=e,i=await (null==n.isRTL?void 0:n.isRTL(o.floating)),l=C(r),a=R(r),s="y"===j(r),c=G.has(l)?-1:1,u=i&&s?-1:1,d=E(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:h}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof h&&(p="end"===a?-1*h:h),s?{x:p*u,y:f*c}:{x:f*c,y:p*u}}function K(){return"undefined"!=typeof window}function q(e){return $(e)?(e.nodeName||"").toLowerCase():"#document"}function U(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function X(e){var t;return null==(t=($(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function $(e){return!!K()&&(e instanceof Node||e instanceof U(e).Node)}function Y(e){return!!K()&&(e instanceof Element||e instanceof U(e).Element)}function Z(e){return!!K()&&(e instanceof HTMLElement||e instanceof U(e).HTMLElement)}function J(e){return!!K()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof U(e).ShadowRoot)}let Q=new Set(["inline","contents"]);function ee(e){let{overflow:t,overflowX:r,overflowY:n,display:o}=ed(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!Q.has(o)}let et=new Set(["table","td","th"]),er=[":popover-open",":modal"];function en(e){return er.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eo=["transform","translate","scale","rotate","perspective"],ei=["transform","translate","scale","rotate","perspective","filter"],el=["paint","layout","strict","content"];function ea(e){let t=es(),r=Y(e)?ed(e):e;return eo.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||ei.some(e=>(r.willChange||"").includes(e))||el.some(e=>(r.contain||"").includes(e))}function es(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ec=new Set(["html","body","#document"]);function eu(e){return ec.has(q(e))}function ed(e){return U(e).getComputedStyle(e)}function ef(e){return Y(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ep(e){if("html"===q(e))return e;let t=e.assignedSlot||e.parentNode||J(e)&&e.host||X(e);return J(t)?t.host:t}function eh(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let o=function e(t){let r=ep(t);return eu(r)?t.ownerDocument?t.ownerDocument.body:t.body:Z(r)&&ee(r)?r:e(r)}(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),l=U(o);if(i){let e=em(l);return t.concat(l,l.visualViewport||[],ee(o)?o:[],e&&r?eh(e):[])}return t.concat(o,eh(o,[],r))}function em(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ev(e){let t=ed(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,o=Z(e),i=o?e.offsetWidth:r,l=o?e.offsetHeight:n,a=y(r)!==i||y(n)!==l;return a&&(r=i,n=l),{width:r,height:n,$:a}}function eg(e){return Y(e)?e:e.contextElement}function ey(e){let t=eg(e);if(!Z(t))return b(1);let r=t.getBoundingClientRect(),{width:n,height:o,$:i}=ev(t),l=(i?y(r.width):r.width)/n,a=(i?y(r.height):r.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let ew=b(0);function eb(e){let t=U(e);return es()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ew}function ex(e,t,r,n){var o;void 0===t&&(t=!1),void 0===r&&(r=!1);let i=e.getBoundingClientRect(),l=eg(e),a=b(1);t&&(n?Y(n)&&(a=ey(n)):a=ey(e));let s=(void 0===(o=r)&&(o=!1),n&&(!o||n===U(l))&&o)?eb(l):b(0),c=(i.left+s.x)/a.x,u=(i.top+s.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=U(l),t=n&&Y(n)?U(n):n,r=e,o=em(r);for(;o&&n&&t!==r;){let e=ey(o),t=o.getBoundingClientRect(),n=ed(o),i=t.left+(o.clientLeft+parseFloat(n.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,u*=e.y,d*=e.x,f*=e.y,c+=i,u+=l,o=em(r=U(o))}}return _({width:d,height:f,x:c,y:u})}function eS(e,t){let r=ef(e).scrollLeft;return t?t.left+r:ex(X(e)).left+r}function eE(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:eS(e,n)),y:n.top+t.scrollTop}}let eC=new Set(["absolute","fixed"]);function eR(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=U(e),n=X(e),o=r.visualViewport,i=n.clientWidth,l=n.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;let e=es();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,r);else if("document"===t)n=function(e){let t=X(e),r=ef(e),n=e.ownerDocument.body,o=g(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),i=g(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),l=-r.scrollLeft+eS(e),a=-r.scrollTop;return"rtl"===ed(n).direction&&(l+=g(t.clientWidth,n.clientWidth)-o),{width:o,height:i,x:l,y:a}}(X(e));else if(Y(t))n=function(e,t){let r=ex(e,!0,"fixed"===t),n=r.top+e.clientTop,o=r.left+e.clientLeft,i=Z(e)?ey(e):b(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:n*i.y}}(t,r);else{let r=eb(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return _(n)}function eA(e){return"static"===ed(e).position}function ek(e,t){if(!Z(e)||"fixed"===ed(e).position)return null;if(t)return t(e);let r=e.offsetParent;return X(e)===r&&(r=r.ownerDocument.body),r}function eT(e,t){var r;let n=U(e);if(en(e))return n;if(!Z(e)){let t=ep(e);for(;t&&!eu(t);){if(Y(t)&&!eA(t))return t;t=ep(t)}return n}let o=ek(e,t);for(;o&&(r=o,et.has(q(r)))&&eA(o);)o=ek(o,t);return o&&eu(o)&&eA(o)&&!ea(o)?n:o||function(e){let t=ep(e);for(;Z(t)&&!eu(t);){if(ea(t))return t;if(en(t))break;t=ep(t)}return null}(e)||n}let ej=async function(e){let t=this.getOffsetParent||eT,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=Z(t),o=X(t),i="fixed"===r,l=ex(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=b(0);if(n||!n&&!i){if(("body"!==q(t)||ee(o))&&(a=ef(t)),n){let e=ex(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eS(o))}i&&!n&&o&&(s.x=eS(o));let c=!o||n||i?b(0):eE(o,a);return{x:l.left+a.scrollLeft-s.x-c.x,y:l.top+a.scrollTop-s.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},eP={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:o}=e,i="fixed"===o,l=X(n),a=!!t&&en(t.floating);if(n===l||a&&i)return r;let s={scrollLeft:0,scrollTop:0},c=b(1),u=b(0),d=Z(n);if((d||!d&&!i)&&(("body"!==q(n)||ee(l))&&(s=ef(n)),Z(n))){let e=ex(n);c=ey(n),u.x=e.x+n.clientLeft,u.y=e.y+n.clientTop}let f=!l||d||i?b(0):eE(l,s,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-s.scrollLeft*c.x+u.x+f.x,y:r.y*c.y-s.scrollTop*c.y+u.y+f.y}},getDocumentElement:X,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:o}=e,i=[..."clippingAncestors"===r?en(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=eh(e,[],!1).filter(e=>Y(e)&&"body"!==q(e)),o=null,i="fixed"===ed(e).position,l=i?ep(e):e;for(;Y(l)&&!eu(l);){let t=ed(l),r=ea(l);r||"fixed"!==t.position||(o=null),(i?!r&&!o:!r&&"static"===t.position&&!!o&&eC.has(o.position)||ee(l)&&!r&&function e(t,r){let n=ep(t);return!(n===r||!Y(n)||eu(n))&&("fixed"===ed(n).position||e(n,r))}(e,l))?n=n.filter(e=>e!==l):o=t,l=ep(l)}return t.set(e,n),n}(t,this._c):[].concat(r),n],l=i[0],a=i.reduce((e,r)=>{let n=eR(t,r,o);return e.top=g(n.top,e.top),e.right=v(n.right,e.right),e.bottom=v(n.bottom,e.bottom),e.left=g(n.left,e.left),e},eR(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:eT,getElementRects:ej,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=ev(e);return{width:t,height:r}},getScale:ey,isElement:Y,isRTL:function(e){return"rtl"===ed(e).direction}};function eD(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eL=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:o,rects:i,platform:l,elements:a,middlewareData:s}=t,{element:c,padding:u=0}=E(e,t)||{};if(null==c)return{};let d=O(u),f={x:r,y:n},p=A(j(o)),h=k(p),m=await l.getDimensions(c),y="y"===p,w=y?"clientHeight":"clientWidth",b=i.reference[h]+i.reference[p]-f[p]-i.floating[h],x=f[p]-i.reference[p],S=await (null==l.getOffsetParent?void 0:l.getOffsetParent(c)),C=S?S[w]:0;C&&await (null==l.isElement?void 0:l.isElement(S))||(C=a.floating[w]||i.floating[h]);let T=C/2-m[h]/2-1,P=v(d[y?"top":"left"],T),D=v(d[y?"bottom":"right"],T),L=C-m[h]-D,M=C/2-m[h]/2+(b/2-x/2),N=g(P,v(M,L)),I=!s.arrow&&null!=R(o)&&M!==N&&i.reference[h]/2-(M<P?P:D)-m[h]/2<0,_=I?M<P?M-P:M-L:0;return{[p]:f[p]+_,data:{[p]:N,centerOffset:M-N-_,...I&&{alignmentOffset:_}},reset:I}}}),eM=(e,t,r)=>{let n=new Map,o={platform:eP,...r},i={...o.platform,_c:n};return H(e,t,{...o,platform:i})};var eN="undefined"!=typeof document?n.useLayoutEffect:function(){};function eI(e,t){let r,n,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!eI(e[n],t[n]))return!1;return!0}if((r=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,o[n]))return!1;for(n=r;0!=n--;){let r=o[n];if(("_owner"!==r||!e.$$typeof)&&!eI(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eO(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function e_(e,t){let r=eO(e);return Math.round(t*r)/r}function eF(e){let t=n.useRef(e);return eN(()=>{t.current=e}),t}let eH=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?eL({element:r.current,padding:n}).fn(t):{}:r?eL({element:r,padding:n}).fn(t):{}}}),eB=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:o,y:i,placement:l,middlewareData:a}=t,s=await V(t,e);return l===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),eW=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...s}=E(e,t),c={x:r,y:n},u=await B(t,s),d=j(C(o)),f=A(d),p=c[f],h=c[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",r=p+u[e],n=p-u[t];p=g(r,v(p,n))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",r=h+u[e],n=h-u[t];h=g(r,v(h,n))}let m=a.fn({...t,[f]:p,[d]:h});return{...m,data:{x:m.x-r,y:m.y-n,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),ez=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:c=!0}=E(e,t),u={x:r,y:n},d=j(o),f=A(d),p=u[f],h=u[d],m=E(a,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(s){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,r=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>r&&(p=r)}if(c){var g,y;let e="y"===f?"width":"height",t=G.has(C(o)),r=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),n=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);h<r?h=r:h>n&&(h=n)}return{[f]:p,[d]:h}}}}(e),options:[e,t]}),eG=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,o,i,l;let{placement:a,middlewareData:s,rects:c,initialPlacement:u,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=E(e,t);if(null!=(r=s.arrow)&&r.alignmentOffset)return{};let b=C(a),x=j(u),S=C(u)===u,T=await (null==d.isRTL?void 0:d.isRTL(f.floating)),O=m||(S||!y?[I(u)]:function(e){let t=I(e);return[P(e),t,P(t)]}(u)),_="none"!==g;!m&&_&&O.push(...function(e,t,r,n){let o=R(e),i=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?L:D;return t?D:L;case"left":case"right":return t?M:N;default:return[]}}(C(e),"start"===r,n);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(P)))),i}(u,y,g,T));let F=[u,...O],H=await B(t,w),W=[],z=(null==(n=s.flip)?void 0:n.overflows)||[];if(p&&W.push(H[b]),h){let e=function(e,t,r){void 0===r&&(r=!1);let n=R(e),o=A(j(e)),i=k(o),l="x"===o?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=I(l)),[l,I(l)]}(a,c,T);W.push(H[e[0]],H[e[1]])}if(z=[...z,{placement:a,overflows:W}],!W.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=F[e];if(t&&("alignment"!==h||x===j(t)||z.every(e=>e.overflows[0]>0&&j(e.placement)===x)))return{data:{index:e,overflows:z},reset:{placement:t}};let r=null==(i=z.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!r)switch(v){case"bestFit":{let e=null==(l=z.filter(e=>{if(_){let t=j(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=u}if(a!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),eV=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let o,i;let{placement:l,rects:a,platform:s,elements:c}=t,{apply:u=()=>{},...d}=E(e,t),f=await B(t,d),p=C(l),h=R(l),m="y"===j(l),{width:y,height:w}=a.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==s.isRTL?void 0:s.isRTL(c.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let b=w-f.top-f.bottom,x=y-f.left-f.right,S=v(w-f[o],b),A=v(y-f[i],x),k=!t.middlewareData.shift,T=S,P=A;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(P=x),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(T=b),k&&!h){let e=g(f.left,0),t=g(f.right,0),r=g(f.top,0),n=g(f.bottom,0);m?P=y-2*(0!==e||0!==t?e+t:g(f.left,f.right)):T=w-2*(0!==r||0!==n?r+n:g(f.top,f.bottom))}await u({...t,availableWidth:P,availableHeight:T});let D=await s.getDimensions(c.floating);return y!==D.width||w!==D.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eK=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...o}=E(e,t);switch(n){case"referenceHidden":{let e=W(await B(t,{...o,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:z(e)}}}case"escaped":{let e=W(await B(t,{...o,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:z(e)}}}default:return{}}}}}(e),options:[e,t]}),eq=(e,t)=>({...eH(e),options:[e,t]});var eU=r(14163),eX=r(60687),e$=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,eX.jsx)(eU.sG.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eX.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e$.displayName="Arrow";var eY=r(13495),eZ=r(66156),eJ=r(18853),eQ="Popper",[e0,e1]=(0,c.A)(eQ),[e2,e6]=e0(eQ),e3=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,eX.jsx)(e2,{scope:t,anchor:o,onAnchorChange:i,children:r})};e3.displayName=eQ;var e4="PopperAnchor",e5=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...i}=e,l=e6(e4,r),a=n.useRef(null),c=(0,s.s)(t,a);return n.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eX.jsx)(eU.sG.div,{...i,ref:c})});e5.displayName=e4;var e8="PopperContent",[e9,e7]=e0(e8),te=n.forwardRef((e,t)=>{let{__scopePopper:r,side:i="bottom",sideOffset:l=0,align:a="center",alignOffset:c=0,arrowPadding:u=0,avoidCollisions:d=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:h="partial",hideWhenDetached:m=!1,updatePositionStrategy:y="optimized",onPlaced:b,...x}=e,S=e6(e8,r),[E,C]=n.useState(null),R=(0,s.s)(t,e=>C(e)),[A,k]=n.useState(null),T=(0,eJ.X)(A),j=T?.width??0,P=T?.height??0,D="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},L=Array.isArray(f)?f:[f],M=L.length>0,N={padding:D,boundary:L.filter(to),altBoundary:M},{refs:I,floatingStyles:O,placement:_,isPositioned:F,middlewareData:H}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:s}={},transform:c=!0,whileElementsMounted:u,open:d}=e,[f,p]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[h,m]=n.useState(i);eI(h,i)||m(i);let[v,g]=n.useState(null),[y,w]=n.useState(null),b=n.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=n.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),S=a||v,E=s||y,C=n.useRef(null),R=n.useRef(null),A=n.useRef(f),k=null!=u,T=eF(u),j=eF(l),P=eF(d),D=n.useCallback(()=>{if(!C.current||!R.current)return;let e={placement:t,strategy:r,middleware:h};j.current&&(e.platform=j.current),eM(C.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==P.current};L.current&&!eI(A.current,t)&&(A.current=t,o.flushSync(()=>{p(t)}))})},[h,t,r,j,P]);eN(()=>{!1===d&&A.current.isPositioned&&(A.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let L=n.useRef(!1);eN(()=>(L.current=!0,()=>{L.current=!1}),[]),eN(()=>{if(S&&(C.current=S),E&&(R.current=E),S&&E){if(T.current)return T.current(S,E,D);D()}},[S,E,D,T,k]);let M=n.useMemo(()=>({reference:C,floating:R,setReference:b,setFloating:x}),[b,x]),N=n.useMemo(()=>({reference:S,floating:E}),[S,E]),I=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!N.floating)return e;let t=e_(N.floating,f.x),n=e_(N.floating,f.y);return c?{...e,transform:"translate("+t+"px, "+n+"px)",...eO(N.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,c,N.floating,f.x,f.y]);return n.useMemo(()=>({...f,update:D,refs:M,elements:N,floatingStyles:I}),[f,D,M,N,I])}({strategy:"fixed",placement:i+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let o;void 0===n&&(n={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=n,u=eg(e),d=i||l?[...u?eh(u):[],...eh(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",r,{passive:!0}),l&&e.addEventListener("resize",r)});let f=u&&s?function(e,t){let r,n=null,o=X(e);function i(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function l(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),i();let c=e.getBoundingClientRect(),{left:u,top:d,width:f,height:p}=c;if(a||t(),!f||!p)return;let h=w(d),m=w(o.clientWidth-(u+f)),y={rootMargin:-h+"px "+-m+"px "+-w(o.clientHeight-(d+p))+"px "+-w(u)+"px",threshold:g(0,v(1,s))||1},b=!0;function x(t){let n=t[0].intersectionRatio;if(n!==s){if(!b)return l();n?l(!1,n):r=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==n||eD(c,e.getBoundingClientRect())||l(),b=!1}try{n=new IntersectionObserver(x,{...y,root:o.ownerDocument})}catch(e){n=new IntersectionObserver(x,y)}n.observe(e)}(!0),i}(u,r):null,p=-1,h=null;a&&(h=new ResizeObserver(e=>{let[n]=e;n&&n.target===u&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),r()}),u&&!c&&h.observe(u),h.observe(t));let m=c?ex(e):null;return c&&function t(){let n=ex(e);m&&!eD(m,n)&&r(),m=n,o=requestAnimationFrame(t)}(),r(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",r),l&&e.removeEventListener("resize",r)}),null==f||f(),null==(e=h)||e.disconnect(),h=null,c&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===y}),elements:{reference:S.anchor},middleware:[eB({mainAxis:l+P,alignmentAxis:c}),d&&eW({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?ez():void 0,...N}),d&&eG({...N}),eV({...N,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${r}px`),l.setProperty("--radix-popper-available-height",`${n}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),A&&eq({element:A,padding:u}),ti({arrowWidth:j,arrowHeight:P}),m&&eK({strategy:"referenceHidden",...N})]}),[B,W]=tl(_),z=(0,eY.c)(b);(0,eZ.N)(()=>{F&&z?.()},[F,z]);let G=H.arrow?.x,V=H.arrow?.y,K=H.arrow?.centerOffset!==0,[q,U]=n.useState();return(0,eZ.N)(()=>{E&&U(window.getComputedStyle(E).zIndex)},[E]),(0,eX.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...O,transform:F?O.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:q,"--radix-popper-transform-origin":[H.transformOrigin?.x,H.transformOrigin?.y].join(" "),...H.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eX.jsx)(e9,{scope:r,placedSide:B,onArrowChange:k,arrowX:G,arrowY:V,shouldHideArrow:K,children:(0,eX.jsx)(eU.sG.div,{"data-side":B,"data-align":W,...x,ref:R,style:{...x.style,animation:F?void 0:"none"}})})})});te.displayName=e8;var tt="PopperArrow",tr={top:"bottom",right:"left",bottom:"top",left:"right"},tn=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=e7(tt,r),i=tr[o.placedSide];return(0,eX.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eX.jsx)(e$,{...n,ref:t,style:{...n.style,display:"block"}})})});function to(e){return null!==e}tn.displayName=tt;var ti=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[s,c]=tl(r),u={start:"0%",center:"50%",end:"100%"}[c],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===s?(p=i?u:`${d}px`,h=`${-a}px`):"top"===s?(p=i?u:`${d}px`,h=`${n.floating.height+a}px`):"right"===s?(p=`${-a}px`,h=i?u:`${f}px`):"left"===s&&(p=`${n.floating.width+a}px`,h=i?u:`${f}px`),{data:{x:p,y:h}}}});function tl(e){let[t,r="center"]=e.split("-");return[t,r]}var ta=r(25028),ts=r(8730),tc=r(65551),tu=r(83721),td=r(69024),tf=r(63376),tp=r(42247),th=[" ","Enter","ArrowUp","ArrowDown"],tm=[" ","Enter"],tv="Select",[tg,ty,tw]=(0,a.N)(tv),[tb,tx]=(0,c.A)(tv,[tw,e1]),tS=e1(),[tE,tC]=tb(tv),[tR,tA]=tb(tv),tk=e=>{let{__scopeSelect:t,children:r,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:s,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:m,required:v,form:g}=e,y=tS(t),[w,b]=n.useState(null),[x,S]=n.useState(null),[E,C]=n.useState(!1),R=(0,u.jH)(d),[A,k]=(0,tc.i)({prop:o,defaultProp:i??!1,onChange:l,caller:tv}),[T,j]=(0,tc.i)({prop:a,defaultProp:s,onChange:c,caller:tv}),P=n.useRef(null),D=!w||g||!!w.closest("form"),[L,M]=n.useState(new Set),N=Array.from(L).map(e=>e.props.value).join(";");return(0,eX.jsx)(e3,{...y,children:(0,eX.jsxs)(tE,{required:v,scope:t,trigger:w,onTriggerChange:b,valueNode:x,onValueNodeChange:S,valueNodeHasChildren:E,onValueNodeHasChildrenChange:C,contentId:(0,h.B)(),value:T,onValueChange:j,open:A,onOpenChange:k,dir:R,triggerPointerDownPosRef:P,disabled:m,children:[(0,eX.jsx)(tg.Provider,{scope:t,children:(0,eX.jsx)(tR,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{M(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{M(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),D?(0,eX.jsxs)(rn,{"aria-hidden":!0,required:v,tabIndex:-1,name:f,autoComplete:p,value:T,onChange:e=>j(e.target.value),disabled:m,form:g,children:[void 0===T?(0,eX.jsx)("option",{value:""}):null,Array.from(L)]},N):null]})})};tk.displayName=tv;var tT="SelectTrigger",tj=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:o=!1,...i}=e,a=tS(r),c=tC(tT,r),u=c.disabled||o,d=(0,s.s)(t,c.onTriggerChange),f=ty(r),p=n.useRef("touch"),[h,m,v]=ri(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===c.value),n=rl(t,e,r);void 0!==n&&c.onValueChange(n.value)}),g=e=>{u||(c.onOpenChange(!0),v()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,eX.jsx)(e5,{asChild:!0,...a,children:(0,eX.jsx)(eU.sG.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":ro(c.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&g(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(g(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&th.includes(e.key)&&(g(),e.preventDefault())})})})});tj.displayName=tT;var tP="SelectValue",tD=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:i,placeholder:l="",...a}=e,c=tC(tP,r),{onValueNodeHasChildrenChange:u}=c,d=void 0!==i,f=(0,s.s)(t,c.onValueNodeChange);return(0,eZ.N)(()=>{u(d)},[u,d]),(0,eX.jsx)(eU.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:ro(c.value)?(0,eX.jsx)(eX.Fragment,{children:l}):i})});tD.displayName=tP;var tL=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,eX.jsx)(eU.sG.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});tL.displayName="SelectIcon";var tM=e=>(0,eX.jsx)(ta.Z,{asChild:!0,...e});tM.displayName="SelectPortal";var tN="SelectContent",tI=n.forwardRef((e,t)=>{let r=tC(tN,e.__scopeSelect),[i,l]=n.useState();return((0,eZ.N)(()=>{l(new DocumentFragment)},[]),r.open)?(0,eX.jsx)(tH,{...e,ref:t}):i?o.createPortal((0,eX.jsx)(tO,{scope:e.__scopeSelect,children:(0,eX.jsx)(tg.Slot,{scope:e.__scopeSelect,children:(0,eX.jsx)("div",{children:e.children})})}),i):null});tI.displayName=tN;var[tO,t_]=tb(tN),tF=(0,ts.TL)("SelectContent.RemoveScroll"),tH=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:c,side:u,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:S,...E}=e,C=tC(tN,r),[R,A]=n.useState(null),[k,T]=n.useState(null),j=(0,s.s)(t,e=>A(e)),[P,D]=n.useState(null),[L,M]=n.useState(null),N=ty(r),[I,O]=n.useState(!1),_=n.useRef(!1);n.useEffect(()=>{if(R)return(0,tf.Eq)(R)},[R]),(0,f.Oh)();let F=n.useCallback(e=>{let[t,...r]=N().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&k&&(k.scrollTop=0),r===n&&k&&(k.scrollTop=k.scrollHeight),r?.focus(),document.activeElement!==o))return},[N,k]),H=n.useCallback(()=>F([P,R]),[F,P,R]);n.useEffect(()=>{I&&H()},[I,H]);let{onOpenChange:B,triggerPointerDownPosRef:W}=C;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(W.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(W.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||B(!1),document.removeEventListener("pointermove",t),W.current=null};return null!==W.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,B,W]),n.useEffect(()=>{let e=()=>B(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[B]);let[z,G]=ri(e=>{let t=N().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=rl(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),V=n.useCallback((e,t,r)=>{let n=!_.current&&!r;(void 0!==C.value&&C.value===t||n)&&(D(e),n&&(_.current=!0))},[C.value]),K=n.useCallback(()=>R?.focus(),[R]),q=n.useCallback((e,t,r)=>{let n=!_.current&&!r;(void 0!==C.value&&C.value===t||n)&&M(e)},[C.value]),U="popper"===o?tW:tB,X=U===tW?{side:u,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:w,sticky:b,hideWhenDetached:x,avoidCollisions:S}:{};return(0,eX.jsx)(tO,{scope:r,content:R,viewport:k,onViewportChange:T,itemRefCallback:V,selectedItem:P,onItemLeave:K,itemTextRefCallback:q,focusSelectedItem:H,selectedItemText:L,position:o,isPositioned:I,searchRef:z,children:(0,eX.jsx)(tp.A,{as:tF,allowPinchZoom:!0,children:(0,eX.jsx)(p.n,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{C.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,eX.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,eX.jsx)(U,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...E,...X,onPlaced:()=>O(!0),ref:j,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:(0,l.m)(E.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=N().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});tH.displayName="SelectContentImpl";var tB=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:o,...l}=e,a=tC(tN,r),c=t_(tN,r),[u,d]=n.useState(null),[f,p]=n.useState(null),h=(0,s.s)(t,e=>p(e)),m=ty(r),v=n.useRef(!1),g=n.useRef(!0),{viewport:y,selectedItem:w,selectedItemText:b,focusSelectedItem:x}=c,S=n.useCallback(()=>{if(a.trigger&&a.valueNode&&u&&f&&y&&w&&b){let e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),n=b.getBoundingClientRect();if("rtl"!==a.dir){let o=n.left-t.left,l=r.left-o,a=e.left-l,s=e.width+a,c=Math.max(s,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-c)]);u.style.minWidth=s+"px",u.style.left=f+"px"}else{let o=t.right-n.right,l=window.innerWidth-r.right-o,a=window.innerWidth-e.right-l,s=e.width+a,c=Math.max(s,t.width),d=window.innerWidth-10,f=(0,i.q)(l,[10,Math.max(10,d-c)]);u.style.minWidth=s+"px",u.style.right=f+"px"}let l=m(),s=window.innerHeight-20,c=y.scrollHeight,d=window.getComputedStyle(f),p=parseInt(d.borderTopWidth,10),h=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),x=p+h+c+parseInt(d.paddingBottom,10)+g,S=Math.min(5*w.offsetHeight,x),E=window.getComputedStyle(y),C=parseInt(E.paddingTop,10),R=parseInt(E.paddingBottom,10),A=e.top+e.height/2-10,k=w.offsetHeight/2,T=p+h+(w.offsetTop+k);if(T<=A){let e=l.length>0&&w===l[l.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-A,k+(e?R:0)+(f.clientHeight-y.offsetTop-y.offsetHeight)+g);u.style.height=T+t+"px"}else{let e=l.length>0&&w===l[0].ref.current;u.style.top="0px";let t=Math.max(A,p+y.offsetTop+(e?C:0)+k);u.style.height=t+(x-T)+"px",y.scrollTop=T-A+y.offsetTop}u.style.margin="10px 0",u.style.minHeight=S+"px",u.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>v.current=!0)}},[m,a.trigger,a.valueNode,u,f,y,w,b,a.dir,o]);(0,eZ.N)(()=>S(),[S]);let[E,C]=n.useState();(0,eZ.N)(()=>{f&&C(window.getComputedStyle(f).zIndex)},[f]);let R=n.useCallback(e=>{e&&!0===g.current&&(S(),x?.(),g.current=!1)},[S,x]);return(0,eX.jsx)(tz,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:v,onScrollButtonChange:R,children:(0,eX.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,eX.jsx)(eU.sG.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});tB.displayName="SelectItemAlignedPosition";var tW=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...i}=e,l=tS(r);return(0,eX.jsx)(te,{...l,...i,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});tW.displayName="SelectPopperPosition";var[tz,tG]=tb(tN,{}),tV="SelectViewport",tK=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:o,...i}=e,a=t_(tV,r),c=tG(tV,r),u=(0,s.s)(t,a.onViewportChange),d=n.useRef(0);return(0,eX.jsxs)(eX.Fragment,{children:[(0,eX.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,eX.jsx)(tg.Slot,{scope:r,children:(0,eX.jsx)(eU.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=c;if(n?.current&&r){let e=Math.abs(d.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let i=o+e,l=Math.min(n,i),a=i-l;r.style.height=l+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});tK.displayName=tV;var tq="SelectGroup",[tU,tX]=tb(tq),t$=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,h.B)();return(0,eX.jsx)(tU,{scope:r,id:o,children:(0,eX.jsx)(eU.sG.div,{role:"group","aria-labelledby":o,...n,ref:t})})});t$.displayName=tq;var tY="SelectLabel",tZ=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=tX(tY,r);return(0,eX.jsx)(eU.sG.div,{id:o.id,...n,ref:t})});tZ.displayName=tY;var tJ="SelectItem",[tQ,t0]=tb(tJ),t1=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,disabled:i=!1,textValue:a,...c}=e,u=tC(tJ,r),d=t_(tJ,r),f=u.value===o,[p,m]=n.useState(a??""),[v,g]=n.useState(!1),y=(0,s.s)(t,e=>d.itemRefCallback?.(e,o,i)),w=(0,h.B)(),b=n.useRef("touch"),x=()=>{i||(u.onValueChange(o),u.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,eX.jsx)(tQ,{scope:r,value:o,disabled:i,textId:w,isSelected:f,onItemTextChange:n.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,eX.jsx)(tg.ItemSlot,{scope:r,value:o,disabled:i,textValue:p,children:(0,eX.jsx)(eU.sG.div,{role:"option","aria-labelledby":w,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:y,onFocus:(0,l.m)(c.onFocus,()=>g(!0)),onBlur:(0,l.m)(c.onBlur,()=>g(!1)),onClick:(0,l.m)(c.onClick,()=>{"mouse"!==b.current&&x()}),onPointerUp:(0,l.m)(c.onPointerUp,()=>{"mouse"===b.current&&x()}),onPointerDown:(0,l.m)(c.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.m)(c.onPointerMove,e=>{b.current=e.pointerType,i?d.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(c.onPointerLeave,e=>{e.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:(0,l.m)(c.onKeyDown,e=>{(d.searchRef?.current===""||" "!==e.key)&&(tm.includes(e.key)&&x()," "===e.key&&e.preventDefault())})})})})});t1.displayName=tJ;var t2="SelectItemText",t6=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:i,style:l,...a}=e,c=tC(t2,r),u=t_(t2,r),d=t0(t2,r),f=tA(t2,r),[p,h]=n.useState(null),m=(0,s.s)(t,e=>h(e),d.onItemTextChange,e=>u.itemTextRefCallback?.(e,d.value,d.disabled)),v=p?.textContent,g=n.useMemo(()=>(0,eX.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:w}=f;return(0,eZ.N)(()=>(y(g),()=>w(g)),[y,w,g]),(0,eX.jsxs)(eX.Fragment,{children:[(0,eX.jsx)(eU.sG.span,{id:d.textId,...a,ref:m}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?o.createPortal(a.children,c.valueNode):null]})});t6.displayName=t2;var t3="SelectItemIndicator",t4=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return t0(t3,r).isSelected?(0,eX.jsx)(eU.sG.span,{"aria-hidden":!0,...n,ref:t}):null});t4.displayName=t3;var t5="SelectScrollUpButton",t8=n.forwardRef((e,t)=>{let r=t_(t5,e.__scopeSelect),o=tG(t5,e.__scopeSelect),[i,l]=n.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,eZ.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){l(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),i?(0,eX.jsx)(re,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});t8.displayName=t5;var t9="SelectScrollDownButton",t7=n.forwardRef((e,t)=>{let r=t_(t9,e.__scopeSelect),o=tG(t9,e.__scopeSelect),[i,l]=n.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,eZ.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),i?(0,eX.jsx)(re,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});t7.displayName=t9;var re=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:o,...i}=e,a=t_("SelectScrollButton",r),s=n.useRef(null),c=ty(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,eZ.N)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,eX.jsx)(eU.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{u()})})}),rt=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,eX.jsx)(eU.sG.div,{"aria-hidden":!0,...n,ref:t})});rt.displayName="SelectSeparator";var rr="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=tS(r),i=tC(rr,r),l=t_(rr,r);return i.open&&"popper"===l.position?(0,eX.jsx)(tn,{...o,...n,ref:t}):null}).displayName=rr;var rn=n.forwardRef(({__scopeSelect:e,value:t,...r},o)=>{let i=n.useRef(null),l=(0,s.s)(o,i),a=(0,tu.Z)(t);return n.useEffect(()=>{let e=i.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[a,t]),(0,eX.jsx)(eU.sG.select,{...r,style:{...td.Qg,...r.style},ref:l,defaultValue:t})});function ro(e){return""===e||void 0===e}function ri(e){let t=(0,eY.c)(e),r=n.useRef(""),o=n.useRef(0),i=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),l=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,i,l]}function rl(e,t,r){var n,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=r?e.indexOf(r):-1,a=(n=e,o=Math.max(l,0),n.map((e,t)=>n[(o+t)%n.length]));1===i.length&&(a=a.filter(e=>e!==r));let s=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==r?s:void 0}rn.displayName="SelectBubbleInput";var ra=tk,rs=tj,rc=tD,ru=tL,rd=tM,rf=tI,rp=tK,rh=t$,rm=tZ,rv=t1,rg=t6,ry=t4,rw=t8,rb=t7,rx=rt},29272:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("ListOrdered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},32547:(e,t,r)=>{r.d(t,{n:()=>d});var n=r(43210),o=r(98599),i=r(14163),l=r(13495),a=r(60687),s="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",u={bubbles:!1,cancelable:!0},d=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:d=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=n.useState(null),x=(0,l.c)(v),S=(0,l.c)(g),E=n.useRef(null),C=(0,o.s)(t,e=>b(e)),R=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(d){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?E.current=t:h(E.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||h(E.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&r.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[d,w,R.paused]),n.useEffect(()=>{if(w){m.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(s,u);w.addEventListener(s,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(h(n,{select:t}),document.activeElement!==r)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(s,x),setTimeout(()=>{let t=new CustomEvent(c,u);w.addEventListener(c,S),w.dispatchEvent(t),t.defaultPrevented||h(e??document.body,{select:!0}),w.removeEventListener(c,S),m.remove(R)},0)}}},[w,x,S,R]);let A=n.useCallback(e=>{if(!r&&!d||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||n!==i?e.shiftKey&&n===o&&(e.preventDefault(),r&&h(i,{select:!0})):(e.preventDefault(),r&&h(o,{select:!0})):n===t&&e.preventDefault()}},[r,d,R.paused]);return(0,a.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:A})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function h(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}d.displayName="FocusScope";var m=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},33886:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},40196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("SquarePlus",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},40211:(e,t,r)=>{r.d(t,{C1:()=>E,bL:()=>x});var n=r(43210),o=r(98599),i=r(11273),l=r(70569),a=r(65551),s=r(83721),c=r(18853),u=r(46059),d=r(14163),f=r(60687),p="Checkbox",[h,m]=(0,i.A)(p),[v,g]=h(p);function y(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:i,disabled:l,form:s,name:c,onCheckedChange:u,required:d,value:h="on",internal_do_not_use_render:m}=e,[g,y]=(0,a.i)({prop:r,defaultProp:i??!1,onChange:u,caller:p}),[w,b]=n.useState(null),[x,S]=n.useState(null),E=n.useRef(!1),C=!w||!!s||!!w.closest("form"),R={checked:g,disabled:l,setChecked:y,control:w,setControl:b,name:c,form:s,value:h,hasConsumerStoppedPropagationRef:E,required:d,defaultChecked:!A(i)&&i,isFormControl:C,bubbleInput:x,setBubbleInput:S};return(0,f.jsx)(v,{scope:t,...R,children:"function"==typeof m?m(R):o})}var w="CheckboxTrigger",b=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...i},a)=>{let{control:s,value:c,disabled:u,checked:p,required:h,setControl:m,setChecked:v,hasConsumerStoppedPropagationRef:y,isFormControl:b,bubbleInput:x}=g(w,e),S=(0,o.s)(a,m),E=n.useRef(p);return n.useEffect(()=>{let e=s?.form;if(e){let t=()=>v(E.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[s,v]),(0,f.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":A(p)?"mixed":p,"aria-required":h,"data-state":k(p),"data-disabled":u?"":void 0,disabled:u,value:c,...i,ref:S,onKeyDown:(0,l.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(r,e=>{v(e=>!!A(e)||!e),x&&b&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});b.displayName=w;var x=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:i,required:l,disabled:a,value:s,onCheckedChange:c,form:u,...d}=e;return(0,f.jsx)(y,{__scopeCheckbox:r,checked:o,defaultChecked:i,disabled:a,required:l,onCheckedChange:c,name:n,form:u,value:s,internal_do_not_use_render:({isFormControl:e})=>(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(b,{...d,ref:t,__scopeCheckbox:r}),e&&(0,f.jsx)(R,{__scopeCheckbox:r})]})})});x.displayName=p;var S="CheckboxIndicator",E=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,i=g(S,r);return(0,f.jsx)(u.C,{present:n||A(i.checked)||!0===i.checked,children:(0,f.jsx)(d.sG.span,{"data-state":k(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});E.displayName=S;var C="CheckboxBubbleInput",R=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:i,hasConsumerStoppedPropagationRef:l,checked:a,defaultChecked:u,required:p,disabled:h,name:m,value:v,form:y,bubbleInput:w,setBubbleInput:b}=g(C,e),x=(0,o.s)(r,b),S=(0,s.Z)(a),E=(0,c.X)(i);n.useEffect(()=>{if(!w)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(S!==a&&e){let r=new Event("click",{bubbles:t});w.indeterminate=A(a),e.call(w,!A(a)&&a),w.dispatchEvent(r)}},[w,S,a,l]);let R=n.useRef(!A(a)&&a);return(0,f.jsx)(d.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:u??R.current,required:p,disabled:h,name:m,value:v,form:y,...t,tabIndex:-1,ref:x,style:{...t.style,...E,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(e){return"indeterminate"===e}function k(e){return A(e)?"indeterminate":e?"checked":"unchecked"}R.displayName=C},41360:(e,t,r)=>{r.d(t,{UC:()=>$,B8:()=>U,bL:()=>q,l9:()=>X});var n=r(43210),o=r(70569),i=r(11273),l=r(9510),a=r(98599),s=r(96963),c=r(14163),u=r(13495),d=r(65551),f=r(43),p=r(60687),h="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,y,w]=(0,l.N)(v),[b,x]=(0,i.A)(v,[w]),[S,E]=b(v),C=n.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(R,{...e,ref:t})})}));C.displayName=v;var R=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:l=!1,dir:s,currentTabStopId:g,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:b,onEntryFocus:x,preventScrollOnEntryFocus:E=!1,...C}=e,R=n.useRef(null),A=(0,a.s)(t,R),k=(0,f.jH)(s),[T,P]=(0,d.i)({prop:g,defaultProp:w??null,onChange:b,caller:v}),[D,L]=n.useState(!1),M=(0,u.c)(x),N=y(r),I=n.useRef(!1),[O,_]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(h,M),()=>e.removeEventListener(h,M)},[M]),(0,p.jsx)(S,{scope:r,orientation:i,dir:k,loop:l,currentTabStopId:T,onItemFocus:n.useCallback(e=>P(e),[P]),onItemShiftTab:n.useCallback(()=>L(!0),[]),onFocusableItemAdd:n.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>_(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:D||0===O?-1:0,"data-orientation":i,...C,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{I.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!I.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(h,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=N().filter(e=>e.focusable);j([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),E)}}I.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>L(!1))})})}),A="RovingFocusGroupItem",k=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:l=!1,tabStopId:a,children:u,...d}=e,f=(0,s.B)(),h=a||f,m=E(A,r),v=m.currentTabStopId===h,w=y(r),{onFocusableItemAdd:b,onFocusableItemRemove:x,currentTabStopId:S}=m;return n.useEffect(()=>{if(i)return b(),()=>x()},[i,b,x]),(0,p.jsx)(g.ItemSlot,{scope:r,id:h,focusable:i,active:l,children:(0,p.jsx)(c.sG.span,{tabIndex:v?0:-1,"data-orientation":m.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?m.onItemFocus(h):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(h)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return T[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>j(r))}}),children:"function"==typeof u?u({isCurrentTabStop:v,hasTabStop:null!=S}):u})})});k.displayName=A;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function j(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var P=r(46059),D="Tabs",[L,M]=(0,i.A)(D,[x]),N=x(),[I,O]=L(D),_=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:i,orientation:l="horizontal",dir:a,activationMode:u="automatic",...h}=e,m=(0,f.jH)(a),[v,g]=(0,d.i)({prop:n,onChange:o,defaultProp:i??"",caller:D});return(0,p.jsx)(I,{scope:r,baseId:(0,s.B)(),value:v,onValueChange:g,orientation:l,dir:m,activationMode:u,children:(0,p.jsx)(c.sG.div,{dir:m,"data-orientation":l,...h,ref:t})})});_.displayName=D;var F="TabsList",H=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,i=O(F,r),l=N(r);return(0,p.jsx)(C,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:n,children:(0,p.jsx)(c.sG.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});H.displayName=F;var B="TabsTrigger",W=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...l}=e,a=O(B,r),s=N(r),u=V(a.baseId,n),d=K(a.baseId,n),f=n===a.value;return(0,p.jsx)(k,{asChild:!0,...s,focusable:!i,active:f,children:(0,p.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...l,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():a.onValueChange(n)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&a.onValueChange(n)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==a.activationMode;f||i||!e||a.onValueChange(n)})})})});W.displayName=B;var z="TabsContent",G=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:i,children:l,...a}=e,s=O(z,r),u=V(s.baseId,o),d=K(s.baseId,o),f=o===s.value,h=n.useRef(f);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(P.C,{present:i||f,children:({present:r})=>(0,p.jsx)(c.sG.div,{"data-state":f?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:d,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&l})})});function V(e,t){return`${e}-trigger-${t}`}function K(e,t){return`${e}-content-${t}`}G.displayName=z;var q=_,U=H,X=W,$=G},41936:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},42247:(e,t,r)=>{r.d(t,{A:()=>K});var n,o=function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function i(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,r(43210)),a="right-scroll-bar-position",s="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var u="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,d=new WeakMap;function f(e){return e}var p=function(e){void 0===e&&(e={});var t,r,n,i,l=(t=null,void 0===r&&(r=f),n=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,i);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(i=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){i=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var o=function(){var r=t;t=[],r.forEach(e)},l=function(){return Promise.resolve().then(o)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}});return l.options=o({async:!0,ssr:!1},e),l}(),h=function(){},m=l.forwardRef(function(e,t){var r,n,a,s,f=l.useRef(null),m=l.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),v=m[0],g=m[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,S=e.enabled,E=e.shards,C=e.sideCar,R=e.noRelative,A=e.noIsolation,k=e.inert,T=e.allowPinchZoom,j=e.as,P=e.gapMode,D=i(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=(r=[f,t],n=function(e){return r.forEach(function(t){return c(t,e)})},(a=(0,l.useState)(function(){return{value:null,callback:n,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=n,s=a.facade,u(function(){var e=d.get(s);if(e){var t=new Set(e),n=new Set(r),o=s.current;t.forEach(function(e){n.has(e)||c(e,null)}),n.forEach(function(e){t.has(e)||c(e,o)})}d.set(s,r)},[r]),s),M=o(o({},D),v);return l.createElement(l.Fragment,null,S&&l.createElement(C,{sideCar:p,removeScrollBar:x,shards:E,noRelative:R,noIsolation:A,inert:k,setCallbacks:g,allowPinchZoom:!!T,lockRef:f,gapMode:P}),y?l.cloneElement(l.Children.only(w),o(o({},M),{ref:L})):l.createElement(void 0===j?"div":j,o({},M,{className:b,ref:L}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:s,zeroRight:a};var v=function(e){var t=e.sideCar,r=i(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return l.createElement(n,o({},r))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=n||r.nc;return t&&e.setAttribute("nonce",t),e}())){var i,l;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},y=function(){var e=g();return function(t,r){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},w=function(){var e=y();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(r),x(n),x(o)]},E=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=S(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},C=w(),R="data-scroll-locked",A=function(e,t,r,n){var o=e.left,i=e.top,l=e.right,c=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(c,"px ").concat(n,";\n  }\n  body[").concat(R,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(c,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(c,"px ").concat(n,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(c,"px ").concat(n,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(R,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},k=function(){var e=parseInt(document.body.getAttribute(R)||"0",10);return isFinite(e)?e:0},T=function(){l.useEffect(function(){return document.body.setAttribute(R,(k()+1).toString()),function(){var e=k()-1;e<=0?document.body.removeAttribute(R):document.body.setAttribute(R,e.toString())}},[])},j=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;T();var i=l.useMemo(function(){return E(o)},[o]);return l.createElement(C,{styles:A(i,!t,o,r?"":"!important")})},P=!1;if("undefined"!=typeof window)try{var D=Object.defineProperty({},"passive",{get:function(){return P=!0,!0}});window.addEventListener("test",D,D),window.removeEventListener("test",D,D)}catch(e){P=!1}var L=!!P&&{passive:!1},M=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},N=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),I(e,n)){var o=O(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},I=function(e,t){return"v"===e?M(t,"overflowY"):M(t,"overflowX")},O=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,r,n,o){var i,l=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),a=l*n,s=r.target,c=t.contains(s),u=!1,d=a>0,f=0,p=0;do{if(!s)break;var h=O(e,s),m=h[0],v=h[1]-h[2]-l*m;(m||v)&&I(e,s)&&(f+=v,p+=m);var g=s.parentNode;s=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&a>f)?u=!0:!d&&(o&&1>Math.abs(p)||!o&&-a>p)&&(u=!0),u},F=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},H=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},W=0,z=[];let G=(p.useMedium(function(e){var t=l.useRef([]),r=l.useRef([0,0]),n=l.useRef(),o=l.useState(W++)[0],i=l.useState(w)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=F(e),l=r.current,s="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],u=e.target,d=Math.abs(s)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=N(d,u);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=N(d,u)),!f)return!1;if(!n.current&&"changedTouches"in e&&(s||c)&&(n.current=o),!o)return!0;var p=n.current||o;return _(p,t,e,"h"===p?s:c,!0)},[]),c=l.useCallback(function(e){if(z.length&&z[z.length-1]===i){var r="deltaY"in e?H(e):F(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(a.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),u=l.useCallback(function(e,r,n,o){var i={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){r.current=F(e),n.current=void 0},[]),f=l.useCallback(function(t){u(t.type,H(t),t.target,s(t,e.lockRef.current))},[]),p=l.useCallback(function(t){u(t.type,F(t),t.target,s(t,e.lockRef.current))},[]);l.useEffect(function(){return z.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,L),document.addEventListener("touchmove",c,L),document.addEventListener("touchstart",d,L),function(){z=z.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,L),document.removeEventListener("touchmove",c,L),document.removeEventListener("touchstart",d,L)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(j,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),v);var V=l.forwardRef(function(e,t){return l.createElement(m,o({},e,{ref:t,sideCar:G}))});V.classNames=m.classNames;let K=V},44610:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},49497:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},58369:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},58450:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},59892:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Bitcoin",[["path",{d:"M11.767 19.089c4.924.868 6.14-6.025 1.216-6.894m-1.216 6.894L5.86 18.047m5.908 1.042-.347 1.97m1.563-8.864c4.924.869 6.14-6.025 1.215-6.893m-1.215 6.893-3.94-.694m5.155-6.2L8.29 4.26m5.908 1.042.348-1.97M7.48 20.364l3.126-17.727",key:"yr8idg"}]])},61662:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},62369:(e,t,r)=>{r.d(t,{b:()=>c});var n=r(43210),o=r(14163),i=r(60687),l="horizontal",a=["horizontal","vertical"],s=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:s=l,...c}=e,u=(r=s,a.includes(r))?s:l;return(0,i.jsx)(o.sG.div,{"data-orientation":u,...n?{role:"none"}:{"aria-orientation":"vertical"===u?u:void 0,role:"separator"},...c,ref:t})});s.displayName="Separator";var c=s},63376:(e,t,r)=>{r.d(t,{Eq:()=>u});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,l={},a=0,s=function(e){return e&&(e.host||s(e.parentNode))},c=function(e,t,r,n){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=s(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[r]||(l[r]=new WeakMap);var u=l[r],d=[],f=new Set,p=new Set(c),h=function(e){!(!e||f.has(e))&&(f.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(n),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,s=(u.get(e)||0)+1;o.set(e,a),u.set(e,s),d.push(e),1===a&&l&&i.set(e,!0),1===s&&e.setAttribute(r,"true"),l||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,l=u.get(e)-1;o.set(e,t),u.set(e,l),t||(i.has(e)||e.removeAttribute(n),i.delete(e)),l||e.removeAttribute(r)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},u=function(e,t,r){void 0===r&&(r="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||n(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,r,"aria-hidden")):function(){return null}}},67969:(e,t,r)=>{r.d(t,{q:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},68123:(e,t,r)=>{r.d(t,{LM:()=>X,OK:()=>$,VM:()=>E,bL:()=>U,lr:()=>N});var n=r(43210),o=r(14163),i=r(46059),l=r(11273),a=r(98599),s=r(13495),c=r(43),u=r(66156),d=r(67969),f=r(70569),p=r(60687),h="ScrollArea",[m,v]=(0,l.A)(h),[g,y]=m(h),w=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:i="hover",dir:l,scrollHideDelay:s=600,...u}=e,[d,f]=n.useState(null),[h,m]=n.useState(null),[v,y]=n.useState(null),[w,b]=n.useState(null),[x,S]=n.useState(null),[E,C]=n.useState(0),[R,A]=n.useState(0),[k,T]=n.useState(!1),[j,P]=n.useState(!1),D=(0,a.s)(t,e=>f(e)),L=(0,c.jH)(l);return(0,p.jsx)(g,{scope:r,type:i,dir:L,scrollHideDelay:s,scrollArea:d,viewport:h,onViewportChange:m,content:v,onContentChange:y,scrollbarX:w,onScrollbarXChange:b,scrollbarXEnabled:k,onScrollbarXEnabledChange:T,scrollbarY:x,onScrollbarYChange:S,scrollbarYEnabled:j,onScrollbarYEnabledChange:P,onCornerWidthChange:C,onCornerHeightChange:A,children:(0,p.jsx)(o.sG.div,{dir:L,...u,ref:D,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":R+"px",...e.style}})})});w.displayName=h;var b="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:i,nonce:l,...s}=e,c=y(b,r),u=n.useRef(null),d=(0,a.s)(t,u,c.onViewportChange);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,p.jsx)(o.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,p.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});x.displayName=b;var S="ScrollAreaScrollbar",E=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=y(S,e.__scopeScrollArea),{onScrollbarXEnabledChange:l,onScrollbarYEnabledChange:a}=i,s="horizontal"===e.orientation;return n.useEffect(()=>(s?l(!0):a(!0),()=>{s?l(!1):a(!1)}),[s,l,a]),"hover"===i.type?(0,p.jsx)(C,{...o,ref:t,forceMount:r}):"scroll"===i.type?(0,p.jsx)(R,{...o,ref:t,forceMount:r}):"auto"===i.type?(0,p.jsx)(A,{...o,ref:t,forceMount:r}):"always"===i.type?(0,p.jsx)(k,{...o,ref:t}):null});E.displayName=S;var C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=y(S,e.__scopeScrollArea),[a,s]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,p.jsx)(i.C,{present:r||a,children:(0,p.jsx)(A,{"data-state":a?"visible":"hidden",...o,ref:t})})}),R=n.forwardRef((e,t)=>{var r;let{forceMount:o,...l}=e,a=y(S,e.__scopeScrollArea),s="horizontal"===e.orientation,c=K(()=>d("SCROLL_END"),100),[u,d]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>r[e][t]??e,"hidden"));return n.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>d("HIDE"),a.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,a.scrollHideDelay,d]),n.useEffect(()=>{let e=a.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(d("SCROLL"),c()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[a.viewport,s,d,c]),(0,p.jsx)(i.C,{present:o||"hidden"!==u,children:(0,p.jsx)(k,{"data-state":"hidden"===u?"hidden":"visible",...l,ref:t,onPointerEnter:(0,f.m)(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:(0,f.m)(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),A=n.forwardRef((e,t)=>{let r=y(S,e.__scopeScrollArea),{forceMount:o,...l}=e,[a,s]=n.useState(!1),c="horizontal"===e.orientation,u=K(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(c?e:t)}},10);return q(r.viewport,u),q(r.content,u),(0,p.jsx)(i.C,{present:o||a,children:(0,p.jsx)(k,{"data-state":a?"visible":"hidden",...l,ref:t})})}),k=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,i=y(S,e.__scopeScrollArea),l=n.useRef(null),a=n.useRef(0),[s,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=B(s.viewport,s.content),d={...o,sizes:s,onSizesChange:c,hasThumb:!!(u>0&&u<1),onThumbChange:e=>l.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function f(e,t){return function(e,t,r,n="ltr"){let o=W(r),i=t||o/2,l=r.scrollbar.paddingStart+i,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-i),s=r.content-r.viewport;return G([l,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===r?(0,p.jsx)(T,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&l.current){let e=z(i.viewport.scrollLeft,s,i.dir);l.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=f(e,i.dir))}}):"vertical"===r?(0,p.jsx)(j,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&l.current){let e=z(i.viewport.scrollTop,s);l.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=f(e))}}):null}),T=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,l=y(S,e.__scopeScrollArea),[s,c]=n.useState(),u=n.useRef(null),d=(0,a.s)(t,u,l.onScrollbarXChange);return n.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,p.jsx)(L,{"data-orientation":"horizontal",...i,ref:d,sizes:r,style:{bottom:0,left:"rtl"===l.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===l.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":W(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{u.current&&l.viewport&&s&&o({content:l.viewport.scrollWidth,viewport:l.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:H(s.paddingLeft),paddingEnd:H(s.paddingRight)}})}})}),j=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,l=y(S,e.__scopeScrollArea),[s,c]=n.useState(),u=n.useRef(null),d=(0,a.s)(t,u,l.onScrollbarYChange);return n.useEffect(()=>{u.current&&c(getComputedStyle(u.current))},[u]),(0,p.jsx)(L,{"data-orientation":"vertical",...i,ref:d,sizes:r,style:{top:0,right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":W(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{u.current&&l.viewport&&s&&o({content:l.viewport.scrollHeight,viewport:l.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:H(s.paddingTop),paddingEnd:H(s.paddingBottom)}})}})}),[P,D]=m(S),L=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:i,hasThumb:l,onThumbChange:c,onThumbPointerUp:u,onThumbPointerDown:d,onThumbPositionChange:h,onDragScroll:m,onWheelScroll:v,onResize:g,...w}=e,b=y(S,r),[x,E]=n.useState(null),C=(0,a.s)(t,e=>E(e)),R=n.useRef(null),A=n.useRef(""),k=b.viewport,T=i.content-i.viewport,j=(0,s.c)(v),D=(0,s.c)(h),L=K(g,10);function M(e){R.current&&m({x:e.clientX-R.current.left,y:e.clientY-R.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&j(e,T)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[k,x,T,j]),n.useEffect(D,[i,D]),q(x,L),q(b.content,L),(0,p.jsx)(P,{scope:r,scrollbar:x,hasThumb:l,onThumbChange:(0,s.c)(c),onThumbPointerUp:(0,s.c)(u),onThumbPositionChange:D,onThumbPointerDown:(0,s.c)(d),children:(0,p.jsx)(o.sG.div,{...w,ref:C,style:{position:"absolute",...w.style},onPointerDown:(0,f.m)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),R.current=x.getBoundingClientRect(),A.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),M(e))}),onPointerMove:(0,f.m)(e.onPointerMove,M),onPointerUp:(0,f.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=A.current,b.viewport&&(b.viewport.style.scrollBehavior=""),R.current=null})})})}),M="ScrollAreaThumb",N=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=D(M,e.__scopeScrollArea);return(0,p.jsx)(i.C,{present:r||o.hasThumb,children:(0,p.jsx)(I,{ref:t,...n})})}),I=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:i,...l}=e,s=y(M,r),c=D(M,r),{onThumbPositionChange:u}=c,d=(0,a.s)(t,e=>c.onThumbChange(e)),h=n.useRef(void 0),m=K(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{m(),h.current||(h.current=V(e,u),u())};return u(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,m,u]),(0,p.jsx)(o.sG.div,{"data-state":c.hasThumb?"visible":"hidden",...l,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,f.m)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;c.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,f.m)(e.onPointerUp,c.onThumbPointerUp)})});N.displayName=M;var O="ScrollAreaCorner",_=n.forwardRef((e,t)=>{let r=y(O,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,p.jsx)(F,{...e,ref:t}):null});_.displayName=O;var F=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...i}=e,l=y(O,r),[a,s]=n.useState(0),[c,u]=n.useState(0),d=!!(a&&c);return q(l.scrollbarX,()=>{let e=l.scrollbarX?.offsetHeight||0;l.onCornerHeightChange(e),u(e)}),q(l.scrollbarY,()=>{let e=l.scrollbarY?.offsetWidth||0;l.onCornerWidthChange(e),s(e)}),d?(0,p.jsx)(o.sG.div,{...i,ref:t,style:{width:a,height:c,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function H(e){return e?parseInt(e,10):0}function B(e,t){let r=e/t;return isNaN(r)?0:r}function W(e){let t=B(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function z(e,t,r="ltr"){let n=W(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,l=t.content-t.viewport,a=(0,d.q)(e,"ltr"===r?[0,l]:[-1*l,0]);return G([0,l],[0,i-n])(a)}function G(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var V=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function o(){let i={left:e.scrollLeft,top:e.scrollTop},l=r.left!==i.left,a=r.top!==i.top;(l||a)&&t(),r=i,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function K(e,t){let r=(0,s.c)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function q(e,t){let r=(0,s.c)(t);(0,u.N)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var U=w,X=x,$=_},72963:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},81950:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]])},83721:(e,t,r)=>{r.d(t,{Z:()=>o});var n=r(43210);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},85866:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},89743:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},91840:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]])},92375:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])},96963:(e,t,r)=>{r.d(t,{B:()=>s});var n,o=r(43210),i=r(66156),l=(n||(n=r.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function s(e){let[t,r]=o.useState(l());return(0,i.N)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},99196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(82614).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};