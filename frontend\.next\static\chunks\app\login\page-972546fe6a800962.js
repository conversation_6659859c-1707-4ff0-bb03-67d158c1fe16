(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{3870:(e,t,r)=>{Promise.resolve().then(r.bind(r,58880))},4607:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(40157).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},17607:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(40157).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},25731:(e,t,r)=>{"use strict";r.d(t,{Rk:()=>l,ZQ:()=>n,oc:()=>o});let s="http://localhost:5000";async function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat(s).concat(e),a=localStorage.getItem("plutoAuthToken"),n={"Content-Type":"application/json",...a?{Authorization:"Bearer ".concat(a)}:{},...t.headers};try{let e;let s=new AbortController,a=setTimeout(()=>s.abort(),1e4),o=await fetch(r,{...t,headers:n,signal:s.signal}).finally(()=>clearTimeout(a));if(401===o.status)throw localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),window.location.href="/login",Error("Authentication expired. Please login again.");let l=o.headers.get("content-type");if(l&&l.includes("application/json"))e=await o.json();else{let t=await o.text();try{e=JSON.parse(t)}catch(r){e={message:t}}}if(!o.ok)throw console.error("API error response:",e),Error(e.error||e.message||"API error: ".concat(o.status));return e}catch(e){if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw console.error("Network error - Is the backend server running?:",e),Error("Cannot connect to server. Please check if the backend is running.");if("AbortError"===e.name)throw console.error("Request timeout:",e),Error("Request timed out. Server may be unavailable.");throw console.error("API request failed:",e),e}}console.log("API Base URL:",s);let n={login:async(e,t)=>{try{let r=await i(async()=>await a("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:t})}));if(r&&r.access_token)return localStorage.setItem("plutoAuthToken",r.access_token),localStorage.setItem("plutoAuth","true"),r.user&&localStorage.setItem("plutoUser",JSON.stringify(r.user)),!0;return!1}catch(e){return console.error("Login API error:",e),!1}},register:async(e,t,r)=>i(async()=>await a("/auth/register",{method:"POST",body:JSON.stringify({username:e,password:t,email:r})})),logout:async()=>(localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),!0)},o={getConfig:async e=>a(e?"/trading/config/".concat(e):"/trading/config"),saveConfig:async e=>a("/trading/config",{method:"POST",body:JSON.stringify(e)}),updateConfig:async(e,t)=>a("/trading/config/".concat(e),{method:"PUT",body:JSON.stringify(t)}),startBot:async e=>a("/trading/bot/start/".concat(e),{method:"POST"}),stopBot:async e=>a("/trading/bot/stop/".concat(e),{method:"POST"}),getBotStatus:async e=>a("/trading/bot/status/".concat(e)),getTradeHistory:async e=>a("/trading/history".concat(e?"?configId=".concat(e):"")),getBalances:async()=>a("/trading/balances"),getMarketPrice:async e=>a("/trading/market-data/".concat(e)),getTradingPairs:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return a("/trading/exchange/trading-pairs?exchange=".concat(e))},getCryptocurrencies:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return a("/trading/exchange/cryptocurrencies?exchange=".concat(e))}},l={getAllSessions:async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return a("/sessions/?include_inactive=".concat(e))},createSession:async e=>a("/sessions/",{method:"POST",body:JSON.stringify(e)}),getSession:async e=>a("/sessions/".concat(e)),updateSession:async(e,t)=>a("/sessions/".concat(e),{method:"PUT",body:JSON.stringify(t)}),deleteSession:async e=>a("/sessions/".concat(e),{method:"DELETE"}),activateSession:async e=>a("/sessions/".concat(e,"/activate"),{method:"POST"}),getSessionHistory:async e=>a("/sessions/".concat(e,"/history")),getActiveSession:async()=>a("/sessions/active")},i=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r=0,s=async()=>{try{return await e()}catch(e){if((e instanceof TypeError&&e.message.includes("Failed to fetch")||"AbortError"===e.name)&&r<t){let e=500*Math.pow(2,r);return console.log("Retrying after ".concat(e,"ms (attempt ").concat(r+1,"/").concat(t,")...")),r++,await new Promise(t=>setTimeout(t,e)),s()}throw e}};return s()}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var s=r(95155),a=r(12115),n=r(99708),o=r(74466),l=r(59434);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:o,asChild:c=!1,...d}=e,m=c?n.DX:"button";return(0,s.jsx)(m,{className:(0,l.cn)(i({variant:a,size:o,className:r})),ref:t,...d})});c.displayName="Button"},40283:(e,t,r)=>{"use strict";r.d(t,{A:()=>d,AuthProvider:()=>c});var s=r(95155),a=r(12115),n=r(35695),o=r(50172),l=r(25731);let i=(0,a.createContext)(void 0),c=e=>{let{children:t}=e,[r,c]=(0,a.useState)(!1),[d,m]=(0,a.useState)(!0),u=(0,n.useRouter)(),x=(0,n.usePathname)();(0,a.useEffect)(()=>{let e=localStorage.getItem("plutoAuth"),t=localStorage.getItem("plutoAuthToken");"true"===e&&t&&c(!0),m(!1)},[]),(0,a.useEffect)(()=>{d||r||"/login"===x?!d&&r&&"/login"===x&&u.push("/dashboard"):u.push("/login")},[r,d,x,u]);let h=async(e,t)=>{m(!0);try{if(await l.ZQ.login(e,t))return c(!0),u.push("/dashboard"),!0;return c(!1),!1}catch(e){return console.error("Login failed:",e),c(!1),!1}finally{m(!1)}},g=async()=>{try{await l.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{c(!1),u.push("/login")}};return!d||(null==x?void 0:x.startsWith("/_next/static/"))?r||"/login"===x||(null==x?void 0:x.startsWith("/_next/static/"))?(0,s.jsx)(i.Provider,{value:{isAuthenticated:r,login:h,logout:g,isLoading:d},children:t}):(0,s.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,s.jsx)(o.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,s.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting to login..."})]}):(0,s.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,s.jsx)(o.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,s.jsx)("p",{className:"ml-4 text-xl",children:"Loading Pluto..."})]})},d=()=>{let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},58880:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var s=r(95155),a=r(12115),n=r(40283),o=r(30285),l=r(62523),i=r(85057),c=r(66695),d=r(59435),m=r(50172),u=r(40157);let x=(0,u.A)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]),h=(0,u.A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var g=r(4607),p=r(17607),f=r(48639),y=r(32087),b=r(80659);let v=(0,u.A)("BotMessageSquare",[["path",{d:"M12 6V2H8",key:"1155em"}],["path",{d:"m8 18-4 4V8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2Z",key:"w2lp3e"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M9 11v2",key:"1ueba0"}],["path",{d:"M15 11v2",key:"i11awn"}],["path",{d:"M20 12h2",key:"1q8mjw"}]]),j=(0,u.A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]),N=(0,u.A)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var w=r(6874),k=r.n(w),S=r(25731);function A(){let[e,t]=(0,a.useState)(""),[r,u]=(0,a.useState)(""),[w,A]=(0,a.useState)(""),[P,C]=(0,a.useState)(!1),{login:T,isLoading:I,isAuthenticated:E}=(0,n.A)(),[R,M]=(0,a.useState)(""),[B,O]=(0,a.useState)(""),[L,q]=(0,a.useState)(!1),[F,J]=(0,a.useState)(!1),[_,U]=(0,a.useState)(new Date().getFullYear()),[Z,z]=(0,a.useState)("checking");(0,a.useEffect)(()=>{let e=async()=>{try{let e=await fetch("http://localhost:5000/health/",{method:"GET"});z(e.ok?"online":"offline")}catch(e){console.error("Backend connectivity check failed:",e),z("offline")}};e();let t=setInterval(e,1e4);return U(new Date().getFullYear()),()=>clearInterval(t)},[]);let H=async t=>{t.preventDefault(),M("");try{await T(e,r)||M("Invalid credentials. Try using testuser/password123")}catch(e){console.error("Login error:",e),M("Login failed. Please try again.")}},D=async s=>{if(s.preventDefault(),M(""),O(""),!e||!r||!w){M("All fields are required");return}try{await S.ZQ.register(e,r,w),O("Registration successful! You can now log in."),setTimeout(()=>{J(!1),q(!0),t(""),u(""),A(""),O("")},2e3)}catch(e){console.error("Registration error:",e),M(e.message||"Could not connect to server. Please try again later.")}},W=()=>{J(!1),q(!0),M(""),O("")},$=()=>{q(!1),J(!0),M(""),O("")};return I&&!E?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-background p-4",children:(0,s.jsx)(m.A,{className:"h-8 w-8 animate-spin text-primary"})}):E?null:(0,s.jsxs)("div",{className:"min-h-screen bg-background text-foreground flex flex-col",children:[(0,s.jsxs)("header",{className:"py-4 px-6 md:px-10 flex justify-between items-center border-b border-border",children:[(0,s.jsx)(d.A,{className:"text-2xl"}),(0,s.jsxs)("nav",{className:"hidden md:flex items-center space-x-6 text-sm",children:[(0,s.jsx)(k(),{href:"#",className:"hover:text-primary",children:"Home"}),(0,s.jsx)(k(),{href:"#",className:"hover:text-primary",children:"Features"}),(0,s.jsx)(k(),{href:"#",className:"hover:text-primary",children:"Pricing"}),(0,s.jsx)(k(),{href:"#",className:"hover:text-primary",children:"Contact"})]}),(0,s.jsxs)("div",{className:"space-x-2",children:[(0,s.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>W(),children:"Login"}),(0,s.jsx)(o.$,{className:"btn-neo",onClick:()=>$(),children:"Register"})]})]}),(0,s.jsxs)("main",{className:"flex-grow flex flex-col items-center justify-center text-center px-4 py-10 md:py-20",children:[(0,s.jsxs)("h1",{className:"text-4xl sm:text-5xl md:text-6xl font-bold mb-6",children:[(0,s.jsx)("span",{className:"text-primary",children:"Pluto"})," Trading Bot Platform"]}),(0,s.jsx)("p",{className:"text-lg md:text-xl text-muted-foreground max-w-2xl mb-10",children:"Automate your trading strategy with our powerful Simple Spot and Stablecoin Swap bots. Maximize your profits with minimal effort."}),!L&&!F&&(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4",children:[(0,s.jsxs)(o.$,{size:"lg",className:"btn-outline-neo text-lg px-8 py-4",onClick:()=>W(),children:[(0,s.jsx)(x,{className:"mr-2 h-5 w-5"})," Login to Trading Platform"]}),(0,s.jsxs)(o.$,{size:"lg",className:"btn-neo text-lg px-8 py-4",onClick:()=>$(),children:[(0,s.jsx)(h,{className:"mr-2 h-5 w-5"})," Create Free Account"]})]}),L&&(0,s.jsxs)(c.Zp,{className:"w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10",children:[(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)(c.ZB,{className:"text-3xl font-bold",children:"Account Login"}),(0,s.jsx)(c.BT,{children:"Access your Pluto Trading Bot dashboard."})]}),(0,s.jsx)(c.Wu,{children:(0,s.jsxs)("form",{onSubmit:H,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"username",className:"text-lg sr-only",children:"Username"}),(0,s.jsx)(l.p,{id:"username",type:"text",value:e,onChange:e=>t(e.target.value),required:!0,className:"text-base",placeholder:"Username"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"password",className:"text-lg sr-only",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.p,{id:"password",type:P?"text":"password",value:r,onChange:e=>u(e.target.value),required:!0,className:"text-base pr-10",placeholder:"Password (try: password123)"}),(0,s.jsx)("button",{type:"button",onClick:()=>C(!P),className:"absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground","aria-label":P?"Hide password":"Show password",children:P?(0,s.jsx)(g.A,{className:"h-5 w-5"}):(0,s.jsx)(p.A,{className:"h-5 w-5"})})]})]}),R&&(0,s.jsx)("p",{className:"text-destructive text-sm",children:R}),B&&(0,s.jsx)("p",{className:"text-green-500 text-sm",children:B}),(0,s.jsx)(o.$,{type:"submit",className:"w-full btn-neo text-lg py-3",disabled:I||"offline"===Z,children:I?(0,s.jsx)(m.A,{className:"mr-2 h-5 w-5 animate-spin"}):"Login"}),(0,s.jsxs)("div",{className:"flex items-center justify-center mt-2 text-sm",children:["checking"===Z&&(0,s.jsxs)("div",{className:"flex items-center text-orange-500",children:[(0,s.jsx)(m.A,{className:"h-3 w-3 mr-1 animate-spin"}),(0,s.jsx)("span",{children:"Checking server connection..."})]}),"online"===Z&&(0,s.jsxs)("div",{className:"flex items-center text-green-500",children:[(0,s.jsx)(f.A,{className:"h-3 w-3 mr-1"}),(0,s.jsx)("span",{children:"Server connected"})]}),"offline"===Z&&(0,s.jsxs)("div",{className:"flex items-center text-destructive",children:[(0,s.jsx)(y.A,{className:"h-3 w-3 mr-1"}),(0,s.jsx)("span",{children:"Server offline - Please start the backend"})]})]}),(0,s.jsxs)("div",{className:"text-center text-sm text-muted-foreground space-y-2 pt-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("a",{href:"#",className:"hover:text-primary underline",children:"Forgot password?"})," (Simulated)"]}),(0,s.jsxs)("p",{children:["Don't have an account? ",(0,s.jsx)("button",{type:"button",onClick:$,className:"hover:text-primary underline",children:"Create Account"})]})]})]})})]}),F&&(0,s.jsxs)(c.Zp,{className:"w-full max-w-md border-2 border-border shadow-2xl hard-shadow mt-10",children:[(0,s.jsxs)(c.aR,{className:"text-center",children:[(0,s.jsx)(c.ZB,{className:"text-3xl font-bold",children:"Create Account"}),(0,s.jsx)(c.BT,{children:"Join Pluto Trading Bot platform."})]}),(0,s.jsx)(c.Wu,{children:(0,s.jsxs)("form",{onSubmit:D,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"reg-username",className:"text-lg",children:"Username"}),(0,s.jsx)(l.p,{id:"reg-username",type:"text",value:e,onChange:e=>t(e.target.value),required:!0,className:"text-base",placeholder:"Choose a username"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"reg-email",className:"text-lg",children:"Email"}),(0,s.jsx)(l.p,{id:"reg-email",type:"email",value:w,onChange:e=>A(e.target.value),required:!0,className:"text-base",placeholder:"Your email address"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"reg-password",className:"text-lg",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.p,{id:"reg-password",type:P?"text":"password",value:r,onChange:e=>u(e.target.value),required:!0,className:"text-base pr-10",placeholder:"Create a password"}),(0,s.jsx)("button",{type:"button",onClick:()=>C(!P),className:"absolute inset-y-0 right-0 px-3 flex items-center text-muted-foreground hover:text-foreground","aria-label":P?"Hide password":"Show password",children:P?(0,s.jsx)(g.A,{className:"h-5 w-5"}):(0,s.jsx)(p.A,{className:"h-5 w-5"})})]})]}),R&&(0,s.jsx)("p",{className:"text-destructive text-sm",children:R}),B&&(0,s.jsx)("p",{className:"text-green-500 text-sm",children:B}),(0,s.jsx)(o.$,{type:"submit",className:"w-full btn-neo text-lg py-3",disabled:I,children:I?(0,s.jsx)(m.A,{className:"mr-2 h-5 w-5 animate-spin"}):"Register"}),(0,s.jsx)("div",{className:"text-center text-sm text-muted-foreground pt-2",children:(0,s.jsxs)("p",{children:["Already have an account? ",(0,s.jsx)("button",{type:"button",onClick:W,className:"hover:text-primary underline",children:"Login"})]})})]})})]})]}),(0,s.jsx)("section",{className:"py-10 md:py-16 bg-card border-t border-border",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold mb-8 text-primary",children:"Trading Strategies"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-12",children:[(0,s.jsxs)("div",{className:"border-2 border-border p-6 rounded-sm bg-background/30",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-3",children:"Simple Spot Mode"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Execute direct buy and sell orders based on your target prices. Ideal for straightforward market participation and capitalizing on price movements."})]}),(0,s.jsxs)("div",{className:"border-2 border-border p-6 rounded-sm bg-background/30",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-3",children:"Stablecoin Swap Mode"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Leverage stablecoins in your trading strategy, aiming for value preservation while capitalizing on market volatility against your chosen crypto assets."})]})]}),(0,s.jsx)("h2",{className:"text-3xl font-bold mb-8 text-primary mt-16",children:"Why Pluto?"}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto mb-12",children:[{icon:(0,s.jsx)(b.A,{className:"h-10 w-10 text-primary mb-3"}),title:"Automated Profits",description:"24/7 trading execution, so you never miss an opportunity."},{icon:(0,s.jsx)(v,{className:"h-10 w-10 text-primary mb-3"}),title:"AI-Powered Insights",description:"Smart suggestions to help you choose the best trading mode."},{icon:(0,s.jsx)(j,{className:"h-10 w-10 text-primary mb-3"}),title:"Dual Strategy Modes",description:"Flexible Simple Spot and Stablecoin Swap options to fit your style."},{icon:(0,s.jsx)(N,{className:"h-10 w-10 text-primary mb-3"}),title:"Secure Simulation",description:"Test strategies risk-free in a simulated environment."}].map(e=>(0,s.jsxs)("div",{className:"border-2 border-border p-6 rounded-sm bg-background/30 flex flex-col items-center",children:[e.icon,(0,s.jsx)("h3",{className:"text-xl font-semibold text-foreground mb-2",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description})]},e.title))}),(0,s.jsx)("h2",{className:"text-3xl font-bold mb-8 text-primary mt-16",children:"What Our Users Say"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,s.jsx)(c.Zp,{className:"border-2 border-border bg-background/30 p-6 text-left",children:(0,s.jsxs)(c.Wu,{className:"p-0",children:[(0,s.jsx)("blockquote",{className:"text-muted-foreground italic",children:'"Pluto\'s Simple Spot mode helped me capture quick profits effortlessly! The interface is so intuitive."'}),(0,s.jsx)("p",{className:"text-right font-semibold text-primary mt-3",children:"- TraderX (Simulated)"})]})}),(0,s.jsx)(c.Zp,{className:"border-2 border-border bg-background/30 p-6 text-left",children:(0,s.jsxs)(c.Wu,{className:"p-0",children:[(0,s.jsx)("blockquote",{className:"text-muted-foreground italic",children:'"The Stablecoin Swap is perfect for my long-term strategy. Love the AI suggestions for mode selection!"'}),(0,s.jsx)("p",{className:"text-right font-semibold text-primary mt-3",children:"- Crypto Enthusiast (Simulated)"})]})})]})]})}),(0,s.jsxs)("footer",{className:"py-6 text-center text-sm text-muted-foreground border-t border-border",children:["\xa9 ",_," Pluto Trading. All Rights Reserved (Simulation)."]})]})}},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(52596),a=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},59435:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(95155),a=r(12115),n=r(49502);let o=e=>{let{className:t,useFullName:r=!0}=e,[o,l]=(0,a.useState)(!1);return(0,s.jsxs)("div",{className:"flex items-center text-2xl font-bold text-primary ".concat(t),children:[o?(0,s.jsx)(n.A,{className:"mr-2 h-7 w-7"}):(0,s.jsx)("img",{src:"https://i.imgur.com/Q0HDcMH.png",alt:"Pluto Trading Bot Logo",className:"mr-2 h-7 w-7 rounded-full object-cover",onError:()=>{l(!0)},onLoad:()=>console.log("Pluto logo loaded successfully")}),(0,s.jsxs)("span",{children:["Pluto",r?" Trading Bot":""]})]})}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var s=r(95155),a=r(12115),n=r(59434);let o=a.forwardRef((e,t)=>{let{className:r,type:a,...o}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...o})});o.displayName="Input"},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>i,Zp:()=>o,aR:()=>l});var s=r(95155),a=r(12115),n=r(59434);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",r),...a})});o.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-4 md:p-6",r),...a})});l.displayName="CardHeader";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-xl font-semibold leading-none tracking-tight",r),...a})});i.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-4 md:p-6 pt-0",r),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-4 md:p-6 pt-0",r),...a})}).displayName="CardFooter"},80659:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(40157).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var s=r(95155),a=r(12115),n=r(40968),o=r(74466),l=r(59434);let i=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.b,{ref:t,className:(0,l.cn)(i(),r),...a})});c.displayName=n.b.displayName}},e=>{var t=t=>e(e.s=t);e.O(0,[823,631,441,684,358],()=>t(3870)),_N_E=e.O()}]);