"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[831],{8803:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("Power",[["path",{d:"M12 2v10",key:"mnfbl"}],["path",{d:"M18.4 6.6a9 9 0 1 1-12.77.04",key:"obofu9"}]])},11133:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},13300:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},15452:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>$});var n=r(12115),o=r(85185),a=r(6101),l=r(46081),i=r(61285),d=r(5845),s=r(19178),u=r(25519),c=r(34378),f=r(28905),p=r(63655),h=r(92293),m=r(93795),y=r(38168),v=r(99708),g=r(95155),w="Dialog",[x,b]=(0,l.A)(w),[k,j]=x(w),D=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:s=!0}=e,u=n.useRef(null),c=n.useRef(null),[f,p]=(0,d.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:w});return(0,g.jsx)(k,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:s,children:r})};D.displayName=w;var A="DialogTrigger",R=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=j(A,r),i=(0,a.s)(t,l.triggerRef);return(0,g.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":U(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});R.displayName=A;var S="DialogPortal",[C,E]=x(S,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=j(S,t);return(0,g.jsx)(C,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,g.jsx)(f.C,{present:r||l.open,children:(0,g.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};M.displayName=S;var P="DialogOverlay",_=n.forwardRef((e,t)=>{let r=E(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=j(P,e.__scopeDialog);return a.modal?(0,g.jsx)(f.C,{present:n||a.open,children:(0,g.jsx)(N,{...o,ref:t})}):null});_.displayName=P;var I=(0,v.TL)("DialogOverlay.RemoveScroll"),N=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(P,r);return(0,g.jsx)(m.A,{as:I,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(p.sG.div,{"data-state":U(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),O="DialogContent",q=n.forwardRef((e,t)=>{let r=E(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=j(O,e.__scopeDialog);return(0,g.jsx)(f.C,{present:n||a.open,children:a.modal?(0,g.jsx)(z,{...o,ref:t}):(0,g.jsx)(F,{...o,ref:t})})});q.displayName=O;var z=n.forwardRef((e,t)=>{let r=j(O,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,y.Eq)(e)},[]),(0,g.jsx)(H,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=n.forwardRef((e,t)=>{let r=j(O,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,g.jsx)(H,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null===(n=e.onCloseAutoFocus)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current||null===(l=r.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null===(n=e.onInteractOutside)||void 0===n||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let i=t.target;(null===(l=r.triggerRef.current)||void 0===l?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),H=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...d}=e,c=j(O,r),f=n.useRef(null),p=(0,a.s)(t,f);return(0,h.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,g.jsx)(s.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":U(c.open),...d,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(Y,{titleId:c.titleId}),(0,g.jsx)(J,{contentRef:f,descriptionId:c.descriptionId})]})]})}),L="DialogTitle",G=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(L,r);return(0,g.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});G.displayName=L;var T="DialogDescription",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=j(T,r);return(0,g.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});B.displayName=T;var K="DialogClose",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=j(K,r);return(0,g.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function U(e){return e?"open":"closed"}V.displayName=K;var X="DialogTitleWarning",[Z,W]=(0,l.q)(X,{contentName:O,titleName:L,docsSlug:"dialog"}),Y=e=>{let{titleId:t}=e,r=W(X),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&!document.getElementById(t)&&console.error(o)},[o,t]),null},J=e=>{let{contentRef:t,descriptionId:r}=e,o=W("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(a)},[a,t,r]),null},Q=D,$=R,ee=M,et=_,er=q,en=G,eo=B,ea=V},18186:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},18271:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},29532:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("SquarePlus",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},30955:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("PowerOff",[["path",{d:"M18.36 6.64A9 9 0 0 1 20.77 15",key:"dxknvb"}],["path",{d:"M6.16 6.16a9 9 0 1 0 12.68 12.68",key:"1x7qb5"}],["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},33349:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},50594:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},54073:(e,t,r)=>{r.d(t,{CC:()=>B,Q6:()=>K,bL:()=>T,zi:()=>V});var n=r(12115),o=r(89367),a=r(85185),l=r(6101),i=r(46081),d=r(5845),s=r(94315),u=r(45503),c=r(11275),f=r(63655),p=r(37328),h=r(95155),m=["PageUp","PageDown"],y=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],v={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},g="Slider",[w,x,b]=(0,p.N)(g),[k,j]=(0,i.A)(g,[b]),[D,A]=k(g),R=n.forwardRef((e,t)=>{let{name:r,min:l=0,max:i=100,step:s=1,orientation:u="horizontal",disabled:c=!1,minStepsBetweenThumbs:f=0,defaultValue:p=[l],value:v,onValueChange:g=()=>{},onValueCommit:x=()=>{},inverted:b=!1,form:k,...j}=e,A=n.useRef(new Set),R=n.useRef(0),S="horizontal"===u,[C=[],P]=(0,d.i)({prop:v,defaultProp:p,onChange:e=>{var t;null===(t=[...A.current][R.current])||void 0===t||t.focus(),g(e)}}),_=n.useRef(C);function I(e,t){let{commit:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{commit:!1},n=(String(s).split(".")[1]||"").length,a=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-l)/s)*s+l,n),d=(0,o.q)(a,[l,i]);P(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,d,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,f*s))return e;{R.current=n.indexOf(d);let t=String(n)!==String(e);return t&&r&&x(n),t?n:e}})}return(0,h.jsx)(D,{scope:e.__scopeSlider,name:r,disabled:c,min:l,max:i,valueIndexToChangeRef:R,thumbs:A.current,values:C,orientation:u,form:k,children:(0,h.jsx)(w.Provider,{scope:e.__scopeSlider,children:(0,h.jsx)(w.Slot,{scope:e.__scopeSlider,children:(0,h.jsx)(S?E:M,{"aria-disabled":c,"data-disabled":c?"":void 0,...j,ref:t,onPointerDown:(0,a.m)(j.onPointerDown,()=>{c||(_.current=C)}),min:l,max:i,inverted:b,onSlideStart:c?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(C,e);I(e,t)},onSlideMove:c?void 0:function(e){I(e,R.current)},onSlideEnd:c?void 0:function(){let e=_.current[R.current];C[R.current]!==e&&x(C)},onHomeKeyDown:()=>!c&&I(l,0,{commit:!0}),onEndKeyDown:()=>!c&&I(i,C.length-1,{commit:!0}),onStepKeyDown:e=>{let{event:t,direction:r}=e;if(!c){let e=m.includes(t.key)||t.shiftKey&&y.includes(t.key),n=R.current;I(C[n]+s*(e?10:1)*r,n,{commit:!0})}}})})})})});R.displayName=g;var[S,C]=k(g,{startEdge:"left",endEdge:"right",size:"width",direction:1}),E=n.forwardRef((e,t)=>{let{min:r,max:o,dir:a,inverted:i,onSlideStart:d,onSlideMove:u,onSlideEnd:c,onStepKeyDown:f,...p}=e,[m,y]=n.useState(null),g=(0,l.s)(t,e=>y(e)),w=n.useRef(void 0),x=(0,s.jH)(a),b="ltr"===x,k=b&&!i||!b&&i;function j(e){let t=w.current||m.getBoundingClientRect(),n=G([0,t.width],k?[r,o]:[o,r]);return w.current=t,n(e-t.left)}return(0,h.jsx)(S,{scope:e.__scopeSlider,startEdge:k?"left":"right",endEdge:k?"right":"left",direction:k?1:-1,size:"width",children:(0,h.jsx)(P,{dir:x,"data-orientation":"horizontal",...p,ref:g,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=j(e.clientX);null==d||d(t)},onSlideMove:e=>{let t=j(e.clientX);null==u||u(t)},onSlideEnd:()=>{w.current=void 0,null==c||c()},onStepKeyDown:e=>{let t=v[k?"from-left":"from-right"].includes(e.key);null==f||f({event:e,direction:t?-1:1})}})})}),M=n.forwardRef((e,t)=>{let{min:r,max:o,inverted:a,onSlideStart:i,onSlideMove:d,onSlideEnd:s,onStepKeyDown:u,...c}=e,f=n.useRef(null),p=(0,l.s)(t,f),m=n.useRef(void 0),y=!a;function g(e){let t=m.current||f.current.getBoundingClientRect(),n=G([0,t.height],y?[o,r]:[r,o]);return m.current=t,n(e-t.top)}return(0,h.jsx)(S,{scope:e.__scopeSlider,startEdge:y?"bottom":"top",endEdge:y?"top":"bottom",size:"height",direction:y?1:-1,children:(0,h.jsx)(P,{"data-orientation":"vertical",...c,ref:p,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=g(e.clientY);null==i||i(t)},onSlideMove:e=>{let t=g(e.clientY);null==d||d(t)},onSlideEnd:()=>{m.current=void 0,null==s||s()},onStepKeyDown:e=>{let t=v[y?"from-bottom":"from-top"].includes(e.key);null==u||u({event:e,direction:t?-1:1})}})})}),P=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:l,onHomeKeyDown:i,onEndKeyDown:d,onStepKeyDown:s,...u}=e,c=A(g,r);return(0,h.jsx)(f.sG.span,{...u,ref:t,onKeyDown:(0,a.m)(e.onKeyDown,e=>{"Home"===e.key?(i(e),e.preventDefault()):"End"===e.key?(d(e),e.preventDefault()):m.concat(y).includes(e.key)&&(s(e),e.preventDefault())}),onPointerDown:(0,a.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),c.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,a.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&o(e)}),onPointerUp:(0,a.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),l(e))})})}),_="SliderTrack",I=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,o=A(_,r);return(0,h.jsx)(f.sG.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:t})});I.displayName=_;var N="SliderRange",O=n.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,a=A(N,r),i=C(N,r),d=n.useRef(null),s=(0,l.s)(t,d),u=a.values.length,c=a.values.map(e=>L(e,a.min,a.max)),p=u>1?Math.min(...c):0,m=100-Math.max(...c);return(0,h.jsx)(f.sG.span,{"data-orientation":a.orientation,"data-disabled":a.disabled?"":void 0,...o,ref:s,style:{...e.style,[i.startEdge]:p+"%",[i.endEdge]:m+"%"}})});O.displayName=N;var q="SliderThumb",z=n.forwardRef((e,t)=>{let r=x(e.__scopeSlider),[o,a]=n.useState(null),i=(0,l.s)(t,e=>a(e)),d=n.useMemo(()=>o?r().findIndex(e=>e.ref.current===o):-1,[r,o]);return(0,h.jsx)(F,{...e,ref:i,index:d})}),F=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:o,name:i,...d}=e,s=A(q,r),u=C(q,r),[p,m]=n.useState(null),y=(0,l.s)(t,e=>m(e)),v=!p||s.form||!!p.closest("form"),g=(0,c.X)(p),x=s.values[o],b=void 0===x?0:L(x,s.min,s.max),k=function(e,t){return t>2?"Value ".concat(e+1," of ").concat(t):2===t?["Minimum","Maximum"][e]:void 0}(o,s.values.length),j=null==g?void 0:g[u.size],D=j?function(e,t,r){let n=e/2,o=G([0,50],[0,n]);return(n-o(t)*r)*r}(j,b,u.direction):0;return n.useEffect(()=>{if(p)return s.thumbs.add(p),()=>{s.thumbs.delete(p)}},[p,s.thumbs]),(0,h.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[u.startEdge]:"calc(".concat(b,"% + ").concat(D,"px)")},children:[(0,h.jsx)(w.ItemSlot,{scope:e.__scopeSlider,children:(0,h.jsx)(f.sG.span,{role:"slider","aria-label":e["aria-label"]||k,"aria-valuemin":s.min,"aria-valuenow":x,"aria-valuemax":s.max,"aria-orientation":s.orientation,"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0,tabIndex:s.disabled?void 0:0,...d,ref:y,style:void 0===x?{display:"none"}:e.style,onFocus:(0,a.m)(e.onFocus,()=>{s.valueIndexToChangeRef.current=o})})}),v&&(0,h.jsx)(H,{name:null!=i?i:s.name?s.name+(s.values.length>1?"[]":""):void 0,form:s.form,value:x},o)]})});z.displayName=q;var H=n.forwardRef((e,t)=>{let{__scopeSlider:r,value:o,...a}=e,i=n.useRef(null),d=(0,l.s)(i,t),s=(0,u.Z)(o);return n.useEffect(()=>{let e=i.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(s!==o&&t){let r=new Event("input",{bubbles:!0});t.call(e,o),e.dispatchEvent(r)}},[s,o]),(0,h.jsx)(f.sG.input,{style:{display:"none"},...a,ref:d,defaultValue:o})});function L(e,t,r){return(0,o.q)(100/(r-t)*(e-t),[0,100])}function G(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}H.displayName="RadioBubbleInput";var T=R,B=I,K=O,V=z},57082:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},60620:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},73672:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},75074:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},76981:(e,t,r)=>{r.d(t,{C1:()=>j,bL:()=>b});var n=r(12115),o=r(6101),a=r(46081),l=r(85185),i=r(5845),d=r(45503),s=r(11275),u=r(28905),c=r(63655),f=r(95155),p="Checkbox",[h,m]=(0,a.A)(p),[y,v]=h(p);function g(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:a,disabled:l,form:d,name:s,onCheckedChange:u,required:c,value:h="on",internal_do_not_use_render:m}=e,[v,g]=(0,i.i)({prop:r,defaultProp:null!=a&&a,onChange:u,caller:p}),[w,x]=n.useState(null),[b,k]=n.useState(null),j=n.useRef(!1),D=!w||!!d||!!w.closest("form"),A={checked:v,disabled:l,setChecked:g,control:w,setControl:x,name:s,form:d,value:h,hasConsumerStoppedPropagationRef:j,required:c,defaultChecked:!R(a)&&a,isFormControl:D,bubbleInput:b,setBubbleInput:k};return(0,f.jsx)(y,{scope:t,...A,children:"function"==typeof m?m(A):o})}var w="CheckboxTrigger",x=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:a,onClick:i,...d}=e,{control:s,value:u,disabled:p,checked:h,required:m,setControl:y,setChecked:g,hasConsumerStoppedPropagationRef:x,isFormControl:b,bubbleInput:k}=v(w,r),j=(0,o.s)(t,y),D=n.useRef(h);return n.useEffect(()=>{let e=null==s?void 0:s.form;if(e){let t=()=>g(D.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[s,g]),(0,f.jsx)(c.sG.button,{type:"button",role:"checkbox","aria-checked":R(h)?"mixed":h,"aria-required":m,"data-state":S(h),"data-disabled":p?"":void 0,disabled:p,value:u,...d,ref:j,onKeyDown:(0,l.m)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(i,e=>{g(e=>!!R(e)||!e),k&&b&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})})});x.displayName=w;var b=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:a,required:l,disabled:i,value:d,onCheckedChange:s,form:u,...c}=e;return(0,f.jsx)(g,{__scopeCheckbox:r,checked:o,defaultChecked:a,disabled:i,required:l,onCheckedChange:s,name:n,form:u,value:d,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(x,{...c,ref:t,__scopeCheckbox:r}),n&&(0,f.jsx)(A,{__scopeCheckbox:r})]})}})});b.displayName=p;var k="CheckboxIndicator",j=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=v(k,r);return(0,f.jsx)(u.C,{present:n||R(a.checked)||!0===a.checked,children:(0,f.jsx)(c.sG.span,{"data-state":S(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});j.displayName=k;var D="CheckboxBubbleInput",A=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...a}=e,{control:l,hasConsumerStoppedPropagationRef:i,checked:u,defaultChecked:p,required:h,disabled:m,name:y,value:g,form:w,bubbleInput:x,setBubbleInput:b}=v(D,r),k=(0,o.s)(t,b),j=(0,d.Z)(u),A=(0,s.X)(l);n.useEffect(()=>{if(!x)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(j!==u&&e){let r=new Event("click",{bubbles:t});x.indeterminate=R(u),e.call(x,!R(u)&&u),x.dispatchEvent(r)}},[x,j,u,i]);let S=n.useRef(!R(u)&&u);return(0,f.jsx)(c.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:S.current,required:h,disabled:m,name:y,value:g,form:w,...a,tabIndex:-1,ref:k,style:{...a.style,...A,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function R(e){return"indeterminate"===e}function S(e){return R(e)?"indeterminate":e?"checked":"unchecked"}A.displayName=D},94063:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(40157).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])}}]);