(()=>{var e={};e.id=974,e.ids=[974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41076:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var n=r(65239),s=r(48088),o=r(88170),i=r.n(o),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let p=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21204)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}],l=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},55511:e=>{"use strict";e.exports=require("crypto")},55807:(e,t,r)=>{Promise.resolve().then(r.bind(r,75694))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73959:(e,t,r)=>{Promise.resolve().then(r.bind(r,21204))},75694:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(60687);r(43210);var s=r(16189),o=r(63213),i=r(11516);function a(){(0,s.useRouter)();let{isAuthenticated:e,isLoading:t}=(0,o.A)();return(0,n.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background",children:[(0,n.jsx)(i.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,n.jsx)("p",{className:"ml-4 text-xl",children:"Initializing Pluto..."})]})}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[388,475],()=>r(41076));module.exports=n})();