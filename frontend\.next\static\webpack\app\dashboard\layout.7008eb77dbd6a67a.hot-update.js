"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/TradingConfigSidebar.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradingConfigSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _contexts_AIContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AIContext */ \"(app-pages-browser)/./src/contexts/AIContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/TargetPriceModal */ \"(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/crypto-input */ \"(app-pages-browser)/./src/components/ui/crypto-input.tsx\");\n/* harmony import */ var _components_dashboard_AlarmSettings__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/dashboard/AlarmSettings */ \"(app-pages-browser)/./src/components/dashboard/AlarmSettings.tsx\");\n/* harmony import */ var _components_dashboard_TelegramSettings__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/dashboard/TelegramSettings */ \"(app-pages-browser)/./src/components/dashboard/TelegramSettings.tsx\");\n/* harmony import */ var _components_dashboard_TradingPairSelector__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/dashboard/TradingPairSelector */ \"(app-pages-browser)/./src/components/dashboard/TradingPairSelector.tsx\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power-off.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n// Force refresh of stablecoins list\nconst STABLECOINS_LIST = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n\n\nfunction TradingConfigSidebar() {\n    _s();\n    // Safely get trading context with error handling\n    let tradingContext;\n    try {\n        tradingContext = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    } catch (error) {\n        console.error('Trading context not available:', error);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"Trading context not available. Please refresh the page.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 40,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this);\n    }\n    const { config, dispatch, botSystemStatus, appSettings, setTargetPrices: contextSetTargetPrices } = tradingContext;\n    const isBotActive = botSystemStatus === 'Running';\n    const isBotWarmingUp = botSystemStatus === 'WarmingUp';\n    // Safely get AI context\n    let aiContext;\n    try {\n        aiContext = (0,_contexts_AIContext__WEBPACK_IMPORTED_MODULE_3__.useAIContext)();\n    } catch (error) {\n        console.warn('AI context not available:', error);\n        aiContext = {\n            suggestion: null,\n            isLoading: false,\n            error: null,\n            getTradingModeSuggestion: ()=>Promise.resolve()\n        };\n    }\n    const { suggestion: aiSuggestion, isLoading: aiLoading, error: aiError, getTradingModeSuggestion } = aiContext;\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_18__.useToast)();\n    const [isTargetModalOpen, setIsTargetModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAlarmModalOpen, setIsAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSessionAlarmModalOpen, setIsSessionAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTelegramModalOpen, setIsTelegramModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTradingPairModalOpen, setIsTradingPairModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAISectionVisible, setIsAISectionVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // AI Suggestion form state\n    const [riskTolerance, setRiskTolerance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"medium\");\n    const [preferredCryptos, setPreferredCryptos] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [investmentGoals, setInvestmentGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        let val;\n        if (type === 'checkbox') {\n            val = checked;\n        } else if (type === 'number') {\n            // Handle empty string or invalid numbers gracefully\n            if (value === '' || value === null || value === undefined) {\n                val = 0;\n            } else {\n                const parsed = parseFloat(value);\n                val = isNaN(parsed) ? 0 : parsed;\n            }\n        } else {\n            val = value;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: val\n            }\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: value\n            }\n        });\n        if (name === 'crypto1') {\n            // Reset crypto2 if new crypto1 doesn't support current crypto2\n            const validQuotes = _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_QUOTES_SIMPLE[value] || DEFAULT_QUOTE_CURRENCIES || [\n                \"USDT\",\n                \"USDC\",\n                \"BTC\"\n            ];\n            if (!config.crypto2 || !Array.isArray(validQuotes) || !validQuotes.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: validQuotes[0] || 'USDT'\n                    }\n                }); // Ensure crypto2 gets a valid default\n            }\n        }\n    };\n    const handleCrypto1Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto1: crypto\n            }\n        });\n        // Reset crypto2 based on trading mode when crypto1 changes\n        if (config.tradingMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (_lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1 || [\n                \"BTC\",\n                \"ETH\",\n                \"BNB\",\n                \"SOL\",\n                \"LINK\",\n                \"AVAX\",\n                \"DOT\",\n                \"UNI\",\n                \"NEAR\",\n                \"AAVE\",\n                \"ATOM\",\n                \"VET\",\n                \"RENDER\",\n                \"POL\",\n                \"ALGO\",\n                \"ARB\",\n                \"FET\",\n                \"PAXG\",\n                \"GALA\",\n                \"CRV\",\n                \"COMP\",\n                \"ENJ\"\n            ]).filter((c)=>c !== crypto);\n            if (!allowedCrypto2.includes(config.crypto2) || config.crypto2 === crypto) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO2 || [\n                \"USDC\",\n                \"DAI\",\n                \"TUSD\",\n                \"FDUSD\",\n                \"USDT\",\n                \"EUR\"\n            ];\n            if (!allowedCrypto2.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        }\n    };\n    const handleCrypto2Selection = (crypto)=>{\n        // In stablecoin swap mode, ensure crypto2 is different from crypto1\n        if (config.tradingMode === \"StablecoinSwap\" && crypto === config.crypto1) {\n            // Don't allow selecting the same crypto as crypto1 in swap mode\n            return;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto2: crypto\n            }\n        });\n    };\n    const handleTradingModeChange = (checked)=>{\n        const newMode = checked ? \"StablecoinSwap\" : \"SimpleSpot\";\n        // Reset crypto2 based on the new trading mode\n        let newCrypto2;\n        if (newMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (_lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1 || [\n                \"BTC\",\n                \"ETH\",\n                \"BNB\",\n                \"SOL\",\n                \"LINK\",\n                \"AVAX\",\n                \"DOT\",\n                \"UNI\",\n                \"NEAR\",\n                \"AAVE\",\n                \"ATOM\",\n                \"VET\",\n                \"RENDER\",\n                \"POL\",\n                \"ALGO\",\n                \"ARB\",\n                \"FET\",\n                \"PAXG\",\n                \"GALA\",\n                \"CRV\",\n                \"COMP\",\n                \"ENJ\"\n            ]).filter((c)=>c !== config.crypto1);\n            newCrypto2 = allowedCrypto2[0];\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO2 || [\n                \"USDC\",\n                \"DAI\",\n                \"TUSD\",\n                \"FDUSD\",\n                \"USDT\",\n                \"EUR\"\n            ];\n            newCrypto2 = allowedCrypto2[0];\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                tradingMode: newMode,\n                crypto2: newCrypto2\n            }\n        });\n    };\n    const handleIncomeSplitChange = (cryptoKey, value)=>{\n        let percent = parseFloat(value);\n        if (isNaN(percent)) percent = 0;\n        if (percent < 0) percent = 0;\n        if (percent > 100) percent = 100;\n        if (cryptoKey === 'incomeSplitCrypto1Percent') {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto1Percent: percent,\n                    incomeSplitCrypto2Percent: 100 - percent\n                }\n            });\n        } else {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto2Percent: percent,\n                    incomeSplitCrypto1Percent: 100 - percent\n                }\n            });\n        }\n    };\n    const handleAISuggestion = async ()=>{\n        if (!riskTolerance || !preferredCryptos || !investmentGoals) {\n            toast({\n                title: \"AI Suggestion Error\",\n                description: \"Please fill all AI suggestion fields.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        await getTradingModeSuggestion({\n            riskTolerance,\n            preferredCryptocurrencies: preferredCryptos,\n            investmentGoals\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingConfigSidebar.useEffect\": ()=>{\n            if (aiSuggestion) {\n                toast({\n                    title: \"AI Suggestion Received\",\n                    description: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Mode:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    aiSuggestion.suggestedMode\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Reason:\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 16\n                                    }, this),\n                                    \" \",\n                                    aiSuggestion.reason\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                size: \"sm\",\n                                className: \"mt-2 btn-neo\",\n                                onClick: {\n                                    \"TradingConfigSidebar.useEffect\": ()=>dispatch({\n                                            type: 'SET_CONFIG',\n                                            payload: {\n                                                tradingMode: aiSuggestion.suggestedMode === 'Simple Spot' ? 'SimpleSpot' : 'StablecoinSwap'\n                                            }\n                                        })\n                                }[\"TradingConfigSidebar.useEffect\"],\n                                children: \"Apply Suggestion\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    duration: Infinity\n                });\n            }\n            if (aiError) {\n                toast({\n                    title: \"AI Suggestion Error\",\n                    description: aiError,\n                    variant: \"destructive\",\n                    duration: Infinity\n                });\n            }\n        }\n    }[\"TradingConfigSidebar.useEffect\"], [\n        aiSuggestion,\n        aiError,\n        toast,\n        dispatch\n    ]);\n    // Use static crypto lists\n    const crypto1Options = _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS || [];\n    const crypto2Options = config.tradingMode === \"SimpleSpot\" ? _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_QUOTES_SIMPLE[config.crypto1] || DEFAULT_QUOTE_CURRENCIES || [\n        \"USDT\",\n        \"USDC\",\n        \"BTC\"\n    ] : (_lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS || []).filter((c)=>c !== config.crypto1); // Ensure Crypto2 is not same as Crypto1 in Swap mode\n    // Debug log to see what's actually loaded\n    console.log('🔍 DEBUG: AVAILABLE_CRYPTOS length:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS.length);\n    console.log('🔍 DEBUG: crypto1Options length:', crypto1Options.length);\n    console.log('🔍 DEBUG: First 20 cryptos:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS.slice(0, 20));\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO1:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1);\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO2:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO2);\n    console.log('🔍 DEBUG: AVAILABLE_STABLECOINS:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_STABLECOINS);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-sidebar-primary\",\n                        children: \"Trading Configuration\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        onClick: ()=>setIsAISectionVisible(!isAISectionVisible),\n                        className: \"text-sidebar-accent-foreground hover:bg-sidebar-accent\",\n                        title: isAISectionVisible ? \"Hide AI Suggestions\" : \"Show AI Suggestions\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            className: \"h-4 w-4 \".concat(isAISectionVisible ? 'text-primary' : 'text-muted-foreground')\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_10__.ScrollArea, {\n                className: \"flex-1 pr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__.Checkbox, {\n                                                    id: \"enableStablecoinSwap\",\n                                                    checked: config.tradingMode === \"StablecoinSwap\",\n                                                    onCheckedChange: handleTradingModeChange\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"enableStablecoinSwap\",\n                                                    className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                    children: \"Enable Stablecoin Swap Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"Current mode: \",\n                                                config.tradingMode === \"SimpleSpot\" ? \"Simple Spot\" : \"Stablecoin Swap\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.tradingMode === \"StablecoinSwap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"preferredStablecoin\",\n                                                    children: \"Preferred Stablecoin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                    name: \"preferredStablecoin\",\n                                                    value: config.preferredStablecoin,\n                                                    onValueChange: (val)=>handleSelectChange(\"preferredStablecoin\", val),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                            id: \"preferredStablecoin\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                placeholder: \"Select stablecoin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                            className: \"max-h-[300px] overflow-y-auto\",\n                                                            children: STABLECOINS_LIST.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: sc,\n                                                                    children: sc\n                                                                }, sc, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 254,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 252,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 18\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        isAISectionVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 89\n                                            }, this),\n                                            \" AI Mode Suggestion\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"riskTolerance\",\n                                                    children: \"Risk Tolerance\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                    value: riskTolerance,\n                                                    onValueChange: setRiskTolerance,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                            id: \"riskTolerance\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                placeholder: \"Select risk tolerance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 55\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 273,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: \"low\",\n                                                                    children: \"Low\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: \"medium\",\n                                                                    children: \"Medium\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 276,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                    value: \"high\",\n                                                                    children: \"High\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 277,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"preferredCryptos\",\n                                                    children: \"Preferred Cryptocurrencies (comma-separated)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"preferredCryptos\",\n                                                    value: preferredCryptos,\n                                                    onChange: (e)=>setPreferredCryptos(e.target.value),\n                                                    placeholder: \"e.g., BTC, ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    htmlFor: \"investmentGoals\",\n                                                    children: \"Investment Goals\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"investmentGoals\",\n                                                    value: investmentGoals,\n                                                    onChange: (e)=>setInvestmentGoals(e.target.value),\n                                                    placeholder: \"e.g., Long term, Short term profit\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleAISuggestion,\n                                            disabled: aiLoading,\n                                            className: \"w-full btn-neo\",\n                                            children: [\n                                                aiLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 33\n                                                }, this),\n                                                \"Get AI Suggestion\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Pair\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_14__.CryptoInput, {\n                                            label: \"Crypto 1 (Base)\",\n                                            value: config.crypto1,\n                                            allowedCryptos: _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ],\n                                            onValidCrypto: handleCrypto1Selection,\n                                            placeholder: \"e.g., BTC, ETH, SOL\",\n                                            description: \"Enter the base cryptocurrency symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_14__.CryptoInput, {\n                                            label: config.tradingMode === \"StablecoinSwap\" ? \"Crypto 2\" : \"Crypto 2 (Stablecoin)\",\n                                            value: config.crypto2,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? (_lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ]).filter((c)=>c !== config.crypto1) : _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO2 || [\n                                                \"USDC\",\n                                                \"DAI\",\n                                                \"TUSD\",\n                                                \"FDUSD\",\n                                                \"USDT\",\n                                                \"EUR\"\n                                            ],\n                                            onValidCrypto: handleCrypto2Selection,\n                                            placeholder: config.tradingMode === \"StablecoinSwap\" ? \"e.g., BTC, ETH, SOL\" : \"e.g., USDT, USDC, DAI\",\n                                            description: config.tradingMode === \"StablecoinSwap\" ? \"Enter the second cryptocurrency symbol\" : \"Enter the quote/stablecoin symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        [\n                                            {\n                                                name: \"baseBid\",\n                                                label: \"Base Bid (Crypto 2)\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            },\n                                            {\n                                                name: \"multiplier\",\n                                                label: \"Multiplier\",\n                                                type: \"number\",\n                                                step: \"0.001\"\n                                            },\n                                            {\n                                                name: \"numDigits\",\n                                                label: \"Display Digits\",\n                                                type: \"number\",\n                                                step: \"1\"\n                                            },\n                                            {\n                                                name: \"slippagePercent\",\n                                                label: \"Slippage %\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            }\n                                        ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: field.name,\n                                                        children: field.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: field.name,\n                                                        name: field.name,\n                                                        type: field.type,\n                                                        value: config[field.name],\n                                                        onChange: handleInputChange,\n                                                        step: field.step,\n                                                        min: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                    children: \"Couple Income % Split (must sum to 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto1Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto1 || \"Crypto 1\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 350,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"incomeSplitCrypto1Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto1Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto1Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto2Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto2 || \"Crypto 2\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"incomeSplitCrypto2Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto2Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto2Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {\n                        className: \"mb-4 bg-sidebar-border\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_19__.cn)(\"text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border\", isBotActive ? \"bg-green-600 text-primary-foreground\" : \"bg-muted text-muted-foreground\"),\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 129\n                                    }, this),\n                                    \"Bot Status: \",\n                                    isBotActive ? 'Running' : isBotWarmingUp ? 'Warming Up' : 'Stopped'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsTargetModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Target Prices\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsAlarmModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Alarm\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsSessionAlarmModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Session Alarms\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsTelegramModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Telegram Settings\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setIsTradingPairModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Select Trading Pair\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>{\n                                    if (isBotActive) {\n                                        dispatch({\n                                            type: 'SYSTEM_STOP_BOT'\n                                        });\n                                    } else {\n                                        dispatch({\n                                            type: 'SYSTEM_START_BOT_INITIATE'\n                                        });\n                                    }\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_19__.cn)('w-full btn-neo', isBotActive || isBotWarmingUp ? 'bg-destructive hover:bg-destructive/90' : 'bg-green-600 hover:bg-green-600/90'),\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 129\n                                    }, this),\n                                    isBotActive ? 'Stop Bot' : isBotWarmingUp ? 'Warming Up...' : 'Start Bot'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>{\n                                    dispatch({\n                                        type: 'SYSTEM_RESET_BOT'\n                                    });\n                                    toast({\n                                        title: \"Bot Reset\",\n                                        description: \"Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.\",\n                                        duration: 4000\n                                    });\n                                },\n                                variant: \"outline\",\n                                className: \"w-full btn-outline-neo\",\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset Bot\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_12__.TargetPriceModal, {\n                isOpen: isTargetModalOpen,\n                onClose: ()=>setIsTargetModalOpen(false),\n                onSetTargetPrices: contextSetTargetPrices\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlarmConfigModal, {\n                isOpen: isAlarmModalOpen,\n                onClose: ()=>setIsAlarmModalOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_AlarmSettings__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                isOpen: isSessionAlarmModalOpen,\n                onClose: ()=>setIsSessionAlarmModalOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 417,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TelegramSettings__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                isOpen: isTelegramModalOpen,\n                onClose: ()=>setIsTelegramModalOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 418,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_TradingPairSelector__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                isOpen: isTradingPairModalOpen,\n                onClose: ()=>setIsTradingPairModalOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingConfigSidebar, \"2e6P7PJ0eqGUxJOc6BUqQm7Z8QI=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_18__.useToast\n    ];\n});\n_c = TradingConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TradingConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx\n"));

/***/ })

});