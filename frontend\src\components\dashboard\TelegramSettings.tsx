"use client";

import React, { useState } from 'react';
import { useTradingContext } from '@/contexts/TradingContext';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { MessageSquare, Send, AlertCircle, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { testTelegramConfig, validateTelegramConfig } from '@/utils/telegramNotifications';

interface TelegramSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function TelegramSettings({ isOpen, onClose }: TelegramSettingsProps) {
  const { telegramConfig, dispatch } = useTradingContext();
  const { toast } = useToast();

  const [localConfig, setLocalConfig] = useState(telegramConfig);
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  const handleSave = () => {
    const validation = validateTelegramConfig(localConfig);
    
    if (!validation.valid) {
      toast({
        title: "Configuration Error",
        description: validation.errors.join(', '),
        variant: "destructive"
      });
      return;
    }

    dispatch({ type: 'SET_TELEGRAM_CONFIG', payload: localConfig });
    toast({
      title: "Telegram Settings Saved",
      description: "Your Telegram notification settings have been updated.",
      duration: 3000
    });
    onClose();
  };

  const handleTestConnection = async () => {
    const validation = validateTelegramConfig(localConfig);
    
    if (!validation.valid) {
      toast({
        title: "Configuration Error",
        description: validation.errors.join(', '),
        variant: "destructive"
      });
      return;
    }

    setIsTestingConnection(true);
    
    try {
      const success = await testTelegramConfig(localConfig);
      
      if (success) {
        toast({
          title: "Test Successful",
          description: "Test message sent successfully! Check your Telegram chat.",
          duration: 5000
        });
      } else {
        toast({
          title: "Test Failed",
          description: "Could not send test message. Please check your configuration.",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Test Failed",
        description: "An error occurred while testing the connection.",
        variant: "destructive"
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Telegram Notifications
          </DialogTitle>
          <DialogDescription>
            Configure Telegram notifications for trading alerts, errors, and system events.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Enable/Disable Toggle */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Notification Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="telegramEnabled"
                  checked={localConfig.enabled}
                  onCheckedChange={(checked) => 
                    setLocalConfig({ ...localConfig, enabled: checked as boolean })
                  }
                />
                <Label htmlFor="telegramEnabled">Enable Telegram notifications</Label>
              </div>
            </CardContent>
          </Card>

          {/* Configuration Fields */}
          {localConfig.enabled && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Bot Configuration</CardTitle>
                  <CardDescription>
                    Create a Telegram bot via @BotFather and get your bot token.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="botToken">Bot Token</Label>
                    <Input
                      id="botToken"
                      type="password"
                      value={localConfig.botToken}
                      onChange={(e) => setLocalConfig({ ...localConfig, botToken: e.target.value })}
                      placeholder="123456789:ABCdefGHIjklMNOpqrsTUVwxyz"
                    />
                    <p className="text-xs text-muted-foreground">
                      Format: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="chatId">Chat ID</Label>
                    <Input
                      id="chatId"
                      value={localConfig.chatId}
                      onChange={(e) => setLocalConfig({ ...localConfig, chatId: e.target.value })}
                      placeholder="-1001234567890 or 123456789"
                    />
                    <p className="text-xs text-muted-foreground">
                      Your personal chat ID or group chat ID (starts with -)
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Setup Instructions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Setup Instructions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-start gap-2">
                      <span className="font-medium text-primary">1.</span>
                      <span>Message @BotFather on Telegram and create a new bot with /newbot</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="font-medium text-primary">2.</span>
                      <span>Copy the bot token and paste it above</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="font-medium text-primary">3.</span>
                      <span>Message @userinfobot to get your chat ID</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="font-medium text-primary">4.</span>
                      <span>Start a conversation with your bot first</span>
                    </div>
                    <div className="flex items-start gap-2">
                      <span className="font-medium text-primary">5.</span>
                      <span>Test the connection using the button below</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Test Connection */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Test Connection</CardTitle>
                </CardHeader>
                <CardContent>
                  <Button
                    onClick={handleTestConnection}
                    disabled={isTestingConnection || !localConfig.botToken || !localConfig.chatId}
                    className="w-full"
                    variant="outline"
                  >
                    {isTestingConnection ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                        Sending Test Message...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" />
                        Send Test Message
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave} className="btn-neo">
              Save Settings
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
