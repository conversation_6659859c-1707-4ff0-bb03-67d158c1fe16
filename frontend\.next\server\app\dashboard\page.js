(()=>{var e={};e.id=105,e.ids=[105],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6638:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=r(65239),a=r(48088),o=r(88170),n=r.n(o),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80559)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(60687),a=r(43210),o=r(78895),n=r(4780);let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:r,className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})}));l.displayName="Table";let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("thead",{ref:r,className:(0,n.cn)("[&_tr]:border-b",e),...t}));i.displayName="TableHeader";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("tbody",{ref:r,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t}));d.displayName="TableBody",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("tfoot",{ref:r,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("tr",{ref:r,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));c.displayName="TableRow";let p=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("th",{ref:r,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));p.displayName="TableHead";let x=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("td",{ref:r,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));x.displayName="TableCell",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("caption",{ref:r,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption";var m=r(42692),u=r(96834);function b(){let{getDisplayOrders:e,config:t,currentMarketPrice:r}=(0,o.U)(),a=e(),b=(e,r=!1)=>{if(null==e||isNaN(e))return"-";let s=e.toFixed(t.numDigits);return r&&e>0?`+${s}`:s},y=e=>null==e||isNaN(e)?"-":`${e.toFixed(2)}%`,f=[{key:"#",label:"#"},{key:"status",label:"Status"},{key:"orderLevel",label:"Level"},{key:"valueLevel",label:"Value"},{key:"crypto2Var",label:`${t.crypto2||"Crypto 2"} Var.`},{key:"crypto1Var",label:`${t.crypto1||"Crypto 1"} Var.`},{key:"targetPrice",label:"Target Price"},{key:"percentFromActualPrice",label:"% from Actual"},{key:"incomeCrypto1",label:`Income ${t.crypto1||"Crypto 1"}`},{key:"incomeCrypto2",label:`Income ${t.crypto2||"Crypto 2"}`},{key:"originalCostCrypto2",label:`Original Cost ${t.crypto2||"Crypto 2"}`}];return(0,s.jsx)("div",{className:"border-2 border-border rounded-sm",children:(0,s.jsxs)(m.F,{className:"w-full whitespace-nowrap",children:[(0,s.jsxs)(l,{className:"min-w-full",children:[(0,s.jsx)(i,{children:(0,s.jsx)(c,{className:"bg-card hover:bg-card",children:f.map(e=>(0,s.jsx)(p,{className:"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm",children:e.label},e.key))})}),(0,s.jsx)(d,{children:0===a.length?(0,s.jsx)(c,{children:(0,s.jsx)(x,{colSpan:f.length,className:"h-24 text-center text-muted-foreground",children:'No target prices set. Use "Set Target Prices" in the sidebar.'})}):a.map(e=>(0,s.jsxs)(c,{className:"hover:bg-card/80",children:[(0,s.jsx)(x,{className:"px-3 py-2 text-xs",children:e.counter}),(0,s.jsx)(x,{className:"px-3 py-2 text-xs",children:(0,s.jsx)(u.E,{variant:"Full"===e.status?"default":"secondary",className:(0,n.cn)("Full"===e.status?"bg-green-600 text-white":"bg-yellow-500 text-black","font-bold"),children:e.status})}),(0,s.jsx)(x,{className:"px-3 py-2 text-xs",children:e.orderLevel}),(0,s.jsx)(x,{className:"px-3 py-2 text-xs",children:b(e.valueLevel)}),(0,s.jsx)(x,{className:(0,n.cn)("px-3 py-2 text-xs",e.crypto2Var&&e.crypto2Var<0?"text-destructive":"text-green-400"),children:b(e.crypto2Var,!0)}),(0,s.jsx)(x,{className:(0,n.cn)("px-3 py-2 text-xs",e.crypto1Var&&e.crypto1Var<0?"text-destructive":"text-green-400"),children:b(e.crypto1Var,!0)}),(0,s.jsx)(x,{className:"px-3 py-2 text-xs font-semibold text-primary",children:b(e.targetPrice)}),(0,s.jsx)(x,{className:(0,n.cn)("px-3 py-2 text-xs",e.percentFromActualPrice<0?"text-destructive":"text-green-400"),children:y(e.percentFromActualPrice)}),(0,s.jsx)(x,{className:(0,n.cn)("px-3 py-2 text-xs",e.incomeCrypto1&&e.incomeCrypto1<0?"text-destructive":"text-green-400"),children:b(e.incomeCrypto1)}),(0,s.jsx)(x,{className:(0,n.cn)("px-3 py-2 text-xs",e.incomeCrypto2&&e.incomeCrypto2<0?"text-destructive":"text-green-400"),children:b(e.incomeCrypto2)}),(0,s.jsx)(x,{className:"px-3 py-2 text-xs",children:b(e.originalCostCrypto2)})]},e.id))})]}),(0,s.jsx)(m.$,{orientation:"horizontal"})]})})}var y=r(37079),f=r(44493),h=r(70428),g=r(8751);function j(){let{config:e,currentMarketPrice:t}=(0,o.U)(),r=e.crypto1&&e.crypto2,a=r?`${e.crypto1}/${e.crypto2}`:"Crypto 1/Crypto 2",n=r?t.toFixed(e.numDigits):"0";return(0,s.jsx)("div",{className:"mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-5 w-5 text-green-500"}),(0,s.jsx)("span",{className:"text-sm font-medium text-muted-foreground",children:"Current Market Price"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("span",{className:"text-lg font-semibold text-foreground",children:[a,":"]}),(0,s.jsxs)("span",{className:"text-2xl font-bold text-primary",children:["$",n]})]})]})})}var v=r(5551),N=r(29867);function C(){let{config:e,saveCurrentSession:t,targetPriceRows:r,orderHistory:n}=(0,o.U)(),{toast:l}=(0,N.dj)(),[i,d]=(0,a.useState)("");v.C.getInstance();let c=i||(e.crypto1&&e.crypto2?`${e.crypto1}/${e.crypto2} ${e.tradingMode||"SimpleSpot"}`:"Crypto 1/Crypto 2 = 0");return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(y.A,{}),(0,s.jsx)(h.A,{}),(0,s.jsxs)(f.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(f.aR,{children:[(0,s.jsxs)(f.ZB,{className:"text-2xl font-bold text-primary",children:["Active Orders (",c,")"]}),(0,s.jsx)(f.BT,{children:"Current state of your target price levels. Prices update in real-time."})]}),(0,s.jsxs)(f.Wu,{children:[(0,s.jsx)(j,{}),(0,s.jsx)(b,{})]})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47729:(e,t,r)=>{Promise.resolve().then(r.bind(r,13014))},55511:e=>{"use strict";e.exports=require("crypto")},57457:(e,t,r)=>{Promise.resolve().then(r.bind(r,80559))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80559:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\dashboard\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[388,992,288,475,12],()=>r(6638));module.exports=s})();