exports.id=313,exports.ids=[313],exports.modules={3935:(e,t,r)=>{"use strict";let{format:n}=r(28354);t.warn={deprecated:e=>()=>{throw Error(n("{ %s } was removed in winston@3.0.0.",e))},useFormat:e=>()=>{throw Error([n("{ %s } was removed in winston@3.0.0.",e),"Use a custom winston.format = winston.format(function) instead."].join("\n"))},forFunctions(e,r,n){n.forEach(n=>{e[n]=t.warn[r](n)})},forProperties(e,r,n){n.forEach(n=>{let s=t.warn[r](n);Object.defineProperty(e,n,{get:s,set:s})})}}},4991:e=>{var t=[],r=[],n=function(){};function s(e){return!~t.indexOf(e)&&(t.push(e),!0)}function i(e){n=e}function o(e){for(var r=[],n=0;n<t.length;n++){if(t[n].async){r.push(t[n]);continue}if(t[n](e))return!0}return!!r.length&&new Promise(function(t){Promise.all(r.map(function(t){return t(e)})).then(function(e){t(e.some(Boolean))})})}function a(e){return!~r.indexOf(e)&&(r.push(e),!0)}function l(){n.apply(n,arguments)}function u(e){for(var t=0;t<r.length;t++)e=r[t].apply(r[t],arguments);return e}function c(e,t){var r=Object.prototype.hasOwnProperty;for(var n in t)r.call(t,n)&&(e[n]=t[n]);return e}function h(e){return e.enabled=!1,e.modify=a,e.set=i,e.use=s,c(function(){return!1},e)}function f(e){return e.enabled=!0,e.modify=a,e.set=i,e.use=s,c(function(){var t=Array.prototype.slice.call(arguments,0);return l.call(l,e,u(t,e)),!0},e)}e.exports=function(e){return e.introduce=c,e.enabled=o,e.process=u,e.modify=a,e.write=l,e.nope=h,e.yep=f,e.set=i,e.use=s,e}},5350:e=>{"use strict";var t=Object.prototype.toString;e.exports=function(e){if("string"==typeof e.displayName&&e.constructor.name)return e.displayName;if("string"==typeof e.name&&e.name)return e.name;if("object"==typeof e&&e.constructor&&"string"==typeof e.constructor.name)return e.constructor.name;var r=e.toString(),n=t.call(e).slice(8,-1);return(r="Function"===n?r.substring(r.indexOf("(")+1,r.indexOf(")")):n)||"anonymous"}},5546:(e,t,r)=>{"use strict";let n=r(20202);function s(e){if(e.every(i))return t=>{let r=t;for(let t=0;t<e.length;t++)if(!(r=e[t].transform(r,e[t].options)))return!1;return r}}function i(e){if("function"!=typeof e.transform)throw Error("No transform function found on format. Did you create a format instance?\nconst myFormat = format(formatFn);\nconst instance = myFormat();");return!0}e.exports=(...e)=>{let t=n(s(e)),r=t();return r.Format=t.Format,r},e.exports.cascade=s},9122:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r,s){let i=!1,o=!1,a=!1,l=0,u=0;function c(){l>=t||a||i||(a=!0,e.next().then(({value:e,done:t})=>{if(!o&&!i){if(a=!1,t){i=!0,l<=0&&s(null);return}l++,r(e,u,h),u++,c()}}).catch(f))}function h(e,t){if(l-=1,!o){if(e)return f(e);if(!1===e){i=!0,o=!0;return}if(t===n.default||i&&l<=0)return i=!0,s(null);c()}}function f(e){o||(a=!1,i=!0,s(e))}c()};var n=function(e){return e&&e.__esModule?e:{default:e}}(r(49686));e.exports=t.default},10204:(e,t,r)=>{"use strict";let{Stream:n,Transform:s}=r(97448),i=r(27411),{LEVEL:o,SPLAT:a}=r(5405),l=r(2444),u=r(51664),c=r(88514),h=r(30802),f=r(20667),{warn:d}=r(3935),p=r(46159),m=/%[scdjifoO%]/g;class g extends s{constructor(e){super({objectMode:!0}),this.configure(e)}child(e){let t=this;return Object.create(t,{write:{value:function(r){let n=Object.assign({},e,r);r instanceof Error&&(n.stack=r.stack,n.message=r.message),t.write(n)}}})}configure({silent:e,format:t,defaultMeta:n,levels:s,level:i="info",exitOnError:o=!0,transports:a,colors:l,emitErrs:h,formatters:f,padLevels:d,rewriters:m,stripColors:g,exceptionHandlers:y,rejectionHandlers:b}={}){if(this.transports.length&&this.clear(),this.silent=e,this.format=t||this.format||r(37945)(),this.defaultMeta=n||null,this.levels=s||this.levels||p.npm.levels,this.level=i,this.exceptions&&this.exceptions.unhandle(),this.rejections&&this.rejections.unhandle(),this.exceptions=new u(this),this.rejections=new c(this),this.profilers={},this.exitOnError=o,a&&(a=Array.isArray(a)?a:[a]).forEach(e=>this.add(e)),l||h||f||d||m||g)throw Error("{ colors, emitErrs, formatters, padLevels, rewriters, stripColors } were removed in winston@3.0.0.\nUse a custom winston.format(function) instead.\nSee: https://github.com/winstonjs/winston/tree/master/UPGRADE-3.0.md");y&&this.exceptions.handle(y),b&&this.rejections.handle(b)}isLevelEnabled(e){let t=y(this.levels,e);if(null===t)return!1;let r=y(this.levels,this.level);return null!==r&&(this.transports&&0!==this.transports.length?-1!==this.transports.findIndex(e=>{let n=y(this.levels,e.level);return null===n&&(n=r),n>=t}):r>=t)}log(e,t,...r){if(1==arguments.length)return e[o]=e.level,this._addDefaultMeta(e),this.write(e),this;if(2==arguments.length)return t&&"object"==typeof t?t[o]=t.level=e:t={[o]:e,level:e,message:t},this._addDefaultMeta(t),this.write(t),this;let[n]=r;if("object"==typeof n&&null!==n&&!(t&&t.match&&t.match(m))){let s=Object.assign({},this.defaultMeta,n,{[o]:e,[a]:r,level:e,message:t});return n.message&&(s.message=`${s.message} ${n.message}`),n.stack&&(s.stack=n.stack),n.cause&&(s.cause=n.cause),this.write(s),this}return this.write(Object.assign({},this.defaultMeta,{[o]:e,[a]:r,level:e,message:t})),this}_transform(e,t,r){if(this.silent)return r();e[o]||(e[o]=e.level),this.levels[e[o]]||0===this.levels[e[o]]||console.error("[winston] Unknown logger level: %s",e[o]),this._readableState.pipes||console.error("[winston] Attempt to write logs with no transports, which can increase memory usage: %j",e);try{this.push(this.format.transform(e,this.format.options))}finally{this._writableState.sync=!1,r()}}_final(e){i(this.transports.slice(),(e,t)=>{if(!e||e.finished)return setImmediate(t);e.once("finish",t),e.end()},e)}add(e){let t=!l(e)||e.log.length>2?new h({transport:e}):e;if(!t._writableState||!t._writableState.objectMode)throw Error("Transports must WritableStreams in objectMode. Set { objectMode: true }.");return this._onEvent("error",t),this._onEvent("warn",t),this.pipe(t),e.handleExceptions&&this.exceptions.handle(),e.handleRejections&&this.rejections.handle(),this}remove(e){if(!e)return this;let t=e;return(!l(e)||e.log.length>2)&&(t=this.transports.filter(t=>t.transport===e)[0]),t&&this.unpipe(t),this}clear(){return this.unpipe(),this}close(){return this.exceptions.unhandle(),this.rejections.unhandle(),this.clear(),this.emit("close"),this}setLevels(){d.deprecated("setLevels")}query(e,t){"function"==typeof e&&(t=e,e={});let r={},n=Object.assign({},(e=e||{}).query||{});i(this.transports.filter(e=>!!e.query),function(t,s){var i;i=(e,n)=>{s&&((n=e||n)&&(r[t.name]=n),s()),s=null},e.query&&"function"==typeof t.formatQuery&&(e.query=t.formatQuery(n)),t.query(e,(r,n)=>{if(r)return i(r);"function"==typeof t.formatResults&&(n=t.formatResults(n,e.format)),i(null,n)})},()=>t(null,r))}stream(e={}){let t=new n,r=[];return t._streams=r,t.destroy=()=>{let e=r.length;for(;e--;)r[e].destroy()},this.transports.filter(e=>!!e.stream).forEach(n=>{let s=n.stream(e);s&&(r.push(s),s.on("log",e=>{e.transport=e.transport||[],e.transport.push(n.name),t.emit("log",e)}),s.on("error",e=>{e.transport=e.transport||[],e.transport.push(n.name),t.emit("error",e)}))}),t}startTimer(){return new f(this)}profile(e,...t){let r=Date.now();if(this.profilers[e]){let n=this.profilers[e];delete this.profilers[e],"function"==typeof t[t.length-2]&&(console.warn("Callback function no longer supported as of winston@3.0.0"),t.pop());let s="object"==typeof t[t.length-1]?t.pop():{};return s.level=s.level||"info",s.durationMs=r-n,s.message=s.message||e,this.write(s)}return this.profilers[e]=r,this}handleExceptions(...e){console.warn("Deprecated: .handleExceptions() will be removed in winston@4. Use .exceptions.handle()"),this.exceptions.handle(...e)}unhandleExceptions(...e){console.warn("Deprecated: .unhandleExceptions() will be removed in winston@4. Use .exceptions.unhandle()"),this.exceptions.unhandle(...e)}cli(){throw Error("Logger.cli() was removed in winston@3.0.0\nUse a custom winston.formats.cli() instead.\nSee: https://github.com/winstonjs/winston/tree/master/UPGRADE-3.0.md")}_onEvent(e,t){t["__winston"+e]||(t["__winston"+e]=(function(r){"error"!==e||this.transports.includes(t)||this.add(t),this.emit(e,r,t)}).bind(this),t.on(e,t["__winston"+e]))}_addDefaultMeta(e){this.defaultMeta&&Object.assign(e,this.defaultMeta)}}function y(e,t){let r=e[t];return r||0===r?r:null}Object.defineProperty(g.prototype,"transports",{configurable:!1,enumerable:!0,get(){let{pipes:e}=this._readableState;return Array.isArray(e)?e:[e].filter(Boolean)}}),e.exports=g},13313:(e,t,r)=>{"use strict";let n=r(69409),{warn:s}=r(3935);t.version=r(90289).version,t.transports=r(77843),t.config=r(46159),t.addColors=n.levels,t.format=n.format,t.createLogger=r(26645),t.Logger=r(10204),t.ExceptionHandler=r(51664),t.RejectionHandler=r(88514),t.Container=r(25369),t.Transport=r(20989),t.loggers=new t.Container;let i=t.createLogger();Object.keys(t.config.npm.levels).concat(["log","query","stream","add","remove","clear","profile","startTimer","handleExceptions","unhandleExceptions","handleRejections","unhandleRejections","configure","child"]).forEach(e=>t[e]=(...t)=>i[e](...t)),Object.defineProperty(t,"level",{get:()=>i.level,set(e){i.level=e}}),Object.defineProperty(t,"exceptions",{get:()=>i.exceptions}),Object.defineProperty(t,"rejections",{get:()=>i.rejections}),["exitOnError"].forEach(e=>{Object.defineProperty(t,e,{get:()=>i[e],set(t){i[e]=t}})}),Object.defineProperty(t,"default",{get:()=>({exceptionHandlers:i.exceptionHandlers,rejectionHandlers:i.rejectionHandlers,transports:i.transports})}),s.deprecated(t,"setLevels"),s.forFunctions(t,"useFormat",["cli"]),s.forProperties(t,"useFormat",["padLevels","stripColors"]),s.forFunctions(t,"deprecated",["addRewriter","addFilter","clone","extend"]),s.forProperties(t,"deprecated",["emitErrs","levelLength"])},13897:(e,t,r)=>{"use strict";let n=r(2444),{MESSAGE:s}=r(5405),i=r(21820),o=r(20989);e.exports=class extends o{constructor(e={}){if(super(e),!e.stream||!n(e.stream))throw Error("options.stream is required.");this._stream=e.stream,this._stream.setMaxListeners(1/0),this.isObjectMode=e.stream._writableState.objectMode,this.eol="string"==typeof e.eol?e.eol:i.EOL}log(e,t){if(setImmediate(()=>this.emit("logged",e)),this.isObjectMode){this._stream.write(e),t&&t();return}this._stream.write(`${e[s]}${this.eol}`),t&&t()}}},16513:(e,t,r)=>{e.exports=r(4991)(function e(t,r){return((r=r||{}).namespace=t,r.prod=!0,r.dev=!1,r.force||e.force)?e.yep(r):e.nope(r)})},16971:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(...t){var r=t.pop();return e.call(this,t,r)}},e.exports=t.default},20202:e=>{"use strict";class t extends Error{constructor(e){super(`Format functions must be synchronous taking a two arguments: (info, opts)
Found: ${e.toString().split("\n")[0]}
`),Error.captureStackTrace(this,t)}}e.exports=e=>{if(e.length>2)throw new t(e);function r(e={}){this.options=e}function n(e){return new r(e)}return r.prototype.transform=e,n.Format=r,n}},20667:(e,t,r)=>{"use strict";class n{constructor(e){let t=r(10204);if("object"!=typeof e||Array.isArray(e)||!(e instanceof t))throw Error("Logger is required for profiling");this.logger=e,this.start=Date.now()}done(...e){"function"==typeof e[e.length-1]&&(console.warn("Callback function no longer supported as of winston@3.0.0"),e.pop());let t="object"==typeof e[e.length-1]?e.pop():{};return t.level=t.level||"info",t.durationMs=Date.now()-this.start,this.logger.write(t)}}e.exports=n},22884:(e,t,r)=>{"use strict";let{Writable:n}=r(97448);e.exports=class extends n{constructor(e){if(super({objectMode:!0}),!e)throw Error("ExceptionStream requires a TransportStream instance.");this.handleExceptions=!0,this.transport=e}_write(e,t,r){return e.exception?this.transport.log(e,r):(r(),!0)}}},25041:e=>{function t(e,t,r,n){return Math.round(e/r)+" "+n+(t>=1.5*r?"s":"")}e.exports=function(e,r){r=r||{};var n,s,i,o,a=typeof e;if("string"===a&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"weeks":case"week":case"w":return 6048e5*r;case"days":case"day":case"d":return 864e5*r;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*r;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*r;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}(e);if("number"===a&&isFinite(e)){return r.long?(s=Math.abs(n=e))>=864e5?t(n,s,864e5,"day"):s>=36e5?t(n,s,36e5,"hour"):s>=6e4?t(n,s,6e4,"minute"):s>=1e3?t(n,s,1e3,"second"):n+" ms":(o=Math.abs(i=e))>=864e5?Math.round(i/864e5)+"d":o>=36e5?Math.round(i/36e5)+"h":o>=6e4?Math.round(i/6e4)+"m":o>=1e3?Math.round(i/1e3)+"s":i+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},25369:(e,t,r)=>{"use strict";let n=r(26645);e.exports=class{constructor(e={}){this.loggers=new Map,this.options=e}add(e,t){if(!this.loggers.has(e)){let r=(t=Object.assign({},t||this.options)).transports||this.options.transports;r?t.transports=Array.isArray(r)?r.slice():[r]:t.transports=[];let s=n(t);s.on("close",()=>this._delete(e)),this.loggers.set(e,s)}return this.loggers.get(e)}get(e,t){return this.add(e,t)}has(e){return!!this.loggers.has(e)}close(e){if(e)return this._removeLogger(e);this.loggers.forEach((e,t)=>this._removeLogger(t))}_removeLogger(e){this.loggers.has(e)&&(this.loggers.get(e).close(),this._delete(e))}_delete(e){this.loggers.delete(e)}}},26414:e=>{e.exports=function(e){return function(t,r,n){if(" "===t)return t;switch(r%3){case 0:return e.red(t);case 1:return e.white(t);case 2:return e.blue(t)}}}},26645:(e,t,r)=>{"use strict";let{LEVEL:n}=r(5405),s=r(46159),i=r(10204),o=r(70456)("winston:create-logger");e.exports=function(e={}){e.levels=e.levels||s.npm.levels;class t extends i{constructor(e){super(e)}}let r=new t(e);return Object.keys(e.levels).forEach(function(e){if(o('Define prototype method for "%s"',e),"log"===e){console.warn('Level "log" not defined: conflicts with the method "log". Use a different level name.');return}t.prototype[e]=function(...t){let s=this||r;if(1===t.length){let[i]=t,o=i&&i.message&&i||{message:i};return o.level=o[n]=e,s._addDefaultMeta(o),s.write(o),this||r}return 0===t.length?(s.log(e,""),s):s.log(e,...t)},t.prototype["is"+e.charAt(0).toUpperCase()+e.slice(1)+"Enabled"]=function(){return(this||r).isLevelEnabled(e)}}),r}},26661:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return(0,i.isAsync)(e)?function(...t){let r=t.pop();return a(e.apply(this,t),r)}:(0,n.default)(function(t,r){var n;try{n=e.apply(this,t)}catch(e){return r(e)}if(n&&"function"==typeof n.then)return a(n,r);r(null,n)})};var n=o(r(16971)),s=o(r(33904)),i=r(45293);function o(e){return e&&e.__esModule?e:{default:e}}function a(e,t){return e.then(e=>{l(t,null,e)},e=>{l(t,e&&(e instanceof Error||e.message)?e:Error(e))})}function l(e,t,r){try{e(t,r)}catch(e){(0,s.default)(e=>{throw e},e)}}e.exports=t.default},27411:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=o(r(41371)),s=o(r(88723)),i=o(r(45293));function o(e){return e&&e.__esModule?e:{default:e}}t.default=(0,o(r(64356)).default)(function(e,t,r){return(0,n.default)(e,(0,s.default)((0,i.default)(t)),r)},3),e.exports=t.default},29698:(e,t,r)=>{"use strict";let{Writable:n}=r(97448);e.exports=class extends n{constructor(e){if(super({objectMode:!0}),!e)throw Error("RejectionStream requires a TransportStream instance.");this.handleRejections=!0,this.transport=e}_write(e,t,r){return e.rejection?this.transport.log(e,r):(r(),!0)}}},31556:(e,t,r)=>{"use strict";let n=r(21820),{LEVEL:s,MESSAGE:i}=r(5405),o=r(20989);e.exports=class extends o{constructor(e={}){super(e),this.name=e.name||"console",this.stderrLevels=this._stringArrayToSet(e.stderrLevels),this.consoleWarnLevels=this._stringArrayToSet(e.consoleWarnLevels),this.eol="string"==typeof e.eol?e.eol:n.EOL,this.forceConsole=e.forceConsole||!1,this._consoleLog=console.log.bind(console),this._consoleWarn=console.warn.bind(console),this._consoleError=console.error.bind(console),this.setMaxListeners(30)}log(e,t){if(setImmediate(()=>this.emit("logged",e)),this.stderrLevels[e[s]]){console._stderr&&!this.forceConsole?console._stderr.write(`${e[i]}${this.eol}`):this._consoleError(e[i]),t&&t();return}if(this.consoleWarnLevels[e[s]]){console._stderr&&!this.forceConsole?console._stderr.write(`${e[i]}${this.eol}`):this._consoleWarn(e[i]),t&&t();return}console._stdout&&!this.forceConsole?console._stdout.write(`${e[i]}${this.eol}`):this._consoleLog(e[i]),t&&t()}_stringArrayToSet(e,t){if(!e)return{};if(t=t||"Cannot make set from type other than Array of string elements",!Array.isArray(e))throw Error(t);return e.reduce((e,r)=>{if("string"!=typeof r)throw Error(t);return e[r]=!0,e},{})}}},33904:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fallback=o,t.wrap=a;var r,n=t.hasQueueMicrotask="function"==typeof queueMicrotask&&queueMicrotask,s=t.hasSetImmediate="function"==typeof setImmediate&&setImmediate,i=t.hasNextTick="object"==typeof process&&"function"==typeof process.nextTick;function o(e){setTimeout(e,0)}function a(e){return(t,...r)=>e(()=>t(...r))}t.default=a(n?queueMicrotask:s?setImmediate:i?process.nextTick:o)},33985:(e,t,r)=>{"use strict";r.r(t),r.d(t,{assign:()=>c,default:()=>k,defaultI18n:()=>p,format:()=>S,parse:()=>T,setGlobalDateI18n:()=>g,setGlobalDateMasks:()=>$});var n=/d{1,4}|M{1,4}|YY(?:YY)?|S{1,3}|Do|ZZ|Z|([HhMsDm])\1?|[aA]|"[^"]*"|'[^']*'/g,s="\\d\\d?",i="\\d\\d",o="[^\\s]+",a=/\[([^]*?)\]/gm;function l(e,t){for(var r=[],n=0,s=e.length;n<s;n++)r.push(e[n].substr(0,t));return r}var u=function(e){return function(t,r){var n=r[e].map(function(e){return e.toLowerCase()}).indexOf(t.toLowerCase());return n>-1?n:null}};function c(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];for(var n=0;n<t.length;n++){var s=t[n];for(var i in s)e[i]=s[i]}return e}var h=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],f=["January","February","March","April","May","June","July","August","September","October","November","December"],d=l(f,3),p={dayNamesShort:l(h,3),dayNames:h,monthNamesShort:d,monthNames:f,amPm:["am","pm"],DoFn:function(e){return e+["th","st","nd","rd"][e%10>3?0:+(e-e%10!=10)*e%10]}},m=c({},p),g=function(e){return m=c(m,e)},y=function(e){return e.replace(/[|\\{()[^$+*?.-]/g,"\\$&")},b=function(e,t){for(void 0===t&&(t=2),e=String(e);e.length<t;)e="0"+e;return e},v={D:function(e){return String(e.getDate())},DD:function(e){return b(e.getDate())},Do:function(e,t){return t.DoFn(e.getDate())},d:function(e){return String(e.getDay())},dd:function(e){return b(e.getDay())},ddd:function(e,t){return t.dayNamesShort[e.getDay()]},dddd:function(e,t){return t.dayNames[e.getDay()]},M:function(e){return String(e.getMonth()+1)},MM:function(e){return b(e.getMonth()+1)},MMM:function(e,t){return t.monthNamesShort[e.getMonth()]},MMMM:function(e,t){return t.monthNames[e.getMonth()]},YY:function(e){return b(String(e.getFullYear()),4).substr(2)},YYYY:function(e){return b(e.getFullYear(),4)},h:function(e){return String(e.getHours()%12||12)},hh:function(e){return b(e.getHours()%12||12)},H:function(e){return String(e.getHours())},HH:function(e){return b(e.getHours())},m:function(e){return String(e.getMinutes())},mm:function(e){return b(e.getMinutes())},s:function(e){return String(e.getSeconds())},ss:function(e){return b(e.getSeconds())},S:function(e){return String(Math.round(e.getMilliseconds()/100))},SS:function(e){return b(Math.round(e.getMilliseconds()/10),2)},SSS:function(e){return b(e.getMilliseconds(),3)},a:function(e,t){return 12>e.getHours()?t.amPm[0]:t.amPm[1]},A:function(e,t){return 12>e.getHours()?t.amPm[0].toUpperCase():t.amPm[1].toUpperCase()},ZZ:function(e){var t=e.getTimezoneOffset();return(t>0?"-":"+")+b(100*Math.floor(Math.abs(t)/60)+Math.abs(t)%60,4)},Z:function(e){var t=e.getTimezoneOffset();return(t>0?"-":"+")+b(Math.floor(Math.abs(t)/60),2)+":"+b(Math.abs(t)%60,2)}},_=function(e){return+e-1},w=[null,s],x=[null,o],M=["isPm",o,function(e,t){var r=e.toLowerCase();return r===t.amPm[0]?0:r===t.amPm[1]?1:null}],O=["timezoneOffset","[^\\s]*?[\\+\\-]\\d\\d:?\\d\\d|[^\\s]*?Z?",function(e){var t=(e+"").match(/([+-]|\d\d)/gi);if(t){var r=60*+t[1]+parseInt(t[2],10);return"+"===t[0]?r:-r}return 0}],E={D:["day",s],DD:["day",i],Do:["day",s+o,function(e){return parseInt(e,10)}],M:["month",s,_],MM:["month",i,_],YY:["year",i,function(e){var t=+(""+new Date().getFullYear()).substr(0,2);return+(""+(+e>68?t-1:t)+e)}],h:["hour",s,void 0,"isPm"],hh:["hour",i,void 0,"isPm"],H:["hour",s],HH:["hour",i],m:["minute",s],mm:["minute",i],s:["second",s],ss:["second",i],YYYY:["year","\\d{4}"],S:["millisecond","\\d",function(e){return 100*+e}],SS:["millisecond",i,function(e){return 10*+e}],SSS:["millisecond","\\d{3}"],d:w,dd:w,ddd:x,dddd:x,MMM:["month",o,u("monthNamesShort")],MMMM:["month",o,u("monthNames")],a:M,A:M,ZZ:O,Z:O},j={default:"ddd MMM DD YYYY HH:mm:ss",shortDate:"M/D/YY",mediumDate:"MMM D, YYYY",longDate:"MMMM D, YYYY",fullDate:"dddd, MMMM D, YYYY",isoDate:"YYYY-MM-DD",isoDateTime:"YYYY-MM-DDTHH:mm:ssZ",shortTime:"HH:mm",mediumTime:"HH:mm:ss",longTime:"HH:mm:ss.SSS"},$=function(e){return c(j,e)},S=function(e,t,r){if(void 0===t&&(t=j.default),void 0===r&&(r={}),"number"==typeof e&&(e=new Date(e)),"[object Date]"!==Object.prototype.toString.call(e)||isNaN(e.getTime()))throw Error("Invalid Date pass to format");t=j[t]||t;var s=[];t=t.replace(a,function(e,t){return s.push(t),"@@@"});var i=c(c({},m),r);return(t=t.replace(n,function(t){return v[t](e,i)})).replace(/@@@/g,function(){return s.shift()})};function T(e,t,r){if(void 0===r&&(r={}),"string"!=typeof t)throw Error("Invalid format in fecha parse");if(t=j[t]||t,e.length>1e3)return null;var s,i={year:new Date().getFullYear(),month:0,day:1,hour:0,minute:0,second:0,millisecond:0,isPm:null,timezoneOffset:null},o=[],l=[],u=t.replace(a,function(e,t){return l.push(y(t)),"@@@"}),h={},f={};u=y(u).replace(n,function(e){var t=E[e],r=t[0],n=t[1],s=t[3];if(h[r])throw Error("Invalid format. "+r+" specified twice in format");return h[r]=!0,s&&(f[s]=!0),o.push(t),"("+n+")"}),Object.keys(f).forEach(function(e){if(!h[e])throw Error("Invalid format. "+e+" is required in specified format")}),u=u.replace(/@@@/g,function(){return l.shift()});var d=e.match(RegExp(u,"i"));if(!d)return null;for(var p=c(c({},m),r),g=1;g<d.length;g++){var b=o[g-1],v=b[0],_=b[2],w=_?_(d[g],p):+d[g];if(null==w)return null;i[v]=w}if(1===i.isPm&&null!=i.hour&&12!=+i.hour?i.hour=+i.hour+12:0===i.isPm&&12==+i.hour&&(i.hour=0),null==i.timezoneOffset){s=new Date(i.year,i.month,i.day,i.hour,i.minute,i.second,i.millisecond);for(var x=[["month","getMonth"],["day","getDate"],["hour","getHours"],["minute","getMinutes"],["second","getSeconds"]],g=0,M=x.length;g<M;g++)if(h[x[g][0]]&&i[x[g][0]]!==s[x[g][1]]())return null}else if(s=new Date(Date.UTC(i.year,i.month,i.day,i.hour,i.minute-i.timezoneOffset,i.second,i.millisecond)),i.month>11||i.month<0||i.day>31||i.day<1||i.hour>23||i.hour<0||i.minute>59||i.minute<0||i.second>59||i.second<0)return null;return s}let k={format:S,parse:T,defaultI18n:p,setGlobalDateI18n:g,setGlobalDateMasks:$}},34717:(e,t,r)=>{e.exports=r(74002)},35993:(e,t,r)=>{"use strict";let n=r(81630),s=r(55591),{Stream:i}=r(97448),o=r(20989),{configure:a}=r(60512);e.exports=class extends o{constructor(e={}){super(e),this.options=e,this.name=e.name||"http",this.ssl=!!e.ssl,this.host=e.host||"localhost",this.port=e.port,this.auth=e.auth,this.path=e.path||"",this.maximumDepth=e.maximumDepth,this.agent=e.agent,this.headers=e.headers||{},this.headers["content-type"]="application/json",this.batch=e.batch||!1,this.batchInterval=e.batchInterval||5e3,this.batchCount=e.batchCount||10,this.batchOptions=[],this.batchTimeoutID=-1,this.batchCallback={},this.port||(this.port=this.ssl?443:80)}log(e,t){this._request(e,null,null,(t,r)=>{r&&200!==r.statusCode&&(t=Error(`Invalid HTTP Status Code: ${r.statusCode}`)),t?this.emit("warn",t):this.emit("logged",e)}),t&&setImmediate(t)}query(e,t){"function"==typeof e&&(t=e,e={});let r=(e={method:"query",params:this.normalizeQuery(e)}).params.auth||null;delete e.params.auth;let n=e.params.path||null;delete e.params.path,this._request(e,r,n,(e,r,n)=>{if(r&&200!==r.statusCode&&(e=Error(`Invalid HTTP Status Code: ${r.statusCode}`)),e)return t(e);if("string"==typeof n)try{n=JSON.parse(n)}catch(e){return t(e)}t(null,n)})}stream(e={}){let t=new i,r=(e={method:"stream",params:e}).params.path||null;delete e.params.path;let n=e.params.auth||null;delete e.params.auth;let s="",o=this._request(e,n,r);return t.destroy=()=>o.destroy(),o.on("data",e=>{let r=(e=(s+e).split(/\n+/)).length-1,n=0;for(;n<r;n++)try{t.emit("log",JSON.parse(e[n]))}catch(e){t.emit("error",e)}s=e[r]}),o.on("error",e=>t.emit("error",e)),t}_request(e,t,r,n){e=e||{},t=t||this.auth,r=r||this.path||"",this.batch?this._doBatch(e,n,t,r):this._doRequest(e,n,t,r)}_doBatch(e,t,r,n){if(this.batchOptions.push(e),1===this.batchOptions.length){let e=this;this.batchCallback=t,this.batchTimeoutID=setTimeout(function(){e.batchTimeoutID=-1,e._doBatchRequest(e.batchCallback,r,n)},this.batchInterval)}this.batchOptions.length===this.batchCount&&this._doBatchRequest(this.batchCallback,r,n)}_doBatchRequest(e,t,r){this.batchTimeoutID>0&&(clearTimeout(this.batchTimeoutID),this.batchTimeoutID=-1);let n=this.batchOptions.slice();this.batchOptions=[],this._doRequest(n,e,t,r)}_doRequest(e,t,r,i){let o=Object.assign({},this.headers);r&&r.bearer&&(o.Authorization=`Bearer ${r.bearer}`);let l=(this.ssl?s:n).request({...this.options,method:"POST",host:this.host,port:this.port,path:`/${i.replace(/^\//,"")}`,headers:o,auth:r&&r.username&&r.password?`${r.username}:${r.password}`:"",agent:this.agent});l.on("error",t),l.on("response",e=>e.on("end",()=>t(null,e)).resume());let u=a({...this.maximumDepth&&{maximumDepth:this.maximumDepth}});l.end(Buffer.from(u(e,this.options.replacer),"utf8"))}}},36188:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=u(r(79672)),s=u(r(57669)),i=u(r(53130)),o=r(45293),a=u(r(9122)),l=u(r(49686));function u(e){return e&&e.__esModule?e:{default:e}}t.default=e=>(t,r,u)=>{if(u=(0,n.default)(u),e<=0)throw RangeError("concurrency limit cannot be less than 1");if(!t)return u(null);if((0,o.isAsyncGenerator)(t))return(0,a.default)(t,e,r,u);if((0,o.isAsyncIterable)(t))return(0,a.default)(t[Symbol.asyncIterator](),e,r,u);var c=(0,s.default)(t),h=!1,f=!1,d=0,p=!1;function m(e,t){if(!f){if(d-=1,e)h=!0,u(e);else if(!1===e)h=!0,f=!0;else{if(t===l.default||h&&d<=0)return h=!0,u(null);p||g()}}}function g(){for(p=!0;d<e&&!h;){var t=c();if(null===t){h=!0,d<=0&&u(null);return}d+=1,r(t.value,t.key,(0,i.default)(m))}p=!1}g()},e.exports=t.default},37945:(e,t,r)=>{"use strict";let n=r(20202),{MESSAGE:s}=r(5405),i=r(60512);function o(e,t){return"bigint"==typeof t?t.toString():t}e.exports=n((e,t)=>{let r=i.configure(t);return e[s]=r(e,t.replacer||o,t.space),e})},38444:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){return(0,n.default)(s.default,e,t)};var n=i(r(66538)),s=i(r(82008));function i(e){return e&&e.__esModule?e:{default:e}}e.exports=t.default},40749:(e,t,r)=>{"use strict";let n=r(29021),{StringDecoder:s}=r(41204),{Stream:i}=r(97448);function o(){}e.exports=(e,t)=>{let r=Buffer.alloc(65536),a=new s("utf8"),l=new i,u="",c=0,h=0;return(-1===e.start&&delete e.start,l.readable=!0,l.destroy=()=>{l.destroyed=!0,l.emit("end"),l.emit("close")},n.open(e.file,"a+","0644",(s,i)=>{if(s){t?t(s):l.emit("error",s),l.destroy();return}!function s(){if(l.destroyed){n.close(i,o);return}return n.read(i,r,0,r.length,c,(n,i)=>{if(n){t?t(n):l.emit("error",n),l.destroy();return}if(!i)return u&&((null==e.start||h>e.start)&&(t?t(null,u):l.emit("line",u)),h++,u=""),setTimeout(s,1e3);let o=a.write(r.slice(0,i));t||l.emit("data",o);let f=(o=(u+o).split(/\n+/)).length-1,d=0;for(;d<f;d++)(null==e.start||h>e.start)&&(t?t(null,o[d]):l.emit("line",o[d])),h++;return u=o[f],c+=i,s()})}()}),t)?l.destroy:l}},41371:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=c(r(43935)),s=c(r(49686)),i=c(r(63692)),o=c(r(79672)),a=c(r(53130)),l=c(r(45293)),u=c(r(64356));function c(e){return e&&e.__esModule?e:{default:e}}t.default=(0,u.default)(function(e,t,r){return((0,n.default)(e)?function(e,t,r){r=(0,o.default)(r);var n=0,i=0,{length:l}=e,u=!1;function c(e,t){!1===e&&(u=!0),!0!==u&&(e?r(e):(++i===l||t===s.default)&&r(null))}for(0===l&&r(null);n<l;n++)t(e[n],n,(0,a.default)(c))}:function(e,t,r){return(0,i.default)(e,1/0,t,r)})(e,(0,l.default)(t),r)},3),e.exports=t.default},42026:(e,t,r)=>{"use strict";let{configs:n,LEVEL:s,MESSAGE:i}=r(5405);class o{constructor(e={levels:n.npm.levels}){this.paddings=o.paddingForLevels(e.levels,e.filler),this.options=e}static getLongestLevel(e){return Math.max(...Object.keys(e).map(e=>e.length))}static paddingForLevel(e,t,r){let n=r+1-e.length,s=Math.floor(n/t.length);return`${t}${t.repeat(s)}`.slice(0,n)}static paddingForLevels(e,t=" "){let r=o.getLongestLevel(e);return Object.keys(e).reduce((e,n)=>(e[n]=o.paddingForLevel(n,t,r),e),{})}transform(e,t){return e.message=`${this.paddings[e[s]]}${e.message}`,e[i]&&(e[i]=`${this.paddings[e[s]]}${e[i]}`),e}}e.exports=e=>new o(e),e.exports.Padder=e.exports.Format=o},43935:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return e&&"number"==typeof e.length&&e.length>=0&&e.length%1==0},e.exports=t.default},45203:e=>{e.exports=function(e){var t=["underline","inverse","grey","yellow","red","green","blue","white","cyan","magenta","brightYellow","brightRed","brightGreen","brightBlue","brightWhite","brightCyan","brightMagenta"];return function(r,n,s){return" "===r?r:e[t[Math.round(Math.random()*(t.length-2))]](r)}}},45293:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isAsyncIterable=t.isAsyncGenerator=t.isAsync=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(r(26661));function s(e){return"AsyncFunction"===e[Symbol.toStringTag]}t.default=function(e){if("function"!=typeof e)throw Error("expected a function");return s(e)?(0,n.default)(e):e},t.isAsync=s,t.isAsyncGenerator=function(e){return"AsyncGenerator"===e[Symbol.toStringTag]},t.isAsyncIterable=function(e){return"function"==typeof e[Symbol.asyncIterator]}},46159:(e,t,r)=>{"use strict";let n=r(69409),{configs:s}=r(5405);t.cli=n.levels(s.cli),t.npm=n.levels(s.npm),t.syslog=n.levels(s.syslog),t.addColors=n.levels},47439:(e,t,r)=>{"use strict";let n=r(29021),s=r(33873),i=r(38444),o=r(74075),{MESSAGE:a}=r(5405),{Stream:l,PassThrough:u}=r(97448),c=r(20989),h=r(70456)("winston:file"),f=r(21820),d=r(40749);e.exports=class extends c{constructor(e={}){function t(r,...n){n.slice(1).forEach(t=>{if(e[t])throw Error(`Cannot set ${t} and ${r} together`)})}if(super(e),this.name=e.name||"file",this._stream=new u,this._stream.setMaxListeners(30),this._onError=this._onError.bind(this),e.filename||e.dirname)t("filename or dirname","stream"),this._basename=this.filename=e.filename?s.basename(e.filename):"winston.log",this.dirname=e.dirname||s.dirname(e.filename),this.options=e.options||{flags:"a"};else if(e.stream)console.warn("options.stream will be removed in winston@4. Use winston.transports.Stream"),t("stream","filename","maxsize"),this._dest=this._stream.pipe(this._setupStream(e.stream)),this.dirname=s.dirname(this._dest.path);else throw Error("Cannot log to file without filename or stream.");this.maxsize=e.maxsize||null,this.rotationFormat=e.rotationFormat||!1,this.zippedArchive=e.zippedArchive||!1,this.maxFiles=e.maxFiles||null,this.eol="string"==typeof e.eol?e.eol:f.EOL,this.tailable=e.tailable||!1,this.lazy=e.lazy||!1,this._size=0,this._pendingSize=0,this._created=0,this._drain=!1,this._opening=!1,this._ending=!1,this._fileExist=!1,this.dirname&&this._createLogDirIfNotExist(this.dirname),this.lazy||this.open()}finishIfEnding(){this._ending&&(this._opening?this.once("open",()=>{this._stream.once("finish",()=>this.emit("finish")),setImmediate(()=>this._stream.end())}):(this._stream.once("finish",()=>this.emit("finish")),setImmediate(()=>this._stream.end())))}log(e,t=()=>{}){if(this.silent)return t(),!0;if(this._drain){this._stream.once("drain",()=>{this._drain=!1,this.log(e,t)});return}if(this._rotate){this._stream.once("rotate",()=>{this._rotate=!1,this.log(e,t)});return}if(this.lazy){if(!this._fileExist){this._opening||this.open(),this.once("open",()=>{this._fileExist=!0,this.log(e,t)});return}if(this._needsNewFile(this._pendingSize)){this._dest.once("close",()=>{this._opening||this.open(),this.once("open",()=>{this.log(e,t)})});return}}let r=`${e[a]}${this.eol}`,n=Buffer.byteLength(r);this._pendingSize+=n,this._opening&&!this.rotatedWhileOpening&&this._needsNewFile(this._size+this._pendingSize)&&(this.rotatedWhileOpening=!0);let s=this._stream.write(r,(function(){if(this._size+=n,this._pendingSize-=n,h("logged %s %s",this._size,r),this.emit("logged",e),!this._rotate&&!this._opening&&this._needsNewFile()){if(this.lazy){this._endStream(()=>{this.emit("fileclosed")});return}this._rotate=!0,this._endStream(()=>this._rotateFile())}}).bind(this));return s?t():(this._drain=!0,this._stream.once("drain",()=>{this._drain=!1,t()})),h("written",s,this._drain),this.finishIfEnding(),s}query(e,t){var r;"function"==typeof e&&(t=e,e={}),(r=(r=e)||{}).rows=r.rows||r.limit||10,r.start=r.start||0,r.until=r.until||new Date,"object"!=typeof r.until&&(r.until=new Date(r.until)),r.from=r.from||r.until-864e5,"object"!=typeof r.from&&(r.from=new Date(r.from)),r.order=r.order||"desc",e=r;let i=s.join(this.dirname,this.filename),o="",a=[],l=0,u=n.createReadStream(i,{encoding:"utf8"});function c(t,r){try{let r=JSON.parse(t);(function(t){if(!t||"object"!=typeof t)return;let r=new Date(t.timestamp);if((!e.from||!(r<e.from))&&(!e.until||!(r>e.until))&&(!e.level||e.level===t.level))return!0})(r)&&function(t){if(e.rows&&a.length>=e.rows&&"desc"!==e.order){u.readable&&u.destroy();return}e.fields&&(t=e.fields.reduce((e,r)=>(e[r]=t[r],e),{})),"desc"===e.order&&a.length>=e.rows&&a.shift(),a.push(t)}(r)}catch(e){r||u.emit("error",e)}}u.on("error",e=>{if(u.readable&&u.destroy(),t)return"ENOENT"!==e.code?t(e):t(null,a)}),u.on("data",t=>{let r=(t=(o+t).split(/\n+/)).length-1,n=0;for(;n<r;n++)(!e.start||l>=e.start)&&c(t[n]),l++;o=t[r]}),u.on("close",()=>{o&&c(o,!0),"desc"===e.order&&(a=a.reverse()),t&&t(null,a)})}stream(e={}){let t=s.join(this.dirname,this.filename),r=new l;return r.destroy=d({file:t,start:e.start},(e,t)=>{if(e)return r.emit("error",e);try{r.emit("data",t),t=JSON.parse(t),r.emit("log",t)}catch(e){r.emit("error",e)}}),r}open(){this.filename&&(this._opening||(this._opening=!0,this.stat((e,t)=>{if(e)return this.emit("error",e);h("stat done: %s { size: %s }",this.filename,t),this._size=t,this._dest=this._createStream(this._stream),this._opening=!1,this.once("open",()=>{this._stream.emit("rotate")||(this._rotate=!1)})})))}stat(e){let t=this._getFile(),r=s.join(this.dirname,t);n.stat(r,(n,s)=>n&&"ENOENT"===n.code?(h("ENOENT\xa0ok",r),this.filename=t,e(null,0)):n?(h(`err ${n.code} ${r}`),e(n)):!s||this._needsNewFile(s.size)?this._incFile(()=>this.stat(e)):void(this.filename=t,e(null,s.size)))}close(e){this._stream&&this._stream.end(()=>{e&&e(),this.emit("flush"),this.emit("closed")})}_needsNewFile(e){return e=e||this._size,this.maxsize&&e>=this.maxsize}_onError(e){this.emit("error",e)}_setupStream(e){return e.on("error",this._onError),e}_cleanupStream(e){return e.removeListener("error",this._onError),e.destroy(),e}_rotateFile(){this._incFile(()=>this.open())}_endStream(e=()=>{}){this._dest?(this._stream.unpipe(this._dest),this._dest.end(()=>{this._cleanupStream(this._dest),e()})):e()}_createStream(e){let t=s.join(this.dirname,this.filename);h("create stream start",t,this.options);let r=n.createWriteStream(t,this.options).on("error",e=>h(e)).on("close",()=>h("close",r.path,r.bytesWritten)).on("open",()=>{h("file open ok",t),this.emit("open",t),e.pipe(r),this.rotatedWhileOpening&&(this._stream=new u,this._stream.setMaxListeners(30),this._rotateFile(),this.rotatedWhileOpening=!1,this._cleanupStream(r),e.end())});return h("create stream ok",t),r}_incFile(e){h("_incFile",this.filename);let t=s.extname(this._basename),r=s.basename(this._basename,t),n=[];this.zippedArchive&&n.push((function(e){let n=this._created>0&&!this.tailable?this._created:"";this._compressFile(s.join(this.dirname,`${r}${n}${t}`),s.join(this.dirname,`${r}${n}${t}.gz`),e)}).bind(this)),n.push((function(e){this.tailable?this._checkMaxFilesTailable(t,r,e):(this._created+=1,this._checkMaxFilesIncrementing(t,r,e))}).bind(this)),i(n,e)}_getFile(){let e=s.extname(this._basename),t=s.basename(this._basename,e),r=this.rotationFormat?this.rotationFormat():this._created;return!this.tailable&&this._created?`${t}${r}${e}`:`${t}${e}`}_checkMaxFilesIncrementing(e,t,r){if(!this.maxFiles||this._created<this.maxFiles)return setImmediate(r);let i=this._created-this.maxFiles,o=this.zippedArchive?".gz":"",a=`${t}${0!==i?i:""}${e}${o}`,l=s.join(this.dirname,a);n.unlink(l,r)}_checkMaxFilesTailable(e,t,r){let o=[];if(!this.maxFiles)return;let a=this.zippedArchive?".gz":"";for(let r=this.maxFiles-1;r>1;r--)o.push((function(r,i){let o=`${t}${r-1}${e}${a}`,l=s.join(this.dirname,o);n.exists(l,u=>{if(!u)return i(null);o=`${t}${r}${e}${a}`,n.rename(l,s.join(this.dirname,o),i)})}).bind(this,r));i(o,()=>{n.rename(s.join(this.dirname,`${t}${e}${a}`),s.join(this.dirname,`${t}1${e}${a}`),r)})}_compressFile(e,t,r){n.access(e,n.F_OK,s=>{if(s)return r();var i=o.createGzip(),a=n.createReadStream(e),l=n.createWriteStream(t);l.on("finish",()=>{n.unlink(e,r)}),a.pipe(i).pipe(l)})}_createLogDirIfNotExist(e){n.existsSync(e)||n.mkdirSync(e,{recursive:!0})}}},49686:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={},e.exports=t.default},50571:(e,t,r)=>{"use strict";var n=r(5350);e.exports=function(e){var t,r=0;function s(){return r||(r=1,t=e.apply(this,arguments),e=null),t}return s.displayName=n(e),s}},51664:(e,t,r)=>{"use strict";let n=r(21820),s=r(27411),i=r(70456)("winston:exception"),o=r(50571),a=r(70205),l=r(22884);e.exports=class{constructor(e){if(!e)throw Error("Logger is required to handle exceptions");this.logger=e,this.handlers=new Map}handle(...e){e.forEach(e=>{if(Array.isArray(e))return e.forEach(e=>this._addHandler(e));this._addHandler(e)}),this.catcher||(this.catcher=this._uncaughtException.bind(this),process.on("uncaughtException",this.catcher))}unhandle(){this.catcher&&(process.removeListener("uncaughtException",this.catcher),this.catcher=!1,Array.from(this.handlers.values()).forEach(e=>this.logger.unpipe(e)))}getAllInfo(e){let t=null;return e&&(t="string"==typeof e?e:e.message),{error:e,level:"error",message:[`uncaughtException: ${t||"(no error message)"}`,e&&e.stack||"  No stack trace"].join("\n"),stack:e&&e.stack,exception:!0,date:new Date().toString(),process:this.getProcessInfo(),os:this.getOsInfo(),trace:this.getTrace(e)}}getProcessInfo(){return{pid:process.pid,uid:process.getuid?process.getuid():null,gid:process.getgid?process.getgid():null,cwd:process.cwd(),execPath:process.execPath,version:process.version,argv:process.argv,memoryUsage:process.memoryUsage()}}getOsInfo(){return{loadavg:n.loadavg(),uptime:n.uptime()}}getTrace(e){return(e?a.parse(e):a.get()).map(e=>({column:e.getColumnNumber(),file:e.getFileName(),function:e.getFunctionName(),line:e.getLineNumber(),method:e.getMethodName(),native:e.isNative()}))}_addHandler(e){if(!this.handlers.has(e)){e.handleExceptions=!0;let t=new l(e);this.handlers.set(e,t),this.logger.pipe(t)}}_uncaughtException(e){let t;let r=this.getAllInfo(e),n=this._getExceptionHandlers(),a="function"==typeof this.logger.exitOnError?this.logger.exitOnError(e):this.logger.exitOnError;function l(){i("doExit",a),i("process._exiting",process._exiting),a&&!process._exiting&&(t&&clearTimeout(t),process.exit(1))}if(!n.length&&a&&(console.warn("winston: exitOnError cannot be true with no exception handlers."),console.warn("winston: not exiting process."),a=!1),!n||0===n.length)return process.nextTick(l);s(n,(e,t)=>{let r=o(t),n=e.transport||e;function s(e){return()=>{i(e),r()}}n._ending=!0,n.once("finish",s("finished")),n.once("error",s("error"))},()=>a&&l()),this.logger.log(r),a&&(t=setTimeout(l,3e3))}_getExceptionHandlers(){return this.logger.transports.filter(e=>(e.transport||e).handleExceptions)}}},53130:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return function(...t){if(null===e)throw Error("Callback was already called.");var r=e;e=null,r.apply(this,t)}},e.exports=t.default},56046:(e,t,r)=>{"use strict";let n=r(20202),{LEVEL:s,MESSAGE:i}=r(5405);e.exports=n((e,{stack:t,cause:r})=>{if(e instanceof Error){let n=Object.assign({},e,{level:e.level,[s]:e[s]||e.level,message:e.message,[i]:e[i]||e.message});return t&&(n.stack=e.stack),r&&(n.cause=e.cause),n}if(!(e.message instanceof Error))return e;let n=e.message;return Object.assign(e,n),e.message=n.message,e[i]=n.message,t&&(e.stack=n.stack),r&&(e.cause=n.cause),e})},57638:e=>{e.exports=function(e){return function(t,r,n){return r%2==0?t:e.inverse(t)}}},57669:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if((0,n.default)(e))return t=-1,r=e.length,function(){return++t<r?{value:e[t],key:t}:null};var t,r,i,o,a,l,u=(0,s.default)(e);return u?(i=-1,function(){var e=u.next();return e.done?null:(i++,{value:e.value,key:i})}):(o=e?Object.keys(e):[],a=-1,l=o.length,function t(){var r=o[++a];return"__proto__"===r?t():a<l?{value:e[r],key:r}:null})};var n=i(r(43935)),s=i(r(69063));function i(e){return e&&e.__esModule?e:{default:e}}e.exports=t.default},60512:(e,t)=>{"use strict";let{hasOwnProperty:r}=Object.prototype,n=f();n.configure=f,n.stringify=n,n.default=n,t.stringify=n,t.configure=f,e.exports=n;let s=/[\u0000-\u001f\u0022\u005c\ud800-\udfff]/;function i(e){return e.length<5e3&&!s.test(e)?`"${e}"`:JSON.stringify(e)}function o(e,t){if(e.length>200||t)return e.sort(t);for(let t=1;t<e.length;t++){let r=e[t],n=t;for(;0!==n&&e[n-1]>r;)e[n]=e[n-1],n--;e[n]=r}return e}let a=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(Object.getPrototypeOf(new Int8Array)),Symbol.toStringTag).get;function l(e){return void 0!==a.call(e)&&0!==e.length}function u(e,t,r){e.length<r&&(r=e.length);let n=","===t?"":" ",s=`"0":${n}${e[0]}`;for(let i=1;i<r;i++)s+=`${t}"${i}":${n}${e[i]}`;return s}function c(e,t){let n;if(r.call(e,t)){if("number"!=typeof(n=e[t]))throw TypeError(`The "${t}" argument must be of type number`);if(!Number.isInteger(n))throw TypeError(`The "${t}" argument must be an integer`);if(n<1)throw RangeError(`The "${t}" argument must be >= 1`)}return void 0===n?1/0:n}function h(e){return 1===e?"1 item":`${e} items`}function f(e){let t=function(e){if(r.call(e,"strict")){let t=e.strict;if("boolean"!=typeof t)throw TypeError('The "strict" argument must be of type boolean');if(t)return e=>{let t=`Object can not safely be stringified. Received type ${typeof e}`;throw"function"!=typeof e&&(t+=` (${e.toString()})`),Error(t)}}}(e={...e});!t||(void 0===e.bigint&&(e.bigint=!1),"circularValue"in e||(e.circularValue=Error));let n=function(e){if(r.call(e,"circularValue")){let t=e.circularValue;if("string"==typeof t)return`"${t}"`;if(null==t)return t;if(t===Error||t===TypeError)return{toString(){throw TypeError("Converting circular structure to JSON")}};throw TypeError('The "circularValue" argument must be of type string or the value null or undefined')}return'"[Circular]"'}(e),s=function(e,t){let n;if(r.call(e,t)&&"boolean"!=typeof(n=e[t]))throw TypeError(`The "${t}" argument must be of type boolean`);return void 0===n||n}(e,"bigint"),a=function(e){let t;if(r.call(e,"deterministic")&&"boolean"!=typeof(t=e.deterministic)&&"function"!=typeof t)throw TypeError('The "deterministic" argument must be of type boolean or comparator function');return void 0===t||t}(e),f="function"==typeof a?a:void 0,d=c(e,"maximumDepth"),p=c(e,"maximumBreadth");return function(e,r,c){if(arguments.length>1){let m="";if("number"==typeof c?m=" ".repeat(Math.min(c,10)):"string"==typeof c&&(m=c.slice(0,10)),null!=r){if("function"==typeof r)return function e(r,u,c,m,g,y){let b=u[r];switch("object"==typeof b&&null!==b&&"function"==typeof b.toJSON&&(b=b.toJSON(r)),typeof(b=m.call(u,r,b))){case"string":return i(b);case"object":{if(null===b)return"null";if(-1!==c.indexOf(b))return n;let t="",r=",",s=y;if(Array.isArray(b)){if(0===b.length)return"[]";if(d<c.length+1)return'"[Array]"';c.push(b),""!==g&&(y+=g,t+=`
${y}`,r=`,
${y}`);let n=Math.min(b.length,p),i=0;for(;i<n-1;i++){let n=e(String(i),b,c,m,g,y);t+=void 0!==n?n:"null",t+=r}let o=e(String(i),b,c,m,g,y);if(t+=void 0!==o?o:"null",b.length-1>p){let e=b.length-p-1;t+=`${r}"... ${h(e)} not stringified"`}return""!==g&&(t+=`
${s}`),c.pop(),`[${t}]`}let u=Object.keys(b),v=u.length;if(0===v)return"{}";if(d<c.length+1)return'"[Object]"';let _="",w="";""!==g&&(y+=g,r=`,
${y}`,_=" ");let x=Math.min(v,p);a&&!l(b)&&(u=o(u,f)),c.push(b);for(let n=0;n<x;n++){let s=u[n],o=e(s,b,c,m,g,y);void 0!==o&&(t+=`${w}${i(s)}:${_}${o}`,w=r)}return v>p&&(t+=`${w}"...":${_}"${h(v-p)} not stringified"`,w=r),""!==g&&w.length>1&&(t=`
${y}${t}
${s}`),c.pop(),`{${t}}`}case"number":return isFinite(b)?String(b):t?t(b):"null";case"boolean":return!0===b?"true":"false";case"undefined":return;case"bigint":if(s)return String(b);default:return t?t(b):void 0}}("",{"":e},[],r,m,"");if(Array.isArray(r))return function e(r,o,a,l,u,c){switch("object"==typeof o&&null!==o&&"function"==typeof o.toJSON&&(o=o.toJSON(r)),typeof o){case"string":return i(o);case"object":{if(null===o)return"null";if(-1!==a.indexOf(o))return n;let t=c,r="",s=",";if(Array.isArray(o)){if(0===o.length)return"[]";if(d<a.length+1)return'"[Array]"';a.push(o),""!==u&&(c+=u,r+=`
${c}`,s=`,
${c}`);let n=Math.min(o.length,p),i=0;for(;i<n-1;i++){let t=e(String(i),o[i],a,l,u,c);r+=void 0!==t?t:"null",r+=s}let f=e(String(i),o[i],a,l,u,c);if(r+=void 0!==f?f:"null",o.length-1>p){let e=o.length-p-1;r+=`${s}"... ${h(e)} not stringified"`}return""!==u&&(r+=`
${t}`),a.pop(),`[${r}]`}a.push(o);let f="";""!==u&&(c+=u,s=`,
${c}`,f=" ");let m="";for(let t of l){let n=e(t,o[t],a,l,u,c);void 0!==n&&(r+=`${m}${i(t)}:${f}${n}`,m=s)}return""!==u&&m.length>1&&(r=`
${c}${r}
${t}`),a.pop(),`{${r}}`}case"number":return isFinite(o)?String(o):t?t(o):"null";case"boolean":return!0===o?"true":"false";case"undefined":return;case"bigint":if(s)return String(o);default:return t?t(o):void 0}}("",e,[],function(e){let t=new Set;for(let r of e)("string"==typeof r||"number"==typeof r)&&t.add(String(r));return t}(r),m,"")}if(0!==m.length)return function e(r,c,m,g,y){switch(typeof c){case"string":return i(c);case"object":{if(null===c)return"null";if("function"==typeof c.toJSON){if("object"!=typeof(c=c.toJSON(r)))return e(r,c,m,g,y);if(null===c)return"null"}if(-1!==m.indexOf(c))return n;let t=y;if(Array.isArray(c)){if(0===c.length)return"[]";if(d<m.length+1)return'"[Array]"';m.push(c),y+=g;let r=`
${y}`,n=`,
${y}`,s=Math.min(c.length,p),i=0;for(;i<s-1;i++){let t=e(String(i),c[i],m,g,y);r+=void 0!==t?t:"null",r+=n}let o=e(String(i),c[i],m,g,y);if(r+=void 0!==o?o:"null",c.length-1>p){let e=c.length-p-1;r+=`${n}"... ${h(e)} not stringified"`}return r+=`
${t}`,m.pop(),`[${r}]`}let s=Object.keys(c),b=s.length;if(0===b)return"{}";if(d<m.length+1)return'"[Object]"';y+=g;let v=`,
${y}`,_="",w="",x=Math.min(b,p);l(c)&&(_+=u(c,v,p),s=s.slice(c.length),x-=c.length,w=v),a&&(s=o(s,f)),m.push(c);for(let t=0;t<x;t++){let r=s[t],n=e(r,c[r],m,g,y);void 0!==n&&(_+=`${w}${i(r)}: ${n}`,w=v)}return b>p&&(_+=`${w}"...": "${h(b-p)} not stringified"`,w=v),""!==w&&(_=`
${y}${_}
${t}`),m.pop(),`{${_}}`}case"number":return isFinite(c)?String(c):t?t(c):"null";case"boolean":return!0===c?"true":"false";case"undefined":return;case"bigint":if(s)return String(c);default:return t?t(c):void 0}}("",e,[],m,"")}return function e(r,c,m){switch(typeof c){case"string":return i(c);case"object":{if(null===c)return"null";if("function"==typeof c.toJSON){if("object"!=typeof(c=c.toJSON(r)))return e(r,c,m);if(null===c)return"null"}if(-1!==m.indexOf(c))return n;let t="",s=void 0!==c.length;if(s&&Array.isArray(c)){if(0===c.length)return"[]";if(d<m.length+1)return'"[Array]"';m.push(c);let r=Math.min(c.length,p),n=0;for(;n<r-1;n++){let r=e(String(n),c[n],m);t+=void 0!==r?r:"null",t+=","}let s=e(String(n),c[n],m);if(t+=void 0!==s?s:"null",c.length-1>p){let e=c.length-p-1;t+=`,"... ${h(e)} not stringified"`}return m.pop(),`[${t}]`}let g=Object.keys(c),y=g.length;if(0===y)return"{}";if(d<m.length+1)return'"[Object]"';let b="",v=Math.min(y,p);s&&l(c)&&(t+=u(c,",",p),g=g.slice(c.length),v-=c.length,b=","),a&&(g=o(g,f)),m.push(c);for(let r=0;r<v;r++){let n=g[r],s=e(n,c[n],m);void 0!==s&&(t+=`${b}${i(n)}:${s}`,b=",")}return y>p&&(t+=`${b}"...":"${h(y-p)} not stringified"`),m.pop(),`{${t}}`}case"number":return isFinite(c)?String(c):t?t(c):"null";case"boolean":return!0===c?"true":"false";case"undefined":return;case"bigint":if(s)return String(c);default:return t?t(c):void 0}}("",e,[])}}},60733:(e,t,r)=>{"use strict";let n=r(28354).inspect,s=r(20202),{LEVEL:i,MESSAGE:o,SPLAT:a}=r(5405);e.exports=s((e,t={})=>{let r=Object.assign({},e);return delete r[i],delete r[o],delete r[a],e[o]=n(r,!1,t.depth||null,t.colorize),e})},62601:(e,t,r)=>{"use strict";let n=r(28354),{SPLAT:s}=r(5405),i=/%[scdjifoO%]/g,o=/%%/g;class a{constructor(e){this.options=e}_splat(e,t){let r=e.message,i=e[s]||e.splat||[],a=r.match(o),l=a&&a.length||0,u=t.length-l-i.length,c=u<0?i.splice(u,-1*u):[],h=c.length;if(h)for(let t=0;t<h;t++)Object.assign(e,c[t]);return e.message=n.format(r,...i),e}transform(e){let t=e.message,r=e[s]||e.splat;if(!r||!r.length)return e;let n=t&&t.match&&t.match(i);if(!n&&(r||r.length)){let t=r.length>1?r.splice(0):r,n=t.length;if(n)for(let r=0;r<n;r++)Object.assign(e,t[r]);return e}return n?this._splat(e,n):e}}e.exports=e=>new a(e)},63692:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i(r(36188)),s=i(r(45293));function i(e){return e&&e.__esModule?e:{default:e}}t.default=(0,i(r(64356)).default)(function(e,t,r,i){return(0,n.default)(t)(e,(0,s.default)(r),i)},4),e.exports=t.default},64356:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(t||(t=e.length),!t)throw Error("arity is undefined");return function(...r){return"function"==typeof r[t-1]?e.apply(this,r):new Promise((n,s)=>{r[t-1]=(e,...t)=>{if(e)return s(e);n(t.length>1?t:t[0])},e.apply(this,r)})}},e.exports=t.default},66538:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=i(r(43935)),s=i(r(45293));function i(e){return e&&e.__esModule?e:{default:e}}t.default=(0,i(r(64356)).default)((e,t,r)=>{var i=(0,n.default)(t)?[]:{};e(t,(e,t,r)=>{(0,s.default)(e)((e,...n)=>{n.length<2&&([n]=n),i[t]=n,r(e)})},e=>r(e,i))},3),e.exports=t.default},69063:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return e[Symbol.iterator]&&e[Symbol.iterator]()},e.exports=t.default},69409:(e,t,r)=>{"use strict";let n=t.format=r(20202);function s(e,t){Object.defineProperty(n,e,{get:()=>t(),configurable:!0})}t.levels=r(79962),s("align",function(){return r(92086)}),s("errors",function(){return r(56046)}),s("cli",function(){return r(76243)}),s("combine",function(){return r(5546)}),s("colorize",function(){return r(86578)}),s("json",function(){return r(37945)}),s("label",function(){return r(76231)}),s("logstash",function(){return r(77302)}),s("metadata",function(){return r(96964)}),s("ms",function(){return r(97555)}),s("padLevels",function(){return r(42026)}),s("prettyPrint",function(){return r(60733)}),s("printf",function(){return r(73098)}),s("simple",function(){return r(70749)}),s("splat",function(){return r(62601)}),s("timestamp",function(){return r(89881)}),s("uncolorize",function(){return r(84015)})},70205:(e,t)=>{function r(e){for(var t in e)this[t]=e[t]}t.get=function(e){var r=Error.stackTraceLimit;Error.stackTraceLimit=1/0;var n={},s=Error.prepareStackTrace;Error.prepareStackTrace=function(e,t){return t},Error.captureStackTrace(n,e||t.get);var i=n.stack;return Error.prepareStackTrace=s,Error.stackTraceLimit=r,i},t.parse=function(e){if(!e.stack)return[];var t=this;return e.stack.split("\n").slice(1).map(function(e){if(e.match(/^\s*[-]{4,}$/))return t._createParsedCallSite({fileName:e,lineNumber:null,functionName:null,typeName:null,methodName:null,columnNumber:null,native:null});var r=e.match(/at (?:(.+)\s+\()?(?:(.+?):(\d+)(?::(\d+))?|([^)]+))\)?/);if(r){var n=null,s=null,i=null,o=null,a=null,l="native"===r[5];if(r[1]){var u=(i=r[1]).lastIndexOf(".");if("."==i[u-1]&&u--,u>0){n=i.substr(0,u),s=i.substr(u+1);var c=n.indexOf(".Module");c>0&&(i=i.substr(c+1),n=n.substr(0,c))}o=null}s&&(o=n,a=s),"<anonymous>"===s&&(a=null,i=null);var h={fileName:r[2]||null,lineNumber:parseInt(r[3],10)||null,functionName:i,typeName:o,methodName:a,columnNumber:parseInt(r[4],10)||null,native:l};return t._createParsedCallSite(h)}}).filter(function(e){return!!e})},["this","typeName","functionName","methodName","fileName","lineNumber","columnNumber","function","evalOrigin"].forEach(function(e){r.prototype[e]=null,r.prototype["get"+e[0].toUpperCase()+e.substr(1)]=function(){return this[e]}}),["topLevel","eval","native","constructor"].forEach(function(e){r.prototype[e]=!1,r.prototype["is"+e[0].toUpperCase()+e.substr(1)]=function(){return this[e]}}),t._createParsedCallSite=function(e){return new r(e)}},70456:(e,t,r)=>{e.exports=r(16513)},70749:(e,t,r)=>{"use strict";let n=r(20202),{MESSAGE:s}=r(5405),i=r(60512);e.exports=n(e=>{let t=i(Object.assign({},e,{level:void 0,message:void 0,splat:void 0})),r=e.padding&&e.padding[e.level]||"";return"{}"!==t?e[s]=`${e.level}:${r} ${e.message} ${t}`:e[s]=`${e.level}:${r} ${e.message}`,e})},70927:e=>{"use strict";e.exports=function(e,t){var r=(t=t||process.argv||[]).indexOf("--"),n=/^-{1,2}/.test(e)?"":"--",s=t.indexOf(n+e);return -1!==s&&(-1===r||s<r)}},72444:e=>{e.exports=function(e){var t=["red","yellow","green","blue","magenta"];return function(r,n,s){return" "===r?r:e[t[n++%t.length]](r)}}},73098:(e,t,r)=>{"use strict";let{MESSAGE:n}=r(5405);class s{constructor(e){this.template=e}transform(e){return e[n]=this.template(e),e}}e.exports=e=>new s(e),e.exports.Printf=e.exports.Format=s},74002:(e,t,r)=>{var n={};e.exports=n,n.themes={};var s=r(28354),i=n.styles=r(81568),o=Object.defineProperties,a=new RegExp(/[\r\n]+/g);n.supportsColor=r(76269).supportsColor,void 0===n.enabled&&(n.enabled=!1!==n.supportsColor()),n.enable=function(){n.enabled=!0},n.disable=function(){n.enabled=!1},n.stripColors=n.strip=function(e){return(""+e).replace(/\x1B\[\d+m/g,"")},n.stylize=function(e,t){if(!n.enabled)return e+"";var r=i[t];return!r&&t in n?n[t](e):r.open+e+r.close};var l=/[|\\{}()[\]^$+*?.]/g,u=function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(l,"\\$&")};function c(e){var t=function e(){return d.apply(e,arguments)};return t._styles=e,t.__proto__=f,t}var h=function(){var e={};return i.grey=i.gray,Object.keys(i).forEach(function(t){i[t].closeRe=RegExp(u(i[t].close),"g"),e[t]={get:function(){return c(this._styles.concat(t))}}}),e}(),f=o(function(){},h);function d(){var e=Array.prototype.slice.call(arguments),t=e.map(function(e){return null!=e&&e.constructor===String?e:s.inspect(e)}).join(" ");if(!n.enabled||!t)return t;for(var r=-1!=t.indexOf("\n"),o=this._styles,l=o.length;l--;){var u=i[o[l]];t=u.open+t.replace(u.closeRe,u.open)+u.close,r&&(t=t.replace(a,function(e){return u.close+e+u.open}))}return t}n.setTheme=function(e){if("string"==typeof e){console.log("colors.setTheme now only accepts an object, not a string.  If you are trying to set a theme from a file, it is now your (the caller's) responsibility to require the file.  The old syntax looked like colors.setTheme(__dirname + '/../themes/generic-logging.js'); The new syntax looks like colors.setTheme(require(__dirname + '/../themes/generic-logging.js'));");return}for(var t in e)!function(t){n[t]=function(r){if("object"==typeof e[t]){var s=r;for(var i in e[t])s=n[e[t][i]](s);return s}return n[e[t]](r)}}(t)};var p=function(e,t){var r=t.split("");return(r=r.map(e)).join("")};for(var m in n.trap=r(83291),n.zalgo=r(89697),n.maps={},n.maps.america=r(26414)(n),n.maps.zebra=r(57638)(n),n.maps.rainbow=r(72444)(n),n.maps.random=r(45203)(n),n.maps)!function(e){n[e]=function(t){return p(n.maps[e],t)}}(m);o(n,function(){var e={};return Object.keys(h).forEach(function(t){e[t]={get:function(){return c([t])}}}),e}())},76231:(e,t,r)=>{"use strict";e.exports=r(20202)((e,t)=>(t.message?e.message=`[${t.label}] ${e.message}`:e.label=t.label,e))},76243:(e,t,r)=>{"use strict";let{Colorizer:n}=r(86578),{Padder:s}=r(42026),{configs:i,MESSAGE:o}=r(5405);class a{constructor(e={}){e.levels||(e.levels=i.cli.levels),this.colorizer=new n(e),this.padder=new s(e),this.options=e}transform(e,t){return this.colorizer.transform(this.padder.transform(e,t),t),e[o]=`${e.level}:${e.message}`,e}}e.exports=e=>new a(e),e.exports.Format=a},76269:(e,t,r)=>{"use strict";var n=r(21820),s=r(70927),i=process.env,o=void 0;function a(e){var t;return 0!==(t=function(e){if(!1===o)return 0;if(s("color=16m")||s("color=full")||s("color=truecolor"))return 3;if(s("color=256"))return 2;if(e&&!e.isTTY&&!0!==o)return 0;var t=+!!o;if("win32"===process.platform){var r=n.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(r[0])>=10&&Number(r[2])>=10586?Number(r[2])>=14931?3:2:1}if("CI"in i)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(function(e){return e in i})||"codeship"===i.CI_NAME?1:t;if("TEAMCITY_VERSION"in i)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(i.TEAMCITY_VERSION);if("TERM_PROGRAM"in i){var a=parseInt((i.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(i.TERM_PROGRAM){case"iTerm.app":return a>=3?3:2;case"Hyper":return 3;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(i.TERM)?2:/^screen|^xterm|^vt100|^rxvt|color|ansi|cygwin|linux/i.test(i.TERM)||"COLORTERM"in i?1:(i.TERM,t)}(e))&&{level:t,hasBasic:!0,has256:t>=2,has16m:t>=3}}s("no-color")||s("no-colors")||s("color=false")?o=!1:(s("color")||s("colors")||s("color=true")||s("color=always"))&&(o=!0),"FORCE_COLOR"in i&&(o=0===i.FORCE_COLOR.length||0!==parseInt(i.FORCE_COLOR,10)),e.exports={supportsColor:a,stdout:a(process.stdout),stderr:a(process.stderr)}},77302:(e,t,r)=>{"use strict";let n=r(20202),{MESSAGE:s}=r(5405),i=r(60512);e.exports=n(e=>{let t={};return e.message&&(t["@message"]=e.message,delete e.message),e.timestamp&&(t["@timestamp"]=e.timestamp,delete e.timestamp),t["@fields"]=e,e[s]=i(t),e})},77843:(e,t,r)=>{"use strict";Object.defineProperty(t,"Console",{configurable:!0,enumerable:!0,get:()=>r(31556)}),Object.defineProperty(t,"File",{configurable:!0,enumerable:!0,get:()=>r(47439)}),Object.defineProperty(t,"Http",{configurable:!0,enumerable:!0,get:()=>r(35993)}),Object.defineProperty(t,"Stream",{configurable:!0,enumerable:!0,get:()=>r(13897)})},79672:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){function t(...r){if(null!==e){var n=e;e=null,n.apply(this,r)}}return Object.assign(t,e),t},e.exports=t.default},79962:(e,t,r)=>{"use strict";let{Colorizer:n}=r(86578);e.exports=e=>(n.addColors(e.colors||e),e)},81568:e=>{var t={};e.exports=t;var r={reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29],black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],gray:[90,39],grey:[90,39],brightRed:[91,39],brightGreen:[92,39],brightYellow:[93,39],brightBlue:[94,39],brightMagenta:[95,39],brightCyan:[96,39],brightWhite:[97,39],bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgGray:[100,49],bgGrey:[100,49],bgBrightRed:[101,49],bgBrightGreen:[102,49],bgBrightYellow:[103,49],bgBrightBlue:[104,49],bgBrightMagenta:[105,49],bgBrightCyan:[106,49],bgBrightWhite:[107,49],blackBG:[40,49],redBG:[41,49],greenBG:[42,49],yellowBG:[43,49],blueBG:[44,49],magentaBG:[45,49],cyanBG:[46,49],whiteBG:[47,49]};Object.keys(r).forEach(function(e){var n=r[e],s=t[e]=[];s.open="\x1b["+n[0]+"m",s.close="\x1b["+n[1]+"m"})},82008:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=s(r(63692));function s(e){return e&&e.__esModule?e:{default:e}}t.default=(0,s(r(64356)).default)(function(e,t,r){return(0,n.default)(e,1,t,r)},3),e.exports=t.default},83291:e=>{e.exports=function(e,t){var r="";e=(e=e||"Run the trap, drop the bass").split("");var n={a:["@","Ą","Ⱥ","Ʌ","Δ","Λ","Д"],b:["\xdf","Ɓ","Ƀ","ɮ","β","฿"],c:["\xa9","Ȼ","Ͼ"],d:["\xd0","Ɗ","Ԁ","ԁ","Ԃ","ԃ"],e:["\xcb","ĕ","Ǝ","ɘ","Σ","ξ","Ҽ","੬"],f:["Ӻ"],g:["ɢ"],h:["Ħ","ƕ","Ң","Һ","Ӈ","Ԋ"],i:["༏"],j:["Ĵ"],k:["ĸ","Ҡ","Ӄ","Ԟ"],l:["Ĺ"],m:["ʍ","Ӎ","ӎ","Ԡ","ԡ","൩"],n:["\xd1","ŋ","Ɲ","Ͷ","Π","Ҋ"],o:["\xd8","\xf5","\xf8","Ǿ","ʘ","Ѻ","ם","۝","๏"],p:["Ƿ","Ҏ"],q:["্"],r:["\xae","Ʀ","Ȑ","Ɍ","ʀ","Я"],s:["\xa7","Ϟ","ϟ","Ϩ"],t:["Ł","Ŧ","ͳ"],u:["Ʊ","Ս"],v:["ט"],w:["Ш","Ѡ","Ѽ","൰"],x:["Ҳ","Ӿ","Ӽ","ӽ"],y:["\xa5","Ұ","Ӌ"],z:["Ƶ","ɀ"]};return e.forEach(function(e){var t=Math.floor(Math.random()*(n[e=e.toLowerCase()]||[" "]).length);void 0!==n[e]?r+=n[e][t]:r+=e}),r}},84015:(e,t,r)=>{"use strict";let n=r(34717),s=r(20202),{MESSAGE:i}=r(5405);e.exports=s((e,t)=>(!1!==t.level&&(e.level=n.strip(e.level)),!1!==t.message&&(e.message=n.strip(String(e.message))),!1!==t.raw&&e[i]&&(e[i]=n.strip(String(e[i]))),e))},86578:(e,t,r)=>{"use strict";let n=r(34717),{LEVEL:s,MESSAGE:i}=r(5405);n.enabled=!0;let o=/\s+/;class a{constructor(e={}){e.colors&&this.addColors(e.colors),this.options=e}static addColors(e){let t=Object.keys(e).reduce((t,r)=>(t[r]=o.test(e[r])?e[r].split(o):e[r],t),{});return a.allColors=Object.assign({},a.allColors||{},t),a.allColors}addColors(e){return a.addColors(e)}colorize(e,t,r){if(void 0===r&&(r=t),!Array.isArray(a.allColors[e]))return n[a.allColors[e]](r);for(let t=0,s=a.allColors[e].length;t<s;t++)r=n[a.allColors[e][t]](r);return r}transform(e,t){return t.all&&"string"==typeof e[i]&&(e[i]=this.colorize(e[s],e.level,e[i])),(t.level||t.all||!t.message)&&(e.level=this.colorize(e[s],e.level)),(t.all||t.message)&&(e.message=this.colorize(e[s],e.level,e.message)),e}}e.exports=e=>new a(e),e.exports.Colorizer=e.exports.Format=a},88514:(e,t,r)=>{"use strict";let n=r(21820),s=r(27411),i=r(70456)("winston:rejection"),o=r(50571),a=r(70205),l=r(29698);e.exports=class{constructor(e){if(!e)throw Error("Logger is required to handle rejections");this.logger=e,this.handlers=new Map}handle(...e){e.forEach(e=>{if(Array.isArray(e))return e.forEach(e=>this._addHandler(e));this._addHandler(e)}),this.catcher||(this.catcher=this._unhandledRejection.bind(this),process.on("unhandledRejection",this.catcher))}unhandle(){this.catcher&&(process.removeListener("unhandledRejection",this.catcher),this.catcher=!1,Array.from(this.handlers.values()).forEach(e=>this.logger.unpipe(e)))}getAllInfo(e){let t=null;return e&&(t="string"==typeof e?e:e.message),{error:e,level:"error",message:[`unhandledRejection: ${t||"(no error message)"}`,e&&e.stack||"  No stack trace"].join("\n"),stack:e&&e.stack,rejection:!0,date:new Date().toString(),process:this.getProcessInfo(),os:this.getOsInfo(),trace:this.getTrace(e)}}getProcessInfo(){return{pid:process.pid,uid:process.getuid?process.getuid():null,gid:process.getgid?process.getgid():null,cwd:process.cwd(),execPath:process.execPath,version:process.version,argv:process.argv,memoryUsage:process.memoryUsage()}}getOsInfo(){return{loadavg:n.loadavg(),uptime:n.uptime()}}getTrace(e){return(e?a.parse(e):a.get()).map(e=>({column:e.getColumnNumber(),file:e.getFileName(),function:e.getFunctionName(),line:e.getLineNumber(),method:e.getMethodName(),native:e.isNative()}))}_addHandler(e){if(!this.handlers.has(e)){e.handleRejections=!0;let t=new l(e);this.handlers.set(e,t),this.logger.pipe(t)}}_unhandledRejection(e){let t;let r=this.getAllInfo(e),n=this._getRejectionHandlers(),a="function"==typeof this.logger.exitOnError?this.logger.exitOnError(e):this.logger.exitOnError;function l(){i("doExit",a),i("process._exiting",process._exiting),a&&!process._exiting&&(t&&clearTimeout(t),process.exit(1))}if(!n.length&&a&&(console.warn("winston: exitOnError cannot be true with no rejection handlers."),console.warn("winston: not exiting process."),a=!1),!n||0===n.length)return process.nextTick(l);s(n,(e,t)=>{let r=o(t),n=e.transport||e;function s(e){return()=>{i(e),r()}}n._ending=!0,n.once("finish",s("finished")),n.once("error",s("error"))},()=>a&&l()),this.logger.log(r),a&&(t=setTimeout(l,3e3))}_getRejectionHandlers(){return this.logger.transports.filter(e=>(e.transport||e).handleRejections)}}},88723:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return(t,r,n)=>e(t,n)},e.exports=t.default},89697:e=>{e.exports=function(e,t){e=e||"   he is here   ";var r={up:["̍","̎","̄","̅","̿","̑","̆","̐","͒","͗","͑","̇","̈","̊","͂","̓","̈","͊","͋","͌","̃","̂","̌","͐","̀","́","̋","̏","̒","̓","̔","̽","̉","ͣ","ͤ","ͥ","ͦ","ͧ","ͨ","ͩ","ͪ","ͫ","ͬ","ͭ","ͮ","ͯ","̾","͛","͆","̚"],down:["̖","̗","̘","̙","̜","̝","̞","̟","̠","̤","̥","̦","̩","̪","̫","̬","̭","̮","̯","̰","̱","̲","̳","̹","̺","̻","̼","ͅ","͇","͈","͉","͍","͎","͓","͔","͕","͖","͙","͚","̣"],mid:["̕","̛","̀","́","͘","̡","̢","̧","̨","̴","̵","̶","͜","͝","͞","͟","͠","͢","̸","̷","͡"," ҉"]},n=[].concat(r.up,r.down,r.mid);function s(e){return Math.floor(Math.random()*e)}return function(e,t){var i,o,a="";for(o in(t=t||{}).up=void 0===t.up||t.up,t.mid=void 0===t.mid||t.mid,t.down=void 0===t.down||t.down,t.size=void 0!==t.size?t.size:"maxi",e=e.split(""))if(!function(e){var t=!1;return n.filter(function(r){t=r===e}),t}(o)){switch(a+=e[o],i={up:0,down:0,mid:0},t.size){case"mini":i.up=s(8),i.mid=s(2),i.down=s(8);break;case"maxi":i.up=s(16)+3,i.mid=s(4)+1,i.down=s(64)+3;break;default:i.up=s(8)+1,i.mid=s(6)/2,i.down=s(8)+1}var l=["up","mid","down"];for(var u in l)for(var c=l[u],h=0;h<=i[c];h++)t[c]&&(a+=r[c][s(r[c].length)])}return a}(e,t)}},89881:(e,t,r)=>{"use strict";let n=r(33985);e.exports=r(20202)((e,t={})=>(t.format&&(e.timestamp="function"==typeof t.format?t.format():n.format(new Date,t.format)),e.timestamp||(e.timestamp=new Date().toISOString()),t.alias&&(e[t.alias]=e.timestamp),e))},90289:e=>{"use strict";e.exports={version:"3.17.0"}},92086:(e,t,r)=>{"use strict";e.exports=r(20202)(e=>(e.message=`	${e.message}`,e))},96964:(e,t,r)=>{"use strict";e.exports=r(20202)((e,t={})=>{let r="metadata";t.key&&(r=t.key);let n=[];return(t.fillExcept||t.fillWith||(n.push("level"),n.push("message")),t.fillExcept&&(n=t.fillExcept),n.length>0)?function(e,t,r){let n=t.reduce((t,r)=>(t[r]=e[r],delete e[r],t),{}),s=Object.keys(e).reduce((t,r)=>(t[r]=e[r],delete e[r],t),{});return Object.assign(e,n,{[r]:s}),e}(e,n,r):t.fillWith?function(e,t,r){return e[r]=t.reduce((t,r)=>(t[r]=e[r],delete e[r],t),{}),e}(e,t.fillWith,r):e})},97555:function(e,t,r){"use strict";let n=r(20202),s=r(25041);e.exports=n(e=>{let t=+new Date;return this.diff=t-(this.prevTime||t),this.prevTime=t,e.ms=`+${s(this.diff)}`,e})}};