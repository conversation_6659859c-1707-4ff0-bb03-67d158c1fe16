"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // For StablecoinSwap mode, we need to calculate the price differently\n        if (config.tradingMode === \"StablecoinSwap\") {\n            // Get both crypto prices in terms of the preferred stablecoin\n            const stablecoin = config.preferredStablecoin || 'USDT';\n            try {\n                // Fetch real-time prices from CoinGecko API\n                const crypto1Id = getCoinGeckoId(config.crypto1);\n                const crypto2Id = getCoinGeckoId(config.crypto2);\n                const stablecoinId = getCoinGeckoId(stablecoin);\n                if (crypto1Id && crypto2Id && stablecoinId) {\n                    const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \",\").concat(crypto2Id, \"&vs_currencies=\").concat(stablecoinId));\n                    if (response.ok) {\n                        var _data_crypto1Id, _data_crypto2Id;\n                        const data = await response.json();\n                        const crypto1Price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[stablecoinId];\n                        const crypto2Price = (_data_crypto2Id = data[crypto2Id]) === null || _data_crypto2Id === void 0 ? void 0 : _data_crypto2Id[stablecoinId];\n                        if (crypto1Price > 0 && crypto2Price > 0) {\n                            const ratio = crypto1Price / crypto2Price;\n                            console.log(\"\\uD83D\\uDCCA StablecoinSwap price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(ratio, \" (\").concat(config.crypto1, \": \").concat(crypto1Price, \" \").concat(stablecoin, \", \").concat(config.crypto2, \": \").concat(crypto2Price, \" \").concat(stablecoin, \")\"));\n                            return ratio;\n                        }\n                    }\n                }\n                // Fallback to the existing stablecoin exchange rate method\n                const crypto1Price = await getStablecoinExchangeRate(config.crypto1, stablecoin);\n                const crypto2Price = await getStablecoinExchangeRate(config.crypto2, stablecoin);\n                // Calculate crypto1/crypto2 ratio\n                const ratio = crypto1Price / crypto2Price;\n                console.log(\"\\uD83D\\uDCCA StablecoinSwap price (fallback): \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(ratio, \" (via \").concat(stablecoin, \")\"));\n                return ratio;\n            } catch (error) {\n                console.error('Error fetching StablecoinSwap prices:', error);\n                // Final fallback to mock prices\n                const crypto1Price = getUSDPrice(config.crypto1);\n                const crypto2Price = getUSDPrice(config.crypto2);\n                return crypto1Price / crypto2Price;\n            }\n        }\n        // For SimpleSpot mode, use direct pair pricing\n        // Try multiple API endpoints for better coverage\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // First try Binance API\n        try {\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol));\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0) {\n                    console.log(\"✅ Price fetched from Binance: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                    return price;\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed, trying alternative...', binanceError);\n        }\n        // Fallback to CoinGecko API for broader pair support\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id));\n                if (response.ok) {\n                    var _data_crypto1Id1;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id1 = data[crypto1Id]) === null || _data_crypto1Id1 === void 0 ? void 0 : _data_crypto1Id1[crypto2Id];\n                    if (price > 0) {\n                        console.log(\"✅ Price fetched from CoinGecko: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed, using mock price...', geckoError);\n        }\n        // Final fallback to mock price\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using mock price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Helper function to get USD price (extracted from calculateFallbackMarketPrice)\nconst getUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies\n        'BTC': 109000,\n        'ETH': 4000,\n        'SOL': 240,\n        'ADA': 1.2,\n        'DOGE': 0.4,\n        'LINK': 25,\n        'MATIC': 0.5,\n        'DOT': 8,\n        'AVAX': 45,\n        'SHIB': 0.000030,\n        'XRP': 2.5,\n        'LTC': 110,\n        'BCH': 500,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: how many units of crypto2 = 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation\n    const fluctuation = (Math.random() - 0.5) * 0.02; // ±1%\n    const finalPrice = basePrice * (1 + fluctuation);\n    console.log(\"\\uD83D\\uDCCA Fallback price calculation: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice, \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice, \") = \").concat(finalPrice.toFixed(6)));\n    return finalPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\n// Load global balances for initial state\nconst globalBalances = loadGlobalBalanceFromStorage();\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: globalBalances.crypto1Balance,\n    crypto2Balance: globalBalances.crypto2Balance,\n    stablecoinBalance: globalBalances.stablecoinBalance,\n    backendStatus: 'unknown',\n    totalProfitLoss: globalBalances.totalProfitLoss,\n    sessionAlarmConfig: {\n        buyAlarmEnabled: true,\n        sellAlarmEnabled: true,\n        buyAlarmSound: 'default.mp3',\n        sellAlarmSound: 'default.mp3',\n        volume: 50\n    },\n    telegramConfig: {\n        enabled: false,\n        botToken: '',\n        chatId: ''\n    },\n    isTrading: false\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst SESSION_STORAGE_KEY = 'tradingSession';\nconst BALANCE_STORAGE_KEY = 'tradingBalances';\n// Session persistence functions\nconst saveSessionToStorage = (state)=>{\n    try {\n        const sessionData = {\n            config: state.config,\n            targetPriceRows: state.targetPriceRows,\n            orderHistory: state.orderHistory,\n            currentMarketPrice: state.currentMarketPrice,\n            crypto1Balance: state.crypto1Balance,\n            crypto2Balance: state.crypto2Balance,\n            stablecoinBalance: state.stablecoinBalance,\n            totalProfitLoss: state.totalProfitLoss,\n            sessionAlarmConfig: state.sessionAlarmConfig,\n            telegramConfig: state.telegramConfig,\n            isTrading: state.isTrading,\n            botSystemStatus: state.botSystemStatus,\n            lastUpdated: new Date().toISOString()\n        };\n        localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(sessionData));\n    } catch (error) {\n        console.error('Failed to save session to storage:', error);\n    }\n};\n// Global balance persistence functions - tracks cumulative spending across all sessions\nconst GLOBAL_BALANCE_STORAGE_KEY = 'plutoTradingBot_globalBalances';\nconst saveGlobalBalanceToStorage = (state)=>{\n    try {\n        const balanceData = {\n            crypto1Balance: state.crypto1Balance,\n            crypto2Balance: state.crypto2Balance,\n            stablecoinBalance: state.stablecoinBalance,\n            totalProfitLoss: state.totalProfitLoss,\n            lastUpdated: new Date().toISOString()\n        };\n        localStorage.setItem(GLOBAL_BALANCE_STORAGE_KEY, JSON.stringify(balanceData));\n        console.log('💾 Global balances saved:', balanceData);\n    } catch (error) {\n        console.error('Failed to save global balance to storage:', error);\n    }\n};\nconst loadGlobalBalanceFromStorage = ()=>{\n    try {\n        const savedBalance = localStorage.getItem(GLOBAL_BALANCE_STORAGE_KEY);\n        if (savedBalance) {\n            const balanceData = JSON.parse(savedBalance);\n            console.log('📥 Global balances loaded:', balanceData);\n            return {\n                crypto1Balance: balanceData.crypto1Balance || 10,\n                crypto2Balance: balanceData.crypto2Balance || 100000,\n                stablecoinBalance: balanceData.stablecoinBalance || 0,\n                totalProfitLoss: balanceData.totalProfitLoss || 0\n            };\n        }\n    } catch (error) {\n        console.error('Failed to load global balance from storage:', error);\n    }\n    // Return default balances if no saved data or error\n    return {\n        crypto1Balance: 10,\n        crypto2Balance: 100000,\n        stablecoinBalance: 0,\n        totalProfitLoss: 0\n    };\n};\n// Legacy balance persistence for session-specific data\nconst saveBalanceToStorage = (state)=>{\n    try {\n        const balanceData = {\n            crypto1Balance: state.crypto1Balance,\n            crypto2Balance: state.crypto2Balance,\n            stablecoinBalance: state.stablecoinBalance,\n            totalProfitLoss: state.totalProfitLoss,\n            lastUpdated: new Date().toISOString()\n        };\n        localStorage.setItem(BALANCE_STORAGE_KEY, JSON.stringify(balanceData));\n    } catch (error) {\n        console.error('Failed to save balance to storage:', error);\n    }\n};\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    // Enforce manual bot control - never auto-start on state restoration\n                    return {\n                        ...parsed,\n                        isTrading: false,\n                        botSystemStatus: 'Stopped'\n                    };\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            const updatedState = {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n            // Save to global storage whenever balances change\n            saveGlobalBalanceToStorage(updatedState);\n            return updatedState;\n        case 'UPDATE_STABLECOIN_BALANCE':\n            const updatedStablecoinState = {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n            // Save to global storage whenever stablecoin balance changes\n            saveGlobalBalanceToStorage(updatedStablecoinState);\n            return updatedStablecoinState;\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            // Preserve global balances when resetting session\n            const currentGlobalBalances = loadGlobalBalanceFromStorage();\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset),\n                // Preserve current global balances instead of resetting to defaults\n                crypto1Balance: currentGlobalBalances.crypto1Balance,\n                crypto2Balance: currentGlobalBalances.crypto2Balance,\n                stablecoinBalance: currentGlobalBalances.stablecoinBalance,\n                totalProfitLoss: currentGlobalBalances.totalProfitLoss\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            // Preserve global balances when switching crypto pairs\n            const newCryptoGlobalBalances = loadGlobalBalanceFromStorage();\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0,\n                // Preserve current global balances instead of resetting to defaults\n                crypto1Balance: newCryptoGlobalBalances.crypto1Balance,\n                crypto2Balance: newCryptoGlobalBalances.crypto2Balance,\n                stablecoinBalance: newCryptoGlobalBalances.stablecoinBalance,\n                totalProfitLoss: newCryptoGlobalBalances.totalProfitLoss\n            };\n        case 'RESTORE_SESSION':\n            return {\n                ...state,\n                ...action.payload,\n                isTrading: false,\n                botSystemStatus: 'Stopped' // Prevent auto-start on session restoration\n            };\n        case 'SET_TELEGRAM_CONFIG':\n            return {\n                ...state,\n                telegramConfig: {\n                    ...state.telegramConfig,\n                    ...action.payload\n                }\n            };\n        case 'SET_SESSION_ALARM_CONFIG':\n            return {\n                ...state,\n                sessionAlarmConfig: {\n                    ...state.sessionAlarmConfig,\n                    ...action.payload\n                }\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Telegram notification function\nconst sendTelegramNotification = async (message, type, telegramConfig)=>{\n    try {\n        if (!telegramConfig.enabled || !telegramConfig.botToken || !telegramConfig.chatId) {\n            return;\n        }\n        const emoji = type === 'error' ? '🚨' : type === 'success' ? '✅' : 'ℹ️';\n        const formattedMessage = \"\".concat(emoji, \" \").concat(message);\n        await fetch(\"https://api.telegram.org/bot\".concat(telegramConfig.botToken, \"/sendMessage\"), {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                chat_id: telegramConfig.chatId,\n                text: formattedMessage,\n                parse_mode: 'HTML'\n            })\n        });\n    } catch (error) {\n        console.error('Failed to send Telegram notification:', error);\n    }\n};\n// Session-specific alarm function\nconst playSessionAlarm = (type, alarmConfig)=>{\n    try {\n        const soundFile = type === 'buy' ? alarmConfig.buyAlarmSound : alarmConfig.sellAlarmSound;\n        const enabled = type === 'buy' ? alarmConfig.buyAlarmEnabled : alarmConfig.sellAlarmEnabled;\n        if (soundFile && enabled) {\n            const audio = new Audio(\"/ringtones/\".concat(soundFile));\n            audio.volume = alarmConfig.volume / 100;\n            audio.play().catch(console.error);\n        }\n    } catch (error) {\n        console.error('Failed to play session alarm:', error);\n    }\n};\n// Enhanced price fetching for StablecoinSwap mode\nconst fetchStablecoinSwapPrice = async (crypto1, crypto2)=>{\n    try {\n        var _data_crypto1_toLowerCase;\n        const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1.toLowerCase(), \"&vs_currencies=\").concat(crypto2.toLowerCase()));\n        const data = await response.json();\n        const price = (_data_crypto1_toLowerCase = data[crypto1.toLowerCase()]) === null || _data_crypto1_toLowerCase === void 0 ? void 0 : _data_crypto1_toLowerCase[crypto2.toLowerCase()];\n        if (price) {\n            return price;\n        }\n        throw new Error('Price not found');\n    } catch (error) {\n        console.error('Error fetching stablecoin swap price:', error);\n        return null;\n    }\n};\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Clear the URL parameter to avoid confusion\n                const newUrl = window.location.pathname;\n                window.history.replaceState({}, '', newUrl);\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load saved state\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                const price = await getMarketPriceFromAPI(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch\n    ]);\n    // Market price fluctuation effect - simulates real-time price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up price fluctuation interval for simulation\n            const priceFluctuationInterval = setInterval({\n                \"TradingProvider.useEffect.priceFluctuationInterval\": ()=>{\n                    // Only fluctuate price if online\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        dispatch({\n                            type: 'FLUCTUATE_MARKET_PRICE'\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceFluctuationInterval\"], 2000); // Update every 2 seconds for realistic simulation\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceFluctuationInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch\n    ]);\n    // Other effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            if (state.appSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && state.appSettings.alertOnOrderExecution) {\n                    soundPath = state.appSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && state.appSettings.alertOnError) {\n                    soundPath = state.appSettings.soundError;\n                }\n                if (soundPath) {\n                    audioRef.current.src = soundPath;\n                    audioRef.current.currentTime = 0; // Reset to beginning\n                    // Play the sound and limit duration to 2 seconds\n                    audioRef.current.play().then({\n                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                            // Set a timeout to pause the audio after 2 seconds\n                            setTimeout({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0; // Reset for next play\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                        }\n                    }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                        \"TradingProvider.useCallback[playSound]\": (err)=>console.error(\"Error playing sound:\", err)\n                    }[\"TradingProvider.useCallback[playSound]\"]);\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Telegram notification function\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async (message)=>{\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return;\n                }\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    })\n                });\n                if (!response.ok) {\n                    console.error('Failed to send Telegram notification:', response.statusText);\n                }\n            } catch (error) {\n                console.error('Error sending Telegram notification:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || '',\n                                        tradingMode: 'SimpleSpot',\n                                        type: 'BUY',\n                                        costBasisCrypto2: costCrypto2\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                    tradingMode: 'SimpleSpot',\n                                    type: 'SELL',\n                                    costBasisCrypto2: inferiorRow.originalCostCrypto2,\n                                    percentageGain: inferiorRow.originalCostCrypto2 > 0 ? (crypto2Received - inferiorRow.originalCostCrypto2) / inferiorRow.originalCostCrypto2 * 100 : 0\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Get real market price for Crypto2/Stablecoin pair (synchronous for now)\n                                const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Get real market price for Crypto1/Stablecoin pair\n                                const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        tradingMode: 'StablecoinSwap',\n                                        type: 'BUY',\n                                        intermediateStablecoinAmount: stablecoinObtained,\n                                        stablecoinPrice: crypto2StablecoinPrice\n                                    }\n                                });\n                                // Calculate potential profit information for BUY entry (for future reference)\n                                const currentMarketValueInCrypto2 = crypto1Bought * (currentMarketPrice || crypto1StablecoinPrice);\n                                const potentialProfitInCrypto2 = currentMarketValueInCrypto2 - amountCrypto2ToUse;\n                                const potentialProfitCrypto1 = crypto1StablecoinPrice > 0 ? potentialProfitInCrypto2 * config.incomeSplitCrypto1Percent / 100 / crypto1StablecoinPrice : 0;\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || '',\n                                        tradingMode: 'StablecoinSwap',\n                                        type: 'BUY',\n                                        costBasisCrypto2: amountCrypto2ToUse,\n                                        intermediateStablecoinAmount: stablecoinObtained,\n                                        stablecoinPrice: crypto1StablecoinPrice,\n                                        // Store potential profit information for reference\n                                        potentialProfitLossCrypto2: potentialProfitInCrypto2,\n                                        potentialProfitLossCrypto1: potentialProfitCrypto1,\n                                        currentMarketValueCrypto2: currentMarketValueInCrypto2\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Enhanced StablecoinSwap P/L calculation with proper two-step process\n                            const costBasisInCrypto2 = inferiorRow.originalCostCrypto2 || 0;\n                            // Two-step SELL formula:\n                            // Step A: Crypto1 → Stablecoin (already calculated as stablecoinFromC1Sell)\n                            // Step B: Stablecoin → Crypto2 (already calculated as crypto2Reacquired)\n                            // Profit = Final Crypto2 Amount - Original Crypto2 Cost\n                            const realizedProfitInCrypto2 = crypto2Reacquired - costBasisInCrypto2;\n                            // Calculate percentage gain/loss based on original cost basis\n                            const percentageGain = costBasisInCrypto2 > 0 ? (crypto2Reacquired - costBasisInCrypto2) / costBasisInCrypto2 * 100 : 0;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            // This represents the portion of profit that would be allocated to Crypto1\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 * config.incomeSplitCrypto1Percent / 100 / crypto1StablecoinPrice : 0;\n                            // Track intermediate values for better P/L analysis\n                            const intermediateStablecoinAmount = stablecoinFromC1Sell;\n                            const effectiveStablecoinPrice = crypto2StablecoinPrice;\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    tradingMode: 'StablecoinSwap',\n                                    type: 'SELL',\n                                    intermediateStablecoinAmount: intermediateStablecoinAmount,\n                                    stablecoinPrice: crypto1StablecoinPrice\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1,\n                                    tradingMode: 'StablecoinSwap',\n                                    type: 'SELL',\n                                    costBasisCrypto2: costBasisInCrypto2,\n                                    percentageGain: percentageGain,\n                                    intermediateStablecoinAmount: intermediateStablecoinAmount,\n                                    stablecoinPrice: effectiveStablecoinPrice,\n                                    // Two-step process tracking\n                                    stepAAmount: amountCrypto1ToSell,\n                                    stepAPrice: crypto1StablecoinPrice,\n                                    stepBAmount: crypto2Reacquired,\n                                    stepBPrice: crypto2StablecoinPrice,\n                                    twoStepProcess: true // Flag to indicate this is a two-step transaction\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        // For StablecoinSwap mode, income should follow the sign of percentFromActualPrice\n                        if (state.config.tradingMode === \"StablecoinSwap\") {\n                            // If percentFromActualPrice is negative, income should be negative\n                            // If percentFromActualPrice is positive, income should be positive\n                            const incomeMultiplier = percentFromActualPrice >= 0 ? 1 : -1;\n                            const absUnrealizedProfit = Math.abs(totalUnrealizedProfitInCrypto2);\n                            incomeCrypto2 = incomeMultiplier * (absUnrealizedProfit * state.config.incomeSplitCrypto2Percent) / 100;\n                            if (currentPrice > 0) {\n                                incomeCrypto1 = incomeMultiplier * (absUnrealizedProfit * state.config.incomeSplitCrypto1Percent / 100) / currentPrice;\n                            }\n                        } else {\n                            // For SimpleSpot mode, use the original logic\n                            incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                            if (currentPrice > 0) {\n                                incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                            }\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot starts if none exists and handle runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot starts\n            if (state.botSystemStatus === 'WarmingUp' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading - only when bot is running\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            // Only auto-save and create new sessions if the bot is actually running\n            if (currentSessionId && state.botSystemStatus === 'Running') {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during active trading, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot();\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": (savedSessionId)=>{\n                                sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Create new session for the new crypto pair\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair during active trading:', state.config.crypto1, '/', state.config.crypto2);\n                                toast({\n                                    title: \"Crypto Pair Changed During Trading\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2,\n        state.botSystemStatus\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": (backupSessionId)=>{\n                                            sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Session restoration on app startup\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const savedSession = localStorage.getItem(SESSION_STORAGE_KEY);\n            if (savedSession) {\n                try {\n                    const sessionData = JSON.parse(savedSession);\n                    dispatch({\n                        type: 'RESTORE_SESSION',\n                        payload: sessionData\n                    });\n                    console.log('🔄 Session restored from localStorage - manual bot start required');\n                } catch (error) {\n                    console.error('Failed to restore session:', error);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    // Auto-save session data every 30 seconds when trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const interval = setInterval({\n                \"TradingProvider.useEffect.interval\": ()=>{\n                    if (state.isTrading) {\n                        saveSessionToStorage(state);\n                    }\n                }\n            }[\"TradingProvider.useEffect.interval\"], 30000);\n            return ({\n                \"TradingProvider.useEffect\": ()=>clearInterval(interval)\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.isTrading,\n        state\n    ]);\n    // Auto-save balances after each trade\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveBalanceToStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        state.totalProfitLoss\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, create one first\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config).then({\n                            \"TradingProvider.useCallback[saveCurrentSession]\": (sessionId)=>{\n                                sessionManager.setCurrentSession(sessionId);\n                                sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                            }\n                        }[\"TradingProvider.useCallback[saveCurrentSession]\"]);\n                        return true;\n                    }\n                    return false;\n                }\n                // Save existing session\n                return sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Telegram notification callback\n    const sendTelegramNotificationCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotificationCallback]\": async (message, type)=>{\n            await sendTelegramNotification(message, type, state.telegramConfig);\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotificationCallback]\"], [\n        state.telegramConfig\n    ]);\n    // Session alarm callback\n    const playSessionAlarmCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSessionAlarmCallback]\": (type)=>{\n            playSessionAlarm(type, state.sessionAlarmConfig);\n        }\n    }[\"TradingProvider.useCallback[playSessionAlarmCallback]\"], [\n        state.sessionAlarmConfig\n    ]);\n    // StablecoinSwap price fetching callback\n    const fetchStablecoinSwapPriceCallback = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchStablecoinSwapPriceCallback]\": async (crypto1, crypto2)=>{\n            return await fetchStablecoinSwapPrice(crypto1, crypto2);\n        }\n    }[\"TradingProvider.useCallback[fetchStablecoinSwapPriceCallback]\"], []);\n    // Enhanced calculate total P/L callback with improved StablecoinSwap logic\n    const calculateTotalPL = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[calculateTotalPL]\": ()=>{\n            if (state.config.tradingMode === \"StablecoinSwap\") {\n                // Calculate total realized P/L in crypto2 terms using enhanced tracking\n                const totalRealizedPL = state.orderHistory.filter({\n                    \"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\": (entry)=>(entry.type === 'SELL' || entry.orderType === 'SELL') && entry.tradingMode === 'StablecoinSwap' && entry.realizedProfitLossCrypto2 !== undefined\n                }[\"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\"]).reduce({\n                    \"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\": (sum, entry)=>sum + (entry.realizedProfitLossCrypto2 || 0)\n                }[\"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\"], 0);\n                return totalRealizedPL;\n            } else {\n                // Enhanced SimpleSpot logic with better filtering\n                const totalRealizedPL = state.orderHistory.filter({\n                    \"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\": (entry)=>(entry.type === 'SELL' || entry.orderType === 'SELL') && (entry.tradingMode === 'SimpleSpot' || !entry.tradingMode) && entry.realizedProfitLossCrypto2 !== undefined\n                }[\"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\"]).reduce({\n                    \"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\": (sum, entry)=>sum + (entry.realizedProfitLossCrypto2 || 0)\n                }[\"TradingProvider.useCallback[calculateTotalPL].totalRealizedPL\"], 0);\n                return totalRealizedPL;\n            }\n        }\n    }[\"TradingProvider.useCallback[calculateTotalPL]\"], [\n        state.orderHistory,\n        state.config.tradingMode\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        sendTelegramNotification: sendTelegramNotificationCallback,\n        playSessionAlarm: playSessionAlarmCallback,\n        fetchStablecoinSwapPrice: fetchStablecoinSwapPriceCallback,\n        calculateTotalPL,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1993,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"OWfivgINct34aLyn48GSkMKClVg=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});