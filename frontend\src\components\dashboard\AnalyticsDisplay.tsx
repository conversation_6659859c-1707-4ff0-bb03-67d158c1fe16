
"use client";

import React, { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useTradingContext } from '@/contexts/TradingContext';
import { TrendingUp, TrendingDown, Percent, ListChecks } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { format } from 'date-fns';
import type { OrderHistoryEntry, TradingConfig } from '@/lib/types';

// Enhanced KPI calculation with StablecoinSwap support
const calculateKPIs = (orderHistory: OrderHistoryEntry[], config: TradingConfig) => {
  if (config.tradingMode === 'StablecoinSwap') {
    return calculateStablecoinSwapKPIs(orderHistory, config);
  } else {
    return calculateSimpleSpotKPIs(orderHistory, config);
  }
};

// StablecoinSwap-specific KPI calculations
const calculateStablecoinSwapKPIs = (orderHistory: OrderHistoryEntry[], config: TradingConfig) => {
  // Filter SELL trades (profit-realizing trades) for StablecoinSwap
  const sellTrades = orderHistory.filter(entry =>
    (entry.type === 'SELL' || entry.orderType === 'SELL') &&
    entry.tradingMode === 'StablecoinSwap' &&
    entry.realizedProfitLossCrypto2 !== undefined
  );

  // Calculate total realized P/L in crypto2 terms
  const totalProfitLossCrypto2 = sellTrades.reduce((sum, entry) => {
    return sum + (entry.realizedProfitLossCrypto2 || 0);
  }, 0);

  // Calculate total realized P/L in crypto1 terms
  const totalProfitLossCrypto1 = sellTrades.reduce((sum, entry) => {
    return sum + (entry.realizedProfitLossCrypto1 || 0);
  }, 0);

  // Calculate win rate based on profitable trades
  const profitableTrades = sellTrades.filter(entry => (entry.realizedProfitLossCrypto2 || 0) > 0).length;
  const winRate = sellTrades.length > 0 ? (profitableTrades / sellTrades.length) * 100 : 0;

  // Total trades executed (both BUY and SELL operations)
  const totalTradesExecuted = orderHistory.filter(entry => entry.tradingMode === 'StablecoinSwap').length;

  // Average profit per sell trade
  const avgProfitPerTradeCrypto2 = sellTrades.length > 0 ? totalProfitLossCrypto2 / sellTrades.length : 0;
  const avgProfitPerTradeCrypto1 = sellTrades.length > 0 ? totalProfitLossCrypto1 / sellTrades.length : 0;

  return {
    totalProfitLossCrypto2: totalProfitLossCrypto2.toFixed(config.numDigits),
    totalProfitLossCrypto1: totalProfitLossCrypto1.toFixed(config.numDigits),
    winRate: winRate.toFixed(2),
    totalTradesExecuted,
    avgProfitPerTradeCrypto2: avgProfitPerTradeCrypto2.toFixed(config.numDigits),
    avgProfitPerTradeCrypto1: avgProfitPerTradeCrypto1.toFixed(config.numDigits),
  };
};

// SimpleSpot-specific KPI calculations (original logic)
const calculateSimpleSpotKPIs = (orderHistory: OrderHistoryEntry[], config: TradingConfig) => {
  const sellTrades = orderHistory.filter(trade =>
    trade.orderType === 'SELL' &&
    trade.realizedProfitLossCrypto2 !== undefined &&
    (trade.tradingMode === 'SimpleSpot' || !trade.tradingMode)
  );

  const totalProfitLossCrypto2 = sellTrades.reduce((sum, trade) => sum + (trade.realizedProfitLossCrypto2 || 0), 0);
  const totalProfitLossCrypto1 = sellTrades.reduce((sum, trade) => sum + (trade.realizedProfitLossCrypto1 || 0), 0);
  const profitableTrades = sellTrades.filter(trade => (trade.realizedProfitLossCrypto2 || 0) > 0).length;
  const winRate = sellTrades.length > 0 ? (profitableTrades / sellTrades.length) * 100 : 0;
  const totalTradesExecuted = orderHistory.filter(entry =>
    entry.tradingMode === 'SimpleSpot' || !entry.tradingMode
  ).length;
  const avgProfitPerTradeCrypto2 = sellTrades.length > 0 ? totalProfitLossCrypto2 / sellTrades.length : 0;
  const avgProfitPerTradeCrypto1 = sellTrades.length > 0 ? totalProfitLossCrypto1 / sellTrades.length : 0;

  return {
    totalProfitLossCrypto2: totalProfitLossCrypto2.toFixed(config.numDigits),
    totalProfitLossCrypto1: totalProfitLossCrypto1.toFixed(config.numDigits),
    winRate: winRate.toFixed(2),
    totalTradesExecuted,
    avgProfitPerTradeCrypto2: avgProfitPerTradeCrypto2.toFixed(config.numDigits),
    avgProfitPerTradeCrypto1: avgProfitPerTradeCrypto1.toFixed(config.numDigits),
  };
};

interface PnlChartDataPoint {
  timestamp: number;
  date: string;
  pnl: number;
}

// Enhanced P/L chart data generation with StablecoinSwap support
const generatePnlChartData = (orderHistory: OrderHistoryEntry[], crypto2Symbol: string, tradingMode: string): PnlChartDataPoint[] => {
  if (!orderHistory || orderHistory.length === 0) {
    return [];
  }

  const sortedHistory = [...orderHistory].sort((a, b) => a.timestamp - b.timestamp);
  let sellTrades;

  if (tradingMode === 'StablecoinSwap') {
    // For StablecoinSwap, filter profit-realizing trades
    sellTrades = sortedHistory.filter(entry =>
      (entry.type === 'SELL' || entry.orderType === 'SELL') &&
      entry.tradingMode === 'StablecoinSwap' &&
      entry.realizedProfitLossCrypto2 !== undefined
    );
  } else {
    // For SimpleSpot, use original logic
    sellTrades = sortedHistory.filter(entry =>
      entry.orderType === 'SELL' &&
      entry.realizedProfitLossCrypto2 !== undefined &&
      (entry.tradingMode === 'SimpleSpot' || !entry.tradingMode)
    );
  }

  let cumulativePnl = 0;
  return sellTrades.map(entry => {
    cumulativePnl += entry.realizedProfitLossCrypto2 || 0;
    return {
      timestamp: entry.timestamp,
      date: format(new Date(entry.timestamp), 'MM/dd HH:mm'),
      pnl: parseFloat(cumulativePnl.toFixed(4)),
    };
  });
};

export default function AnalyticsDisplay() {
  const { orderHistory, config, getDisplayOrders } = useTradingContext();

  const kpis = useMemo(() => calculateKPIs(orderHistory, config), [orderHistory, config]);

  const pnlChartData = useMemo(
    () => generatePnlChartData(orderHistory, config.crypto2, config.tradingMode),
    [orderHistory, config.crypto2, config.tradingMode]
  );
  
  const currentUnrealizedPL = useMemo(() => {
    const currentActiveOrders = getDisplayOrders(); // getDisplayOrders is memoized in context
    const rawValue = currentActiveOrders.reduce((sum, order) => {
      if (order.status === 'Full' && order.incomeCrypto2 !== undefined) {
        return sum + order.incomeCrypto2;
      }
      return sum;
    }, 0);
    return rawValue.toFixed(config.numDigits);
  }, [getDisplayOrders, config.numDigits]);

  const kpiCards = useMemo(() => [
    { title: `Total Realized P/L (${config.crypto1 || "Crypto 1"})`, value: kpis.totalProfitLossCrypto1, icon: <TrendingUp className="h-6 w-6 text-primary" />, description: "Sum of profits from sell trades in Crypto1" },
    { title: `Total Realized P/L (${config.crypto2 || "Crypto 2"})`, value: kpis.totalProfitLossCrypto2, icon: <TrendingUp className="h-6 w-6 text-primary" />, description: "Sum of profits from sell trades in Crypto2" },
    { title: "Win Rate", value: `${kpis.winRate}%`, icon: <Percent className="h-6 w-6 text-primary" />, description: "Profitable sell trades / Total sell trades" },
    { title: "Total Trades Executed", value: kpis.totalTradesExecuted.toString(), icon: <ListChecks className="h-6 w-6 text-primary" />, description: "All buy and sell operations" },
    { title: `Avg. Profit per Sell (${config.crypto1 || "Crypto 1"})`, value: kpis.avgProfitPerTradeCrypto1, icon: <TrendingDown className="h-6 w-6 text-primary" />, description: "Average profit from sell trades in Crypto1" },
    { title: `Avg. Profit per Sell (${config.crypto2 || "Crypto 2"})`, value: kpis.avgProfitPerTradeCrypto2, icon: <TrendingDown className="h-6 w-6 text-primary" />, description: "Average profit from sell trades in Crypto2" },
    { title: `Current Unrealized P/L (${config.crypto2 || "Crypto 2"})`, value: currentUnrealizedPL, icon: <TrendingUp className="h-6 w-6 text-primary" />, description: "Potential P/L from open positions" },
  ], [kpis, currentUnrealizedPL, config.crypto1, config.crypto2]);

  // Memoize style objects for LineChart Tooltip
  const tooltipContentStyle = useMemo(() => ({ backgroundColor: "hsl(var(--card))", borderColor: "hsl(var(--border))" }), []);
  const tooltipLabelStyle = useMemo(() => ({ color: "hsl(var(--card-foreground))" }), []);
  const tooltipItemStyle = useMemo(() => ({ color: "hsl(var(--primary))" }), []);

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
        {kpiCards.map(kpi => (
          <Card key={kpi.title} className="border-2 border-border">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">{kpi.title}</CardTitle>
              {kpi.icon}
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-foreground">{kpi.value}</div>
              <p className="text-xs text-muted-foreground pt-1">{kpi.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>
      <Card className="border-2 border-border">
        <CardHeader>
          <CardTitle className="text-xl font-bold text-primary">Cumulative Profit/Loss Over Time ({config.crypto2})</CardTitle>
          <CardDescription>Chart visualization of trading performance.</CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          {pnlChartData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={pnlChartData} margin={{ top: 5, right: 20, left: -25, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis dataKey="date" stroke="hsl(var(--muted-foreground))" fontSize={12} tickLine={false} axisLine={false} />
                <YAxis stroke="hsl(var(--muted-foreground))" fontSize={12} tickLine={false} axisLine={false} tickFormatter={(value) => `${value}`} />
                <Tooltip
                  contentStyle={tooltipContentStyle}
                  labelStyle={tooltipLabelStyle}
                  itemStyle={tooltipItemStyle}
                />
                <Legend wrapperStyle={{fontSize: "12px"}}/>
                <Line
                  type="monotone"
                  dataKey="pnl"
                  name={`Cumulative P/L (${config.crypto2 || "Crypto 2"})`}
                  stroke="hsl(var(--primary))"
                  strokeWidth={2}
                  dot={pnlChartData.length > 100 ? false : { r: 3, fill: "hsl(var(--primary))" }}
                  activeDot={{ r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-full bg-card-foreground/5 rounded-sm">
              <p className="text-muted-foreground">No trading data available for the P/L chart yet.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
