"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ RotateCcw)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8\",\n            key: \"1357e3\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 3v5h5\",\n            key: \"1xhq8a\"\n        }\n    ]\n];\nconst RotateCcw = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"RotateCcw\", __iconNode);\n //# sourceMappingURL=rotate-ccw.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx":
/*!******************************************************!*\
  !*** ./src/components/dashboard/BalancesDisplay.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BalancesDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,RotateCcw,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,RotateCcw,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,RotateCcw,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,RotateCcw,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,RotateCcw,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,RotateCcw,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,RotateCcw,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bitcoin.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,RotateCcw,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bitcoin,Check,CircleDollarSign,Coins,Edit3,RotateCcw,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/coins.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction BalancesDisplay() {\n    _s();\n    const { crypto1Balance, crypto2Balance, stablecoinBalance, config, dispatch, calculateTotalPL, resetGlobalBalances } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const [editingBalance, setEditingBalance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [tempValues, setTempValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        crypto1: crypto1Balance.toString(),\n        crypto2: crypto2Balance.toString(),\n        stablecoin: stablecoinBalance.toString()\n    });\n    const formatBalance = (balance)=>balance.toFixed(config.numDigits);\n    const handleEdit = (balanceType)=>{\n        setEditingBalance(balanceType);\n        setTempValues({\n            crypto1: crypto1Balance.toString(),\n            crypto2: crypto2Balance.toString(),\n            stablecoin: stablecoinBalance.toString()\n        });\n    };\n    const handleSave = (balanceType)=>{\n        const newValue = parseFloat(tempValues[balanceType]);\n        if (!isNaN(newValue) && newValue >= 0) {\n            if (balanceType === 'crypto1') {\n                dispatch({\n                    type: 'UPDATE_BALANCES',\n                    payload: {\n                        crypto1: newValue,\n                        crypto2: crypto2Balance\n                    }\n                });\n            } else if (balanceType === 'crypto2') {\n                dispatch({\n                    type: 'UPDATE_BALANCES',\n                    payload: {\n                        crypto1: crypto1Balance,\n                        crypto2: newValue\n                    }\n                });\n            } else if (balanceType === 'stablecoin') {\n                dispatch({\n                    type: 'UPDATE_STABLECOIN_BALANCE',\n                    payload: newValue\n                });\n            }\n        }\n        setEditingBalance(null);\n    };\n    const handleCancel = ()=>{\n        setEditingBalance(null);\n        setTempValues({\n            crypto1: crypto1Balance.toString(),\n            crypto2: crypto2Balance.toString(),\n            stablecoin: stablecoinBalance.toString()\n        });\n    };\n    const handleResetGlobalBalances = ()=>{\n        const defaultBalances = resetGlobalBalances();\n        toast({\n            title: \"Global Balances Reset\",\n            description: \"Balances reset to defaults: \".concat(defaultBalances.crypto1Balance, \" \").concat(config.crypto1 || 'Crypto1', \", \").concat(defaultBalances.crypto2Balance, \" \").concat(config.crypto2 || 'Crypto2', \", \").concat(defaultBalances.stablecoinBalance, \" \").concat(config.preferredStablecoin || 'Stablecoin')\n        });\n    };\n    // Enhanced Total P/L display with StablecoinSwap support\n    const renderTotalPL = ()=>{\n        const totalPL = calculateTotalPL();\n        const currency = config.tradingMode === 'StablecoinSwap' ? config.crypto2 : 'USD';\n        const symbol = config.tradingMode === 'StablecoinSwap' ? '' : '$';\n        const colorClass = totalPL >= 0 ? 'text-green-600' : 'text-red-600';\n        const icon = totalPL >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            className: \"h-5 w-5 text-green-600\"\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n            lineNumber: 70,\n            columnNumber: 33\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            className: \"h-5 w-5 text-red-600\"\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n            lineNumber: 70,\n            columnNumber: 85\n        }, this);\n        const profitIcon = totalPL >= 0 ? '📈' : '📉';\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-sm font-medium text-muted-foreground\",\n                            children: \"Total Realized P/L\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        icon\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold \".concat(colorClass, \" flex items-center gap-2\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: profitIcon\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                symbol,\n                                                totalPL >= 0 ? '+' : '',\n                                                totalPL.toFixed(config.numDigits),\n                                                \" \",\n                                                currency\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: config.tradingMode === 'StablecoinSwap' ? 'StablecoinSwap Mode' : 'SimpleSpot Mode'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this);\n    };\n    const renderBalanceCard = (title, balance, balanceType, icon, currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-sm font-medium text-muted-foreground\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 9\n                        }, this),\n                        icon\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: editingBalance === balanceType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                type: \"number\",\n                                value: tempValues[balanceType],\n                                onChange: (e)=>setTempValues((prev)=>({\n                                            ...prev,\n                                            [balanceType]: e.target.value\n                                        })),\n                                className: \"text-lg font-bold\",\n                                step: \"any\",\n                                min: \"0\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        size: \"sm\",\n                                        onClick: ()=>handleSave(balanceType),\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Save\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        size: \"sm\",\n                                        variant: \"outline\",\n                                        onClick: handleCancel,\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cancel\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-foreground\",\n                                        children: formatBalance(balance)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"Available \",\n                                            currency\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                size: \"sm\",\n                                variant: \"ghost\",\n                                onClick: ()=>handleEdit(balanceType),\n                                className: \"ml-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n            lineNumber: 105,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4 mb-6\",\n        children: [\n            renderTotalPL(),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    size: \"sm\",\n                    variant: \"outline\",\n                    onClick: handleResetGlobalBalances,\n                    className: \"text-orange-600 border-orange-600 hover:bg-orange-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"h-4 w-4 mr-2\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this),\n                        \"Reset Global Balances\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-3\",\n                children: [\n                    renderBalanceCard(\"\".concat(config.crypto1 || \"Crypto 1\", \" Balance\"), crypto1Balance, 'crypto1', /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-5 w-5 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this), config.crypto1 || \"Crypto 1\"),\n                    renderBalanceCard(\"\".concat(config.crypto2 || \"Crypto 2\", \" Balance\"), crypto2Balance, 'crypto2', /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-5 w-5 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this), config.crypto2 || \"Crypto 2\"),\n                    renderBalanceCard(\"Stablecoin Balance (\".concat(config.preferredStablecoin || 'N/A', \")\"), stablecoinBalance, 'stablecoin', /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bitcoin_Check_CircleDollarSign_Coins_Edit3_RotateCcw_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-5 w-5 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this), 'Stablecoins')\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\BalancesDisplay.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, this);\n}\n_s(BalancesDisplay, \"6GMfjJ84uDg2u8xN//yOfKGDDnI=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = BalancesDisplay;\nvar _c;\n$RefreshReg$(_c, \"BalancesDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/BalancesDisplay.tsx\n"));

/***/ })

});