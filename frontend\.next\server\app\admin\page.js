(()=>{var e={};e.id=698,e.ids=[698],e.modules={643:e=>{"use strict";e.exports=require("node:perf_hooks")},1132:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\bot\\tradingbot_final\\frontend\\src\\app\\admin\\page.tsx","default")},1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},7252:e=>{"use strict";e.exports=require("express")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11003:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},11723:e=>{"use strict";e.exports=require("querystring")},11997:e=>{"use strict";e.exports=require("punycode")},12412:e=>{"use strict";e.exports=require("assert")},14985:e=>{"use strict";e.exports=require("dns")},16141:e=>{"use strict";e.exports=require("node:zlib")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},19063:e=>{"use strict";e.exports=require("require-in-the-middle")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19185:e=>{"use strict";e.exports=require("dgram")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},23056:(e,t,s)=>{Promise.resolve().then(s.bind(s,35603))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var r=s(60687),a=s(43210),i=s(8730),n=s(24224),o=s(4780);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:s,asChild:a=!1,...n},d)=>{let c=a?i.DX:"button";return(0,r.jsx)(c,{className:(0,o.cn)(l({variant:t,size:s,className:e})),ref:d,...n})});d.displayName="Button"},29976:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1132)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\admin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"E:\\bot\\tradingbot_final\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["E:\\bot\\tradingbot_final\\frontend\\src\\app\\admin\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35603:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>R});var r=s(60687),a=s(43210),i=s(16189),n=s(85763),o=s(29523),l=s(89667),d=s(80013),c=s(44493),u=s(78895),p=s(29867),m=s(42692),x=s(58369),h=s(82614);let v=(0,h.A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]),g=(0,h.A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]),f=(0,h.A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var y=s(24026),b=s(11003),j=s(76311),N=s(96834);let k=(0,h.A)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),S=(0,h.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),w=(0,h.A)("Pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),C=(0,h.A)("FolderOpen",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]),A=(0,h.A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);var T=s(15036),E=s(57207),q=s(5551);function _(){let{config:e,targetPriceRows:t,orderHistory:s,currentMarketPrice:i,crypto1Balance:n,crypto2Balance:d,stablecoinBalance:x,botSystemStatus:h,dispatch:v}=(0,u.U)(),{toast:g}=(0,p.dj)(),[y,b]=(0,a.useState)([]),[j,_]=(0,a.useState)(0),[R,M]=(0,a.useState)(null),[P,$]=(0,a.useState)(null),[I,B]=(0,a.useState)(""),[z,D]=(0,a.useState)(0),F=q.C.getInstance(),O=()=>{let e=F.getAllSessions(),t=F.getFilteredSessions(5e3);_(e.length-t.length),b(t.sort((e,t)=>t.lastModified-e.lastModified))},L=async()=>{if(!R){g({title:"Error",description:"No active session to save",variant:"destructive"});return}try{let r,a;let o=F.loadSession(R);if(!o){g({title:"Error",description:"Current session not found",variant:"destructive"});return}let l=F.getAllSessions(),c=o.name.replace(/ \((Saved|AutoSaved).*\)$/,""),u=l.find(e=>e.id!==R&&e.name.startsWith(c)&&e.name.includes("(Saved")&&!e.isActive);if(u){r=u.id;let e=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1});a=`${c} (Saved ${e})`,console.log(`📝 Updating existing saved session: ${a}`)}else{let t=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1});a=`${c} (Saved ${t})`,r=await F.createNewSession(a,e),console.log(`💾 Creating new saved session: ${a}`)}let p=F.getCurrentRuntime(R);u&&F.renameSession(r,a),F.saveSession(r,e,t,s,i,n,d,x,!1,p)?(O(),g({title:"Session Saved",description:u?`Save checkpoint updated (Runtime: ${G(p)})`:`Session saved as checkpoint (Runtime: ${G(p)})`})):g({title:"Error",description:"Failed to save session",variant:"destructive"})}catch(e){console.error("Error saving session:",e),g({title:"Error",description:"Failed to save session",variant:"destructive"})}},H=e=>{let t=F.loadSession(e);if(!t){g({title:"Error",description:"Failed to load session",variant:"destructive"});return}v({type:"SET_CONFIG",payload:t.config}),v({type:"SET_TARGET_PRICE_ROWS",payload:t.targetPriceRows}),v({type:"CLEAR_ORDER_HISTORY"}),t.orderHistory.forEach(e=>{v({type:"ADD_ORDER_HISTORY_ENTRY",payload:e})}),v({type:"SET_MARKET_PRICE",payload:t.currentMarketPrice}),v({type:"SET_BALANCES",payload:{crypto1:t.crypto1Balance,crypto2:t.crypto2Balance}}),v({type:"SYSTEM_STOP_BOT"}),F.setCurrentSession(e),M(e),O(),g({title:"Session Loaded",description:`Session "${t.name}" has been loaded. Bot stopped - manual start required.`,duration:5e3})},K=e=>{F.deleteSession(e)&&(R===e&&M(null),O(),g({title:"Session Deleted",description:"Session has been deleted successfully"}))},V=e=>{I.trim()&&F.renameSession(e,I.trim())&&($(null),B(""),O(),g({title:"Session Renamed",description:"Session has been renamed successfully"}))},U=e=>{let t=F.exportSessionToCSV(e);if(!t){g({title:"Error",description:"Failed to export session",variant:"destructive"});return}let s=F.loadSession(e),r=new Blob([t],{type:"text/csv;charset=utf-8;"}),a=document.createElement("a"),i=URL.createObjectURL(r);a.setAttribute("href",i),a.setAttribute("download",`${s?.name||"session"}_${new Date().toISOString().split("T")[0]}.csv`),a.style.visibility="hidden",document.body.appendChild(a),a.click(),document.body.removeChild(a),g({title:"Export Complete",description:"Session data has been exported to CSV"})},G=e=>{if(!e||e<0)return"0s";let t=Math.floor(e/1e3),s=Math.floor(t/3600),r=Math.floor(t%3600/60),a=t%60;return s>0?`${s}h ${r}m ${a}s`:r>0?`${r}m ${a}s`:`${a}s`},W=()=>y.filter(e=>e.isActive),Y=()=>y.filter(e=>!e.isActive);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsx)(c.aR,{children:(0,r.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(k,{className:"h-5 w-5"}),"Current Sessions"]})}),(0,r.jsx)(c.Wu,{children:W().length>0?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground",children:[(0,r.jsx)("div",{children:"Session Name"}),(0,r.jsx)("div",{children:"Active Status"}),(0,r.jsx)("div",{children:"Runtime"}),(0,r.jsx)("div",{children:"Actions"})]}),(0,r.jsx)("div",{className:"space-y-2",children:W().map(e=>(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-2",children:P===e.id?(0,r.jsxs)("div",{className:"flex gap-2 flex-1",children:[(0,r.jsx)(l.p,{value:I,onChange:e=>B(e.target.value),onKeyPress:t=>"Enter"===t.key&&V(e.id),className:"text-sm"}),(0,r.jsx)(o.$,{size:"sm",onClick:()=>V(e.id),children:(0,r.jsx)(S,{className:"h-3 w-3"})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"font-medium",children:e.name}),(0,r.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>{$(e.id),B(e.name)},children:(0,r.jsx)(w,{className:"h-3 w-3"})})]})}),(0,r.jsx)("div",{children:(0,r.jsx)(N.E,{variant:"default",children:"Active"})}),(0,r.jsx)("div",{className:"text-sm",children:e.id===R?G(z):G(e.runtime)}),(0,r.jsx)("div",{children:e.id===R?(0,r.jsxs)(o.$,{onClick:L,size:"sm",className:"btn-neo",children:[(0,r.jsx)(S,{className:"mr-2 h-3 w-3"}),"Save"]}):(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>H(e.id),title:"Load Session",children:(0,r.jsx)(C,{className:"h-3 w-3"})}),(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>U(e.id),title:"Export Session",children:(0,r.jsx)(A,{className:"h-3 w-3"})})]})})]},e.id))})]}):(0,r.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,r.jsx)(k,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{children:"No active session"}),(0,r.jsx)("p",{className:"text-xs",children:"Start trading to create a session automatically"})]})})]}),(0,r.jsxs)(c.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,r.jsxs)(c.aR,{children:[(0,r.jsxs)(c.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(f,{className:"h-5 w-5"}),"Past Sessions (",Y().length,")"]}),(0,r.jsxs)(c.BT,{children:["Showing: ",Y().length," sessions",j>0&&(0,r.jsxs)("span",{className:"text-muted-foreground ml-2",children:["(",j," short sessions hidden)"]})]})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsx)(m.F,{className:"h-[400px]",children:0===Y().length?(0,r.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,r.jsx)(T.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{children:"No saved sessions yet."}),(0,r.jsx)("p",{className:"text-xs",children:"Save your current session to get started."})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground",children:[(0,r.jsx)("div",{children:"Session Name"}),(0,r.jsx)("div",{children:"Active Status"}),(0,r.jsx)("div",{children:"Total Runtime"}),(0,r.jsx)("div",{children:"Actions"})]}),(0,r.jsx)("div",{className:"space-y-2",children:Y().map(e=>(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-2",children:P===e.id?(0,r.jsxs)("div",{className:"flex gap-2 flex-1",children:[(0,r.jsx)(l.p,{value:I,onChange:e=>B(e.target.value),onKeyPress:t=>"Enter"===t.key&&V(e.id),className:"text-sm"}),(0,r.jsx)(o.$,{size:"sm",onClick:()=>V(e.id),children:(0,r.jsx)(S,{className:"h-3 w-3"})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"font-medium",children:e.name}),(0,r.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>{$(e.id),B(e.name)},children:(0,r.jsx)(w,{className:"h-3 w-3"})})]})}),(0,r.jsx)("div",{children:(0,r.jsx)(N.E,{variant:"secondary",children:"Inactive"})}),(0,r.jsx)("div",{className:"text-sm",children:G(F.getCurrentRuntime(e.id))}),(0,r.jsxs)("div",{className:"flex gap-1",children:[(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>H(e.id),title:"Load Session",children:(0,r.jsx)(C,{className:"h-3 w-3"})}),(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>U(e.id),title:"Export Session",children:(0,r.jsx)(A,{className:"h-3 w-3"})}),(0,r.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>K(e.id),title:"Delete Session",children:(0,r.jsx)(E.A,{className:"h-3 w-3"})})]})]},e.id))})]})})})]})]})}function R(){let{botSystemStatus:e}=(0,u.U)(),[t,s]=(0,a.useState)("iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA"),[h,N]=(0,a.useState)("jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp"),[k,S]=(0,a.useState)(!1),[w,C]=(0,a.useState)(!1),[A,T]=(0,a.useState)(""),[E,q]=(0,a.useState)(""),{toast:R}=(0,p.dj)(),M=(0,i.useRouter)(),P=async()=>{try{localStorage.setItem("binance_api_key",t),localStorage.setItem("binance_api_secret",h),console.log("API Keys Saved:",{apiKey:t.substring(0,10)+"...",apiSecret:h.substring(0,10)+"..."}),R({title:"API Keys Saved",description:"Binance API keys have been saved securely."})}catch(e){R({title:"Error",description:"Failed to save API keys.",variant:"destructive"})}},$=async()=>{try{(await fetch("https://api.binance.com/api/v3/ping")).ok?R({title:"API Connection Test",description:"Successfully connected to Binance API!"}):R({title:"Connection Failed",description:"Unable to connect to Binance API.",variant:"destructive"})}catch(e){R({title:"Connection Error",description:"Network error while testing API connection.",variant:"destructive"})}},I=async()=>{if(!A||!E){R({title:"Missing Configuration",description:"Please enter both Telegram bot token and chat ID.",variant:"destructive"});return}try{(await fetch(`https://api.telegram.org/bot${A}/sendMessage`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:E,text:"\uD83E\uDD16 Test message from Pluto Trading Bot! Your Telegram integration is working correctly."})})).ok?R({title:"Telegram Test Successful",description:"Test message sent successfully!"}):R({title:"Telegram Test Failed",description:"Failed to send test message. Check your token and chat ID.",variant:"destructive"})}catch(e){R({title:"Telegram Error",description:"Network error while testing Telegram integration.",variant:"destructive"})}},B=[{value:"systemTools",label:"System Tools",icon:(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"})},{value:"apiKeys",label:"Exchange API Keys",icon:(0,r.jsx)(v,{className:"mr-2 h-4 w-4"})},{value:"telegram",label:"Telegram Integration",icon:(0,r.jsx)(g,{className:"mr-2 h-4 w-4"})},{value:"sessionManager",label:"Session Manager",icon:(0,r.jsx)(f,{className:"mr-2 h-4 w-4"})}];return(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,r.jsxs)(c.Zp,{className:"border-2 border-border",children:[(0,r.jsxs)(c.aR,{className:"flex flex-row justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(c.ZB,{className:"text-3xl font-bold text-primary",children:"Admin Panel"}),(0,r.jsx)(c.BT,{children:"Manage global settings and tools for Pluto Trading Bot."})]}),(0,r.jsxs)(o.$,{variant:"outline",onClick:()=>M.push("/dashboard"),className:"btn-outline-neo",children:[(0,r.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Return to Dashboard"]})]}),(0,r.jsx)(c.Wu,{children:(0,r.jsxs)(n.tU,{defaultValue:"systemTools",className:"w-full",children:[(0,r.jsx)(m.F,{className:"pb-2",children:(0,r.jsx)(n.j7,{className:"grid w-full grid-cols-4 mb-6",children:B.map(e=>(0,r.jsx)(n.Xi,{value:e.value,className:"px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",children:(0,r.jsxs)("div",{className:"flex items-center",children:[e.icon," ",e.label]})},e.value))})}),(0,r.jsx)(n.av,{value:"systemTools",className:"space-y-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-primary",children:"System Management Tools"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>R({title:"DB Editor Clicked"}),children:"View Database (Read-Only)"}),(0,r.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>R({title:"Export Orders Clicked"}),children:"Export Orders to Excel"}),(0,r.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>R({title:"Export History Clicked"}),children:"Export History to Excel"}),(0,r.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>R({title:"Backup DB Clicked"}),children:"Backup Database"}),(0,r.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>R({title:"Restore DB Clicked"}),disabled:!0,children:"Restore Database"}),(0,r.jsx)(o.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>R({title:"Diagnostics Clicked"}),children:"Run System Diagnostics"})]})]})}),(0,r.jsx)(n.av,{value:"apiKeys",className:"space-y-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-primary",children:"Exchange API Configuration"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"apiKey",children:"Binance API Key"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.p,{id:"apiKey",placeholder:"Enter your Binance API Key",type:k?"text":"password",value:t,onChange:e=>s(e.target.value),className:"pr-10"}),(0,r.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>S(!k),children:k?(0,r.jsx)(b.A,{className:"h-4 w-4"}):(0,r.jsx)(j.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"apiSecret",children:"Binance API Secret"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.p,{id:"apiSecret",placeholder:"Enter your Binance API Secret",type:w?"text":"password",value:h,onChange:e=>N(e.target.value),className:"pr-10"}),(0,r.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>C(!w),children:w?(0,r.jsx)(b.A,{className:"h-4 w-4"}):(0,r.jsx)(j.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(o.$,{onClick:P,className:"btn-neo",children:"Save API Keys"}),(0,r.jsx)(o.$,{onClick:$,variant:"outline",className:"btn-outline-neo",children:"Test Connection"})]})]})]})}),(0,r.jsx)(n.av,{value:"telegram",className:"space-y-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-primary",children:"Telegram Bot Integration"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"telegramToken",children:"Telegram Bot Token"}),(0,r.jsx)(l.p,{id:"telegramToken",placeholder:"Enter your Telegram Bot Token",type:"password",value:A,onChange:e=>T(e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"telegramChatId",children:"Telegram Chat ID"}),(0,r.jsx)(l.p,{id:"telegramChatId",placeholder:"Enter your Telegram Chat ID",type:"text",value:E,onChange:e=>q(e.target.value)})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(o.$,{onClick:()=>{try{localStorage.setItem("telegram_bot_token",A),localStorage.setItem("telegram_chat_id",E),console.log("Telegram Config Saved:",{telegramToken:A.substring(0,10)+"...",telegramChatId:E}),R({title:"Telegram Config Saved",description:"Telegram settings have been saved successfully."})}catch(e){R({title:"Error",description:"Failed to save Telegram configuration.",variant:"destructive"})}},className:"btn-neo flex-1",children:"Save Telegram Config"}),(0,r.jsx)(o.$,{onClick:I,variant:"outline",className:"btn-outline-neo flex-1",children:"Test Telegram"})]})]}),(0,r.jsxs)("div",{className:"bg-muted p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-semibold mb-2",children:"Setup Guide:"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-sm",children:[(0,r.jsx)("li",{children:"Create a new bot by messaging @BotFather on Telegram"}),(0,r.jsx)("li",{children:"Use the command /newbot and follow the instructions"}),(0,r.jsx)("li",{children:"Copy the bot token provided by BotFather"}),(0,r.jsx)("li",{children:"Start a chat with your bot and send any message"}),(0,r.jsx)("li",{children:"Get your chat ID by visiting: https://api.telegram.org/bot[YOUR_BOT_TOKEN]/getUpdates"}),(0,r.jsx)("li",{children:"Enter both the bot token and chat ID above, then test the connection"})]})]})]})}),(0,r.jsx)(n.av,{value:"sessionManager",className:"space-y-6",children:(0,r.jsx)(_,{})})]})})]})})}},36686:e=>{"use strict";e.exports=require("diagnostics_channel")},37067:e=>{"use strict";e.exports=require("node:http")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41204:e=>{"use strict";e.exports=require("string_decoder")},42692:(e,t,s)=>{"use strict";s.d(t,{$:()=>l,F:()=>o});var r=s(60687),a=s(43210),i=s(68123),n=s(4780);let o=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(i.bL,{ref:a,className:(0,n.cn)("relative overflow-hidden",e),...s,children:[(0,r.jsx)(i.LM,{className:"h-full w-full rounded-[inherit]",children:t}),(0,r.jsx)(l,{}),(0,r.jsx)(i.OK,{})]}));o.displayName=i.bL.displayName;let l=a.forwardRef(({className:e,orientation:t="vertical",...s},a)=>(0,r.jsx)(i.VM,{ref:a,orientation:t,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...s,children:(0,r.jsx)(i.lr,{className:"relative flex-1 rounded-full bg-border"})}));l.displayName=i.VM.displayName},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>o});var r=s(60687),a=s(43210),i=s(4780);let n=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",e),...t}));n.displayName="Card";let o=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-4 md:p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,i.cn)("text-xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-4 md:p-6 pt-0",e),...t}));c.displayName="CardContent",a.forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-4 md:p-6 pt-0",e),...t})).displayName="CardFooter"},44708:e=>{"use strict";e.exports=require("node:https")},54379:e=>{"use strict";e.exports=require("node:path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56801:e=>{"use strict";e.exports=require("import-in-the-middle")},57075:e=>{"use strict";e.exports=require("node:stream")},57207:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64912:(e,t,s)=>{Promise.resolve().then(s.bind(s,1132))},73024:e=>{"use strict";e.exports=require("node:fs")},73136:e=>{"use strict";e.exports=require("node:url")},73496:e=>{"use strict";e.exports=require("http2")},73566:e=>{"use strict";e.exports=require("worker_threads")},74075:e=>{"use strict";e.exports=require("zlib")},74998:e=>{"use strict";e.exports=require("perf_hooks")},76311:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(82614).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},77030:e=>{"use strict";e.exports=require("node:net")},77598:e=>{"use strict";e.exports=require("node:crypto")},78148:(e,t,s)=>{"use strict";s.d(t,{b:()=>o});var r=s(43210),a=s(14163),i=s(60687),n=r.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=n},78474:e=>{"use strict";e.exports=require("node:events")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},79748:e=>{"use strict";e.exports=require("fs/promises")},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var r=s(60687),a=s(43210),i=s(78148),n=s(24224),o=s(4780);let l=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.b,{ref:s,className:(0,o.cn)(l(),e),...t}));d.displayName=i.b.displayName},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84297:e=>{"use strict";e.exports=require("async_hooks")},85763:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>d,av:()=>c,j7:()=>l,tU:()=>o});var r=s(60687),a=s(43210),i=s(41360),n=s(4780);let o=i.bL,l=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.B8,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));l.displayName=i.B8.displayName;let d=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.l9,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));d.displayName=i.l9.displayName;let c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.UC,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=i.UC.displayName},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(60687),a=s(43210),i=s(4780);let n=a.forwardRef(({className:e,type:t,...s},a)=>(0,r.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...s}));n.displayName="Input"},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(60687);s(43210);var a=s(24224),i=s(4780);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,i.cn)(n({variant:t}),e),...s})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[992,194,740],()=>s(29976));module.exports=r})();