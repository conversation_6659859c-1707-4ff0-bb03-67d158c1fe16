(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{12136:(e,s,t)=>{Promise.resolve().then(t.bind(t,13290))},13290:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>I});var a=t(95155),r=t(12115),i=t(35695),n=t(17313),l=t(30285),o=t(62523),c=t(85057),d=t(66695),m=t(77213),u=t(87481),h=t(66424),x=t(18271),p=t(93576),g=t(73503),v=t(15300),f=t(18186),b=t(4607),j=t(17607),y=t(26126),N=t(40207),S=t(59119),w=t(23129),C=t(32773),k=t(68718),A=t(37648),T=t(77223),E=t(84553);function R(){let{config:e,targetPriceRows:s,orderHistory:t,currentMarketPrice:i,crypto1Balance:n,crypto2Balance:c,stablecoinBalance:x,botSystemStatus:p,dispatch:g}=(0,m.U)(),{toast:f}=(0,u.dj)(),[b,j]=(0,r.useState)([]),[R,I]=(0,r.useState)(0),[B,_]=(0,r.useState)(null),[D,P]=(0,r.useState)(null),[F,$]=(0,r.useState)(""),[O,z]=(0,r.useState)(0),L=E.C.getInstance();(0,r.useEffect)(()=>{M();let e=L.getCurrentSessionId();_(e),e&&z(L.getCurrentRuntime(e));let s=e=>{"trading_sessions"===e.key&&e.newValue&&(M(),console.log("\uD83D\uDD04 Sessions synced from another window"))};return window.addEventListener("storage",s),()=>{window.removeEventListener("storage",s)}},[]),(0,r.useEffect)(()=>{let e=setInterval(()=>{if(B){let e=L.getCurrentRuntime(B);z(e),console.log("⏱️ Runtime update: ".concat(J(e)," for session ").concat(B))}},5e3);return()=>clearInterval(e)},[B,L]);let M=()=>{let e=L.getAllSessions(),s=L.getFilteredSessions(5e3);I(e.length-s.length),j(s.sort((e,s)=>s.lastModified-e.lastModified))},K=async()=>{if(!B){f({title:"Error",description:"No active session to save",variant:"destructive"});return}try{let a,r;let l=L.loadSession(B);if(!l){f({title:"Error",description:"Current session not found",variant:"destructive"});return}let o=L.getAllSessions(),d=l.name.replace(/ \((Saved|AutoSaved).*\)$/,""),m=o.find(e=>e.id!==B&&e.name.startsWith(d)&&e.name.includes("(Saved")&&!e.isActive);if(m){a=m.id;let e=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1});r="".concat(d," (Saved ").concat(e,")"),console.log("\uD83D\uDCDD Updating existing saved session: ".concat(r))}else{let s=new Date().toLocaleString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!1});r="".concat(d," (Saved ").concat(s,")"),a=await L.createNewSession(r,e),console.log("\uD83D\uDCBE Creating new saved session: ".concat(r))}let u=L.getCurrentRuntime(B);m&&L.renameSession(a,r),L.saveSession(a,e,s,t,i,n,c,x,!1,u)?(M(),f({title:"Session Saved",description:m?"Save checkpoint updated (Runtime: ".concat(J(u),")"):"Session saved as checkpoint (Runtime: ".concat(J(u),")")})):f({title:"Error",description:"Failed to save session",variant:"destructive"})}catch(e){console.error("Error saving session:",e),f({title:"Error",description:"Failed to save session",variant:"destructive"})}},U=e=>{let s=L.loadSession(e);if(!s){f({title:"Error",description:"Failed to load session",variant:"destructive"});return}g({type:"SET_CONFIG",payload:s.config}),g({type:"SET_TARGET_PRICE_ROWS",payload:s.targetPriceRows}),g({type:"CLEAR_ORDER_HISTORY"}),s.orderHistory.forEach(e=>{g({type:"ADD_ORDER_HISTORY_ENTRY",payload:e})}),g({type:"SET_MARKET_PRICE",payload:s.currentMarketPrice}),g({type:"SET_BALANCES",payload:{crypto1:s.crypto1Balance,crypto2:s.crypto2Balance}}),g({type:"SYSTEM_STOP_BOT"}),L.setCurrentSession(e),_(e),M(),f({title:"Session Loaded",description:'Session "'.concat(s.name,'" has been loaded. Bot stopped - manual start required.'),duration:5e3})},V=e=>{L.deleteSession(e)&&(B===e&&_(null),M(),f({title:"Session Deleted",description:"Session has been deleted successfully"}))},W=e=>{F.trim()&&L.renameSession(e,F.trim())&&(P(null),$(""),M(),f({title:"Session Renamed",description:"Session has been renamed successfully"}))},Y=e=>{let s=L.exportSessionToCSV(e);if(!s){f({title:"Error",description:"Failed to export session",variant:"destructive"});return}let t=L.loadSession(e),a=new Blob([s],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a"),i=URL.createObjectURL(a);r.setAttribute("href",i),r.setAttribute("download","".concat((null==t?void 0:t.name)||"session","_").concat(new Date().toISOString().split("T")[0],".csv")),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r),f({title:"Export Complete",description:"Session data has been exported to CSV"})},J=e=>{if(!e||e<0)return"0s";let s=Math.floor(e/1e3),t=Math.floor(s/3600),a=Math.floor(s%3600/60),r=s%60;return t>0?"".concat(t,"h ").concat(a,"m ").concat(r,"s"):a>0?"".concat(a,"m ").concat(r,"s"):"".concat(r,"s")},Z=()=>b.filter(e=>e.isActive),H=()=>b.filter(e=>!e.isActive);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(d.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,a.jsx)(d.aR,{children:(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-5 w-5"}),"Current Sessions"]})}),(0,a.jsx)(d.Wu,{children:Z().length>0?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground",children:[(0,a.jsx)("div",{children:"Session Name"}),(0,a.jsx)("div",{children:"Active Status"}),(0,a.jsx)("div",{children:"Runtime"}),(0,a.jsx)("div",{children:"Actions"})]}),(0,a.jsx)("div",{className:"space-y-2",children:Z().map(e=>(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:D===e.id?(0,a.jsxs)("div",{className:"flex gap-2 flex-1",children:[(0,a.jsx)(o.p,{value:F,onChange:e=>$(e.target.value),onKeyPress:s=>"Enter"===s.key&&W(e.id),className:"text-sm"}),(0,a.jsx)(l.$,{size:"sm",onClick:()=>W(e.id),children:(0,a.jsx)(S.A,{className:"h-3 w-3"})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsx)(l.$,{size:"sm",variant:"ghost",onClick:()=>{P(e.id),$(e.name)},children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]})}),(0,a.jsx)("div",{children:(0,a.jsx)(y.E,{variant:"default",children:"Active"})}),(0,a.jsx)("div",{className:"text-sm",children:e.id===B?J(O):J(e.runtime)}),(0,a.jsx)("div",{children:e.id===B?(0,a.jsxs)(l.$,{onClick:K,size:"sm",className:"btn-neo",children:[(0,a.jsx)(S.A,{className:"mr-2 h-3 w-3"}),"Save"]}):(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>U(e.id),title:"Load Session",children:(0,a.jsx)(C.A,{className:"h-3 w-3"})}),(0,a.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>Y(e.id),title:"Export Session",children:(0,a.jsx)(k.A,{className:"h-3 w-3"})})]})})]},e.id))})]}):(0,a.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,a.jsx)(N.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("p",{children:"No active session"}),(0,a.jsx)("p",{className:"text-xs",children:"Start trading to create a session automatically"})]})})]}),(0,a.jsxs)(d.Zp,{className:"bg-card-foreground/5 border-border border-2",children:[(0,a.jsxs)(d.aR,{children:[(0,a.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5"}),"Past Sessions (",H().length,")"]}),(0,a.jsxs)(d.BT,{children:["Showing: ",H().length," sessions",R>0&&(0,a.jsxs)("span",{className:"text-muted-foreground ml-2",children:["(",R," short sessions hidden)"]})]})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)(h.F,{className:"h-[400px]",children:0===H().length?(0,a.jsxs)("div",{className:"text-center text-muted-foreground py-8",children:[(0,a.jsx)(A.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,a.jsx)("p",{children:"No saved sessions yet."}),(0,a.jsx)("p",{className:"text-xs",children:"Save your current session to get started."})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground",children:[(0,a.jsx)("div",{children:"Session Name"}),(0,a.jsx)("div",{children:"Active Status"}),(0,a.jsx)("div",{children:"Total Runtime"}),(0,a.jsx)("div",{children:"Actions"})]}),(0,a.jsx)("div",{className:"space-y-2",children:H().map(e=>(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:D===e.id?(0,a.jsxs)("div",{className:"flex gap-2 flex-1",children:[(0,a.jsx)(o.p,{value:F,onChange:e=>$(e.target.value),onKeyPress:s=>"Enter"===s.key&&W(e.id),className:"text-sm"}),(0,a.jsx)(l.$,{size:"sm",onClick:()=>W(e.id),children:(0,a.jsx)(S.A,{className:"h-3 w-3"})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsx)(l.$,{size:"sm",variant:"ghost",onClick:()=>{P(e.id),$(e.name)},children:(0,a.jsx)(w.A,{className:"h-3 w-3"})})]})}),(0,a.jsx)("div",{children:(0,a.jsx)(y.E,{variant:"secondary",children:"Inactive"})}),(0,a.jsx)("div",{className:"text-sm",children:J(L.getCurrentRuntime(e.id))}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>U(e.id),title:"Load Session",children:(0,a.jsx)(C.A,{className:"h-3 w-3"})}),(0,a.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>Y(e.id),title:"Export Session",children:(0,a.jsx)(k.A,{className:"h-3 w-3"})}),(0,a.jsx)(l.$,{size:"sm",variant:"outline",onClick:()=>V(e.id),title:"Delete Session",children:(0,a.jsx)(T.A,{className:"h-3 w-3"})})]})]},e.id))})]})})})]})]})}function I(){let{botSystemStatus:e}=(0,m.U)(),[s,t]=(0,r.useState)("iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA"),[y,N]=(0,r.useState)("jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp"),[S,w]=(0,r.useState)(!1),[C,k]=(0,r.useState)(!1),[A,T]=(0,r.useState)(""),[E,I]=(0,r.useState)(""),{toast:B}=(0,u.dj)(),_=(0,i.useRouter)(),D=async()=>{try{localStorage.setItem("binance_api_key",s),localStorage.setItem("binance_api_secret",y),console.log("API Keys Saved:",{apiKey:s.substring(0,10)+"...",apiSecret:y.substring(0,10)+"..."}),B({title:"API Keys Saved",description:"Binance API keys have been saved securely."})}catch(e){B({title:"Error",description:"Failed to save API keys.",variant:"destructive"})}},P=async()=>{try{(await fetch("https://api.binance.com/api/v3/ping")).ok?B({title:"API Connection Test",description:"Successfully connected to Binance API!"}):B({title:"Connection Failed",description:"Unable to connect to Binance API.",variant:"destructive"})}catch(e){B({title:"Connection Error",description:"Network error while testing API connection.",variant:"destructive"})}},F=async()=>{if(!A||!E){B({title:"Missing Configuration",description:"Please enter both Telegram bot token and chat ID.",variant:"destructive"});return}try{(await fetch("https://api.telegram.org/bot".concat(A,"/sendMessage"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({chat_id:E,text:"\uD83E\uDD16 Test message from Pluto Trading Bot! Your Telegram integration is working correctly."})})).ok?B({title:"Telegram Test Successful",description:"Test message sent successfully!"}):B({title:"Telegram Test Failed",description:"Failed to send test message. Check your token and chat ID.",variant:"destructive"})}catch(e){B({title:"Telegram Error",description:"Network error while testing Telegram integration.",variant:"destructive"})}},$=[{value:"systemTools",label:"System Tools",icon:(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"})},{value:"apiKeys",label:"Exchange API Keys",icon:(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"})},{value:"telegram",label:"Telegram Integration",icon:(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4"})},{value:"sessionManager",label:"Session Manager",icon:(0,a.jsx)(v.A,{className:"mr-2 h-4 w-4"})}];return(0,a.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,a.jsxs)(d.Zp,{className:"border-2 border-border",children:[(0,a.jsxs)(d.aR,{className:"flex flex-row justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.ZB,{className:"text-3xl font-bold text-primary",children:"Admin Panel"}),(0,a.jsx)(d.BT,{children:"Manage global settings and tools for Pluto Trading Bot."})]}),(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>_.push("/dashboard"),className:"btn-outline-neo",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Return to Dashboard"]})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)(n.tU,{defaultValue:"systemTools",className:"w-full",children:[(0,a.jsx)(h.F,{className:"pb-2",children:(0,a.jsx)(n.j7,{className:"grid w-full grid-cols-4 mb-6",children:$.map(e=>(0,a.jsx)(n.Xi,{value:e.value,className:"px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.icon," ",e.label]})},e.value))})}),(0,a.jsx)(n.av,{value:"systemTools",className:"space-y-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary",children:"System Management Tools"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(l.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>B({title:"DB Editor Clicked"}),children:"View Database (Read-Only)"}),(0,a.jsx)(l.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>B({title:"Export Orders Clicked"}),children:"Export Orders to Excel"}),(0,a.jsx)(l.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>B({title:"Export History Clicked"}),children:"Export History to Excel"}),(0,a.jsx)(l.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>B({title:"Backup DB Clicked"}),children:"Backup Database"}),(0,a.jsx)(l.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>B({title:"Restore DB Clicked"}),disabled:!0,children:"Restore Database"}),(0,a.jsx)(l.$,{variant:"outline",className:"btn-outline-neo",onClick:()=>B({title:"Diagnostics Clicked"}),children:"Run System Diagnostics"})]})]})}),(0,a.jsx)(n.av,{value:"apiKeys",className:"space-y-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary",children:"Exchange API Configuration"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"apiKey",children:"Binance API Key"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{id:"apiKey",placeholder:"Enter your Binance API Key",type:S?"text":"password",value:s,onChange:e=>t(e.target.value),className:"pr-10"}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>w(!S),children:S?(0,a.jsx)(b.A,{className:"h-4 w-4"}):(0,a.jsx)(j.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"apiSecret",children:"Binance API Secret"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{id:"apiSecret",placeholder:"Enter your Binance API Secret",type:C?"text":"password",value:y,onChange:e=>N(e.target.value),className:"pr-10"}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>k(!C),children:C?(0,a.jsx)(b.A,{className:"h-4 w-4"}):(0,a.jsx)(j.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(l.$,{onClick:D,className:"btn-neo",children:"Save API Keys"}),(0,a.jsx)(l.$,{onClick:P,variant:"outline",className:"btn-outline-neo",children:"Test Connection"})]})]})]})}),(0,a.jsx)(n.av,{value:"telegram",className:"space-y-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary",children:"Telegram Bot Integration"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"telegramToken",children:"Telegram Bot Token"}),(0,a.jsx)(o.p,{id:"telegramToken",placeholder:"Enter your Telegram Bot Token",type:"password",value:A,onChange:e=>T(e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"telegramChatId",children:"Telegram Chat ID"}),(0,a.jsx)(o.p,{id:"telegramChatId",placeholder:"Enter your Telegram Chat ID",type:"text",value:E,onChange:e=>I(e.target.value)})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(l.$,{onClick:()=>{try{localStorage.setItem("telegram_bot_token",A),localStorage.setItem("telegram_chat_id",E),console.log("Telegram Config Saved:",{telegramToken:A.substring(0,10)+"...",telegramChatId:E}),B({title:"Telegram Config Saved",description:"Telegram settings have been saved successfully."})}catch(e){B({title:"Error",description:"Failed to save Telegram configuration.",variant:"destructive"})}},className:"btn-neo flex-1",children:"Save Telegram Config"}),(0,a.jsx)(l.$,{onClick:F,variant:"outline",className:"btn-outline-neo flex-1",children:"Test Telegram"})]})]}),(0,a.jsxs)("div",{className:"bg-muted p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Setup Guide:"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-sm",children:[(0,a.jsx)("li",{children:"Create a new bot by messaging @BotFather on Telegram"}),(0,a.jsx)("li",{children:"Use the command /newbot and follow the instructions"}),(0,a.jsx)("li",{children:"Copy the bot token provided by BotFather"}),(0,a.jsx)("li",{children:"Start a chat with your bot and send any message"}),(0,a.jsx)("li",{children:"Get your chat ID by visiting: https://api.telegram.org/bot[YOUR_BOT_TOKEN]/getUpdates"}),(0,a.jsx)("li",{children:"Enter both the bot token and chat ID above, then test the connection"})]})]})]})}),(0,a.jsx)(n.av,{value:"sessionManager",className:"space-y-6",children:(0,a.jsx)(R,{})})]})})]})})}},17313:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>l});var a=t(95155),r=t(12115),i=t(30064),n=t(59434);let l=i.bL,o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(i.B8,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...r})});o.displayName=i.B8.displayName;let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(i.l9,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...r})});c.displayName=i.l9.displayName;let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(i.UC,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...r})});d.displayName=i.UC.displayName},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(95155);t(12115);var r=t(74466),i=t(59434);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:t}),s),...r})}},30285:(e,s,t)=>{"use strict";t.d(s,{$:()=>c});var a=t(95155),r=t(12115),i=t(99708),n=t(74466),l=t(59434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-sm text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-2 border-transparent",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-2 border-transparent",outline:"border-2 border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-2 border-transparent",ghost:"hover:bg-accent hover:text-accent-foreground border-2 border-transparent",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-sm px-3",lg:"h-11 rounded-sm px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,s)=>{let{className:t,variant:r,size:n,asChild:c=!1,...d}=e,m=c?i.DX:"button";return(0,a.jsx)(m,{className:(0,l.cn)(o({variant:r,size:n,className:t})),ref:s,...d})});c.displayName="Button"},62523:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var a=t(95155),r=t(12115),i=t(59434);let n=r.forwardRef((e,s)=>{let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:s,...n})});n.displayName="Input"},66424:(e,s,t)=>{"use strict";t.d(s,{$:()=>o,F:()=>l});var a=t(95155),r=t(12115),i=t(47655),n=t(59434);let l=r.forwardRef((e,s)=>{let{className:t,children:r,...l}=e;return(0,a.jsxs)(i.bL,{ref:s,className:(0,n.cn)("relative overflow-hidden",t),...l,children:[(0,a.jsx)(i.LM,{className:"h-full w-full rounded-[inherit]",children:r}),(0,a.jsx)(o,{}),(0,a.jsx)(i.OK,{})]})});l.displayName=i.bL.displayName;let o=r.forwardRef((e,s)=>{let{className:t,orientation:r="vertical",...l}=e;return(0,a.jsx)(i.VM,{ref:s,orientation:r,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",t),...l,children:(0,a.jsx)(i.lr,{className:"relative flex-1 rounded-full bg-border"})})});o.displayName=i.VM.displayName},66695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>l});var a=t(95155),r=t(12115),i=t(59434);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("rounded-sm border-2 border-border bg-card text-card-foreground",t),...r})});n.displayName="Card";let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-4 md:p-6",t),...r})});l.displayName="CardHeader";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,i.cn)("text-xl font-semibold leading-none tracking-tight",t),...r})});o.displayName="CardTitle";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",t),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("p-4 md:p-6 pt-0",t),...r})});d.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-4 md:p-6 pt-0",t),...r})}).displayName="CardFooter"},85057:(e,s,t)=>{"use strict";t.d(s,{J:()=>c});var a=t(95155),r=t(12115),i=t(40968),n=t(74466),l=t(59434);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(i.b,{ref:s,className:(0,l.cn)(o(),t),...r})});c.displayName=i.b.displayName}},e=>{var s=s=>e(e.s=s);e.O(0,[823,98,655,654,318,441,684,358],()=>s(12136)),_N_E=e.O()}]);