"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/genkit";
exports.ids = ["vendor-chunks/genkit"];
exports.modules = {

/***/ "(action-browser)/./node_modules/genkit/lib/common.js":
/*!*******************************************!*\
  !*** ./node_modules/genkit/lib/common.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar common_exports = {};\n__export(common_exports, {\n  BaseDataPointSchema: () => import_ai.BaseDataPointSchema,\n  Chat: () => import_chat.Chat,\n  Document: () => import_ai.Document,\n  DocumentDataSchema: () => import_ai.DocumentDataSchema,\n  GENKIT_CLIENT_HEADER: () => import_core.GENKIT_CLIENT_HEADER,\n  GENKIT_VERSION: () => import_core.GENKIT_VERSION,\n  GenerationBlockedError: () => import_ai.GenerationBlockedError,\n  GenerationCommonConfigSchema: () => import_ai.GenerationCommonConfigSchema,\n  GenerationResponseError: () => import_ai.GenerationResponseError,\n  GenkitError: () => import_core.GenkitError,\n  LlmResponseSchema: () => import_ai.LlmResponseSchema,\n  LlmStatsSchema: () => import_ai.LlmStatsSchema,\n  Message: () => import_ai.Message,\n  MessageSchema: () => import_ai.MessageSchema,\n  ModelRequestSchema: () => import_ai.ModelRequestSchema,\n  ModelResponseSchema: () => import_ai.ModelResponseSchema,\n  OperationSchema: () => import_core.OperationSchema,\n  PartSchema: () => import_ai.PartSchema,\n  ReflectionServer: () => import_core.ReflectionServer,\n  RoleSchema: () => import_ai.RoleSchema,\n  Session: () => import_session.Session,\n  StatusCodes: () => import_core.StatusCodes,\n  StatusSchema: () => import_core.StatusSchema,\n  ToolCallSchema: () => import_ai.ToolCallSchema,\n  ToolInterruptError: () => import_ai.ToolInterruptError,\n  ToolSchema: () => import_ai.ToolSchema,\n  UserFacingError: () => import_core.UserFacingError,\n  defineJsonSchema: () => import_core.defineJsonSchema,\n  defineSchema: () => import_core.defineSchema,\n  dynamicTool: () => import_tool.dynamicTool,\n  embedderActionMetadata: () => import_ai.embedderActionMetadata,\n  embedderRef: () => import_ai.embedderRef,\n  evaluatorRef: () => import_ai.evaluatorRef,\n  getCurrentEnv: () => import_core.getCurrentEnv,\n  getStreamingCallback: () => import_core.getStreamingCallback,\n  indexerRef: () => import_ai.indexerRef,\n  isDevEnv: () => import_core.isDevEnv,\n  modelActionMetadata: () => import_ai.modelActionMetadata,\n  modelRef: () => import_ai.modelRef,\n  rerankerRef: () => import_ai.rerankerRef,\n  retrieverRef: () => import_ai.retrieverRef,\n  runWithStreamingCallback: () => import_core.runWithStreamingCallback,\n  z: () => import_core.z\n});\nmodule.exports = __toCommonJS(common_exports);\nvar import_ai = __webpack_require__(/*! @genkit-ai/ai */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/index.js\");\nvar import_chat = __webpack_require__(/*! @genkit-ai/ai/chat */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/chat.js\");\nvar import_session = __webpack_require__(/*! @genkit-ai/ai/session */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/session.js\");\nvar import_tool = __webpack_require__(/*! @genkit-ai/ai/tool */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/tool.js\");\nvar import_core = __webpack_require__(/*! @genkit-ai/core */ \"(action-browser)/./node_modules/@genkit-ai/core/lib/index.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/common.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/embedder.js":
/*!*********************************************!*\
  !*** ./node_modules/genkit/lib/embedder.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar embedder_exports = {};\n__export(embedder_exports, {\n  EmbedderInfoSchema: () => import_embedder.EmbedderInfoSchema,\n  embedderRef: () => import_embedder.embedderRef\n});\nmodule.exports = __toCommonJS(embedder_exports);\nvar import_embedder = __webpack_require__(/*! @genkit-ai/ai/embedder */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/embedder.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=embedder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/embedder.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/genkit.js":
/*!*******************************************!*\
  !*** ./node_modules/genkit/lib/genkit.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar genkit_exports = {};\n__export(genkit_exports, {\n  Genkit: () => Genkit,\n  __disableReflectionApi: () => __disableReflectionApi,\n  genkit: () => genkit\n});\nmodule.exports = __toCommonJS(genkit_exports);\nvar import_ai = __webpack_require__(/*! @genkit-ai/ai */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/index.js\");\nvar import_embedder = __webpack_require__(/*! @genkit-ai/ai/embedder */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/embedder.js\");\nvar import_evaluator = __webpack_require__(/*! @genkit-ai/ai/evaluator */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/evaluator.js\");\nvar import_formats = __webpack_require__(/*! @genkit-ai/ai/formats */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/formats/index.js\");\nvar import_model = __webpack_require__(/*! @genkit-ai/ai/model */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/model.js\");\nvar import_reranker = __webpack_require__(/*! @genkit-ai/ai/reranker */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/reranker.js\");\nvar import_retriever = __webpack_require__(/*! @genkit-ai/ai/retriever */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/retriever.js\");\nvar import_tool = __webpack_require__(/*! @genkit-ai/ai/tool */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/tool.js\");\nvar import_core = __webpack_require__(/*! @genkit-ai/core */ \"(action-browser)/./node_modules/@genkit-ai/core/lib/index.js\");\nvar import_async = __webpack_require__(/*! @genkit-ai/core/async */ \"(action-browser)/./node_modules/@genkit-ai/core/lib/async.js\");\nvar import_logging = __webpack_require__(/*! ./logging.js */ \"(action-browser)/./node_modules/genkit/lib/logging.js\");\nvar import_registry = __webpack_require__(/*! ./registry.js */ \"(action-browser)/./node_modules/genkit/lib/registry.js\");\nvar import_tracing = __webpack_require__(/*! ./tracing.js */ \"(action-browser)/./node_modules/genkit/lib/tracing.js\");\nclass Genkit {\n  /** Developer-configured options. */\n  options;\n  /** Registry instance that is exclusively modified by this Genkit instance. */\n  registry;\n  /** Reflection server for this registry. May be null if not started. */\n  reflectionServer = null;\n  /** List of flows that have been registered in this instance. */\n  flows = [];\n  get apiStability() {\n    return this.registry.apiStability;\n  }\n  constructor(options) {\n    this.options = options || {};\n    this.registry = new import_registry.Registry();\n    if (this.options.context) {\n      this.registry.context = this.options.context;\n    }\n    this.configure();\n    if ((0, import_core.isDevEnv)() && !disableReflectionApi) {\n      this.reflectionServer = new import_core.ReflectionServer(this.registry, {\n        configuredEnvs: [\"dev\"]\n      });\n      this.reflectionServer.start().catch((e) => import_logging.logger.error);\n    }\n  }\n  /**\n   * Defines and registers a flow function.\n   */\n  defineFlow(config, fn) {\n    const flow = (0, import_core.defineFlow)(this.registry, config, fn);\n    this.flows.push(flow);\n    return flow;\n  }\n  /**\n   * Defines and registers a tool.\n   *\n   * Tools can be passed to models by name or value during `generate` calls to be called automatically based on the prompt and situation.\n   */\n  defineTool(config, fn) {\n    return (0, import_ai.defineTool)(this.registry, config, fn);\n  }\n  /**\n   * Defines a dynamic tool. Dynamic tools are just like regular tools ({@link Genkit.defineTool}) but will not be registered in the\n   * Genkit registry and can be defined dynamically at runtime.\n   */\n  dynamicTool(config, fn) {\n    return (0, import_tool.dynamicTool)(config, fn).attach(this.registry);\n  }\n  /**\n   * Defines and registers a schema from a Zod schema.\n   *\n   * Defined schemas can be referenced by `name` in prompts in place of inline schemas.\n   */\n  defineSchema(name, schema) {\n    return (0, import_core.defineSchema)(this.registry, name, schema);\n  }\n  /**\n   * Defines and registers a schema from a JSON schema.\n   *\n   * Defined schemas can be referenced by `name` in prompts in place of inline schemas.\n   */\n  defineJsonSchema(name, jsonSchema) {\n    return (0, import_core.defineJsonSchema)(this.registry, name, jsonSchema);\n  }\n  /**\n   * Defines a new model and adds it to the registry.\n   */\n  defineModel(options, runner) {\n    return (0, import_model.defineModel)(this.registry, options, runner);\n  }\n  /**\n   * Defines a new background model and adds it to the registry.\n   */\n  defineBackgroundModel(options) {\n    return (0, import_model.defineBackgroundModel)(this.registry, options);\n  }\n  /**\n   * Looks up a prompt by `name` (and optionally `variant`). Can be used to lookup\n   * .prompt files or prompts previously defined with {@link Genkit.definePrompt}\n   */\n  prompt(name, options) {\n    return this.wrapExecutablePromptPromise(\n      `${name}${options?.variant ? `.${options?.variant}` : \"\"}`,\n      (0, import_ai.prompt)(this.registry, name, {\n        ...options,\n        dir: this.options.promptDir ?? \"./prompts\"\n      })\n    );\n  }\n  wrapExecutablePromptPromise(name, promise) {\n    const executablePrompt = async (input, opts) => {\n      return (await promise)(input, opts);\n    };\n    executablePrompt.render = async (input, opts) => {\n      return (await promise).render(input, opts);\n    };\n    executablePrompt.stream = (input, opts) => {\n      let channel = new import_async.Channel();\n      const generated = (0, import_tracing.runInNewSpan)(\n        this.registry,\n        {\n          metadata: {\n            name,\n            input\n          },\n          labels: {\n            [import_tracing.SPAN_TYPE_ATTR]: \"dotprompt\"\n          }\n        },\n        () => (0, import_ai.generate)(\n          this.registry,\n          promise.then(\n            (action) => action.render(input, {\n              ...opts,\n              onChunk: (chunk) => channel.send(chunk)\n            })\n          )\n        )\n      );\n      generated.then(\n        () => channel.close(),\n        (err) => channel.error(err)\n      );\n      return {\n        response: generated,\n        stream: channel\n      };\n    };\n    executablePrompt.asTool = async () => {\n      return (await promise).asTool();\n    };\n    return executablePrompt;\n  }\n  /**\n   * Defines and registers a prompt based on a function.\n   *\n   * This is an alternative to defining and importing a .prompt file, providing\n   * the most advanced control over how the final request to the model is made.\n   *\n   * @param options - Prompt metadata including model, model params,\n   * input/output schemas, etc\n   * @param fn - A function that returns a {@link GenerateRequest}. Any config\n   * parameters specified by the {@link GenerateRequest} will take precedence\n   * over any parameters specified by `options`.\n   *\n   * ```ts\n   * const hi = ai.definePrompt(\n   *   {\n   *     name: 'hi',\n   *     input: {\n   *       schema: z.object({\n   *         name: z.string(),\n   *       }),\n   *     },\n   *     config: {\n   *       temperature: 1,\n   *     },\n   *   },\n   *   async (input) => {\n   *     return {\n   *       messages: [ { role: 'user', content: [{ text: `hi ${input.name}` }] } ],\n   *     };\n   *   }\n   * );\n   * const { text } = await hi({ name: 'Genkit' });\n   * ```\n   */\n  definePrompt(options, templateOrFn) {\n    if (templateOrFn) {\n      if (options.messages) {\n        throw new import_core.GenkitError({\n          status: \"INVALID_ARGUMENT\",\n          message: \"Cannot specify template/function argument and `options.messages` at the same time\"\n        });\n      }\n      if (typeof templateOrFn === \"string\") {\n        return (0, import_ai.definePrompt)(this.registry, {\n          ...options,\n          messages: templateOrFn\n        });\n      } else {\n        return (0, import_ai.definePrompt)(this.registry, {\n          ...options,\n          messages: async (input) => {\n            const response = await templateOrFn(input);\n            return response.messages;\n          }\n        });\n      }\n    }\n    return (0, import_ai.definePrompt)(this.registry, options);\n  }\n  /**\n   * Creates a retriever action for the provided {@link RetrieverFn} implementation.\n   */\n  defineRetriever(options, runner) {\n    return (0, import_retriever.defineRetriever)(this.registry, options, runner);\n  }\n  /**\n   * defineSimpleRetriever makes it easy to map existing data into documents that\n   * can be used for prompt augmentation.\n   *\n   * @param options Configuration options for the retriever.\n   * @param handler A function that queries a datastore and returns items from which to extract documents.\n   * @returns A Genkit retriever.\n   */\n  defineSimpleRetriever(options, handler) {\n    return (0, import_retriever.defineSimpleRetriever)(this.registry, options, handler);\n  }\n  /**\n   * Creates an indexer action for the provided {@link IndexerFn} implementation.\n   */\n  defineIndexer(options, runner) {\n    return (0, import_retriever.defineIndexer)(this.registry, options, runner);\n  }\n  /**\n   * Creates evaluator action for the provided {@link EvaluatorFn} implementation.\n   */\n  defineEvaluator(options, runner) {\n    return (0, import_evaluator.defineEvaluator)(this.registry, options, runner);\n  }\n  /**\n   * Creates embedder model for the provided {@link EmbedderFn} model implementation.\n   */\n  defineEmbedder(options, runner) {\n    return (0, import_embedder.defineEmbedder)(this.registry, options, runner);\n  }\n  /**\n   * create a handlebards helper (https://handlebarsjs.com/guide/block-helpers.html) to be used in dotpormpt templates.\n   */\n  defineHelper(name, fn) {\n    (0, import_ai.defineHelper)(this.registry, name, fn);\n  }\n  /**\n   * Creates a handlebars partial (https://handlebarsjs.com/guide/partials.html) to be used in dotpormpt templates.\n   */\n  definePartial(name, source) {\n    (0, import_ai.definePartial)(this.registry, name, source);\n  }\n  /**\n   *  Creates a reranker action for the provided {@link RerankerFn} implementation.\n   */\n  defineReranker(options, runner) {\n    return (0, import_reranker.defineReranker)(this.registry, options, runner);\n  }\n  /**\n   * Embeds the given `content` using the specified `embedder`.\n   */\n  embed(params) {\n    return (0, import_ai.embed)(this.registry, params);\n  }\n  /**\n   * A veneer for interacting with embedder models in bulk.\n   */\n  embedMany(params) {\n    return (0, import_embedder.embedMany)(this.registry, params);\n  }\n  /**\n   * Evaluates the given `dataset` using the specified `evaluator`.\n   */\n  evaluate(params) {\n    return (0, import_ai.evaluate)(this.registry, params);\n  }\n  /**\n   * Reranks documents from a {@link RerankerArgument} based on the provided query.\n   */\n  rerank(params) {\n    return (0, import_ai.rerank)(this.registry, params);\n  }\n  /**\n   * Indexes `documents` using the provided `indexer`.\n   */\n  index(params) {\n    return (0, import_retriever.index)(this.registry, params);\n  }\n  /**\n   * Retrieves documents from the `retriever` based on the provided `query`.\n   */\n  retrieve(params) {\n    return (0, import_ai.retrieve)(this.registry, params);\n  }\n  async generate(options) {\n    let resolvedOptions;\n    if (options instanceof Promise) {\n      resolvedOptions = await options;\n    } else if (typeof options === \"string\" || Array.isArray(options)) {\n      resolvedOptions = {\n        prompt: options\n      };\n    } else {\n      resolvedOptions = options;\n    }\n    return (0, import_ai.generate)(this.registry, resolvedOptions);\n  }\n  generateStream(options) {\n    if (typeof options === \"string\" || Array.isArray(options)) {\n      options = { prompt: options };\n    }\n    return (0, import_ai.generateStream)(this.registry, options);\n  }\n  /**\n   * Checks the status of of a given operation. Returns a new operation which will contain the updated status.\n   *\n   * ```ts\n   * let operation = await ai.generateOperation({\n   *   model: googleAI.model('veo-2.0-generate-001'),\n   *   prompt: 'A banana riding a bicycle.',\n   * });\n   *\n   * while (!operation.done) {\n   *   operation = await ai.checkOperation(operation!);\n   *   await new Promise((resolve) => setTimeout(resolve, 5000));\n   * }\n   * ```\n   *\n   * @param operation\n   * @returns\n   */\n  checkOperation(operation) {\n    return (0, import_ai.checkOperation)(this.registry, operation);\n  }\n  run(name, funcOrInput, maybeFunc) {\n    if (maybeFunc) {\n      return (0, import_core.run)(name, funcOrInput, maybeFunc, this.registry);\n    }\n    return (0, import_core.run)(name, funcOrInput, this.registry);\n  }\n  /**\n   * Returns current action (or flow) invocation context. Can be used to access things like auth\n   * data set by HTTP server frameworks. If invoked outside of an action (e.g. flow or tool) will\n   * return `undefined`.\n   */\n  currentContext() {\n    return (0, import_core.getContext)(this);\n  }\n  /**\n   * Configures the Genkit instance.\n   */\n  configure() {\n    const activeRegistry = this.registry;\n    (0, import_model.defineGenerateAction)(activeRegistry);\n    (0, import_formats.configureFormats)(activeRegistry);\n    const plugins = [...this.options.plugins ?? []];\n    if (this.options.model) {\n      this.registry.registerValue(\n        \"defaultModel\",\n        \"defaultModel\",\n        this.options.model\n      );\n    }\n    if (this.options.promptDir !== null) {\n      (0, import_ai.loadPromptFolder)(\n        this.registry,\n        this.options.promptDir ?? \"./prompts\",\n        \"\"\n      );\n    }\n    plugins.forEach((plugin) => {\n      const loadedPlugin = plugin(this);\n      import_logging.logger.debug(`Registering plugin ${loadedPlugin.name}...`);\n      activeRegistry.registerPluginProvider(loadedPlugin.name, {\n        name: loadedPlugin.name,\n        async initializer() {\n          import_logging.logger.debug(`Initializing plugin ${loadedPlugin.name}:`);\n          await loadedPlugin.initializer();\n        },\n        async resolver(action, target) {\n          if (loadedPlugin.resolver) {\n            await loadedPlugin.resolver(action, target);\n          }\n        },\n        async listActions() {\n          if (loadedPlugin.listActions) {\n            return await loadedPlugin.listActions();\n          }\n          return [];\n        }\n      });\n    });\n  }\n  /**\n   * Stops all servers.\n   */\n  async stopServers() {\n    await this.reflectionServer?.stop();\n    this.reflectionServer = null;\n  }\n}\nfunction genkit(options) {\n  return new Genkit(options);\n}\nconst shutdown = async () => {\n  import_logging.logger.info(\"Shutting down all Genkit servers...\");\n  await import_core.ReflectionServer.stopAll();\n  process.exit(0);\n};\nprocess.on(\"SIGTERM\", shutdown);\nprocess.on(\"SIGINT\", shutdown);\nlet disableReflectionApi = false;\nfunction __disableReflectionApi() {\n  disableReflectionApi = true;\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=genkit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/genkit.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/index.js":
/*!******************************************!*\
  !*** ./node_modules/genkit/lib/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __reExport = (target, mod, secondTarget) => (__copyProps(target, mod, \"default\"), secondTarget && __copyProps(secondTarget, mod, \"default\"));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar index_exports = {};\n__export(index_exports, {\n  Genkit: () => import_genkit.Genkit,\n  genkit: () => import_genkit.genkit\n});\nmodule.exports = __toCommonJS(index_exports);\n__reExport(index_exports, __webpack_require__(/*! ./common.js */ \"(action-browser)/./node_modules/genkit/lib/common.js\"), module.exports);\nvar import_genkit = __webpack_require__(/*! ./genkit.js */ \"(action-browser)/./node_modules/genkit/lib/genkit.js\");\n/**\n * @license\n *\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/index.mjs":
/*!*******************************************!*\
  !*** ./node_modules/genkit/lib/index.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Genkit: () => (/* reexport safe */ _genkit_js__WEBPACK_IMPORTED_MODULE_1__.Genkit),\n/* harmony export */   genkit: () => (/* reexport safe */ _genkit_js__WEBPACK_IMPORTED_MODULE_1__.genkit)\n/* harmony export */ });\n/* harmony import */ var _common_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./common.js */ \"(action-browser)/./node_modules/genkit/lib/common.js\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _common_js__WEBPACK_IMPORTED_MODULE_0__) if([\"default\",\"Genkit\",\"genkit\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _common_js__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _genkit_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./genkit.js */ \"(action-browser)/./node_modules/genkit/lib/genkit.js\");\n/**\n * @license\n *\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9nZW5raXQvbGliL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUM0QjtBQUNpQjtBQUkzQztBQUNGIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxnZW5raXRcXGxpYlxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqXG4gKiBDb3B5cmlnaHQgMjAyNSBHb29nbGUgTExDXG4gKlxuICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAqIHlvdSBtYXkgbm90IHVzZSB0aGlzIGZpbGUgZXhjZXB0IGluIGNvbXBsaWFuY2Ugd2l0aCB0aGUgTGljZW5zZS5cbiAqIFlvdSBtYXkgb2J0YWluIGEgY29weSBvZiB0aGUgTGljZW5zZSBhdFxuICpcbiAqICAgICBodHRwOi8vd3d3LmFwYWNoZS5vcmcvbGljZW5zZXMvTElDRU5TRS0yLjBcbiAqXG4gKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gKiBkaXN0cmlidXRlZCB1bmRlciB0aGUgTGljZW5zZSBpcyBkaXN0cmlidXRlZCBvbiBhbiBcIkFTIElTXCIgQkFTSVMsXG4gKiBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgQ09ORElUSU9OUyBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC5cbiAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAqIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovXG5leHBvcnQgKiBmcm9tIFwiLi9jb21tb24uanNcIjtcbmltcG9ydCB7IEdlbmtpdCwgZ2Vua2l0IH0gZnJvbSBcIi4vZ2Vua2l0LmpzXCI7XG5leHBvcnQge1xuICBHZW5raXQsXG4gIGdlbmtpdFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/logging.js":
/*!********************************************!*\
  !*** ./node_modules/genkit/lib/logging.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar logging_exports = {};\n__export(logging_exports, {\n  logger: () => import_logging.logger\n});\nmodule.exports = __toCommonJS(logging_exports);\nvar import_logging = __webpack_require__(/*! @genkit-ai/core/logging */ \"(action-browser)/./node_modules/@genkit-ai/core/lib/logging.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=logging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/logging.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/logging.mjs":
/*!*********************************************!*\
  !*** ./node_modules/genkit/lib/logging.mjs ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logger: () => (/* reexport safe */ _genkit_ai_core_logging__WEBPACK_IMPORTED_MODULE_0__.logger)\n/* harmony export */ });\n/* harmony import */ var _genkit_ai_core_logging__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @genkit-ai/core/logging */ \"(action-browser)/./node_modules/@genkit-ai/core/lib/logging.mjs\");\n\n\n//# sourceMappingURL=logging.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9nZW5raXQvbGliL2xvZ2dpbmcubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWlEO0FBRy9DO0FBQ0YiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGdlbmtpdFxcbGliXFxsb2dnaW5nLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBsb2dnZXIgfSBmcm9tIFwiQGdlbmtpdC1haS9jb3JlL2xvZ2dpbmdcIjtcbmV4cG9ydCB7XG4gIGxvZ2dlclxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWxvZ2dpbmcubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/logging.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/middleware.js":
/*!***********************************************!*\
  !*** ./node_modules/genkit/lib/middleware.js ***!
  \***********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar middleware_exports = {};\n__export(middleware_exports, {\n  augmentWithContext: () => import_middleware.augmentWithContext,\n  downloadRequestMedia: () => import_middleware.downloadRequestMedia,\n  simulateSystemPrompt: () => import_middleware.simulateSystemPrompt,\n  validateSupport: () => import_middleware.validateSupport\n});\nmodule.exports = __toCommonJS(middleware_exports);\nvar import_middleware = __webpack_require__(/*! @genkit-ai/ai/model/middleware */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/model/middleware.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=middleware.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/middleware.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/model.js":
/*!******************************************!*\
  !*** ./node_modules/genkit/lib/model.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar model_exports = {};\n__export(model_exports, {\n  CandidateErrorSchema: () => import_model.CandidateErrorSchema,\n  CandidateSchema: () => import_model.CandidateSchema,\n  CustomPartSchema: () => import_model.CustomPartSchema,\n  DataPartSchema: () => import_model.DataPartSchema,\n  GenerateRequestSchema: () => import_model.GenerateRequestSchema,\n  GenerateResponseChunkSchema: () => import_model.GenerateResponseChunkSchema,\n  GenerateResponseSchema: () => import_model.GenerateResponseSchema,\n  GenerationCommonConfigDescriptions: () => import_model.GenerationCommonConfigDescriptions,\n  GenerationCommonConfigSchema: () => import_model.GenerationCommonConfigSchema,\n  GenerationUsageSchema: () => import_model.GenerationUsageSchema,\n  MediaPartSchema: () => import_model.MediaPartSchema,\n  MessageSchema: () => import_model.MessageSchema,\n  ModelInfoSchema: () => import_model.ModelInfoSchema,\n  ModelRequestSchema: () => import_model.ModelRequestSchema,\n  ModelResponseSchema: () => import_model.ModelResponseSchema,\n  PartSchema: () => import_model.PartSchema,\n  RoleSchema: () => import_model.RoleSchema,\n  TextPartSchema: () => import_model.TextPartSchema,\n  ToolDefinitionSchema: () => import_model.ToolDefinitionSchema,\n  ToolRequestPartSchema: () => import_model.ToolRequestPartSchema,\n  ToolResponsePartSchema: () => import_model.ToolResponsePartSchema,\n  getBasicUsageStats: () => import_model.getBasicUsageStats,\n  modelRef: () => import_model.modelRef,\n  simulateConstrainedGeneration: () => import_model.simulateConstrainedGeneration\n});\nmodule.exports = __toCommonJS(model_exports);\nvar import_model = __webpack_require__(/*! @genkit-ai/ai/model */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/model.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=model.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/model.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/model.mjs":
/*!*******************************************!*\
  !*** ./node_modules/genkit/lib/model.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CandidateErrorSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.CandidateErrorSchema),\n/* harmony export */   CandidateSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.CandidateSchema),\n/* harmony export */   CustomPartSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.CustomPartSchema),\n/* harmony export */   DataPartSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.DataPartSchema),\n/* harmony export */   GenerateRequestSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.GenerateRequestSchema),\n/* harmony export */   GenerateResponseChunkSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.GenerateResponseChunkSchema),\n/* harmony export */   GenerateResponseSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.GenerateResponseSchema),\n/* harmony export */   GenerationCommonConfigDescriptions: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.GenerationCommonConfigDescriptions),\n/* harmony export */   GenerationCommonConfigSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.GenerationCommonConfigSchema),\n/* harmony export */   GenerationUsageSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.GenerationUsageSchema),\n/* harmony export */   MediaPartSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.MediaPartSchema),\n/* harmony export */   MessageSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.MessageSchema),\n/* harmony export */   ModelInfoSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.ModelInfoSchema),\n/* harmony export */   ModelRequestSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.ModelRequestSchema),\n/* harmony export */   ModelResponseSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.ModelResponseSchema),\n/* harmony export */   PartSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.PartSchema),\n/* harmony export */   RoleSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.RoleSchema),\n/* harmony export */   TextPartSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.TextPartSchema),\n/* harmony export */   ToolDefinitionSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.ToolDefinitionSchema),\n/* harmony export */   ToolRequestPartSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.ToolRequestPartSchema),\n/* harmony export */   ToolResponsePartSchema: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.ToolResponsePartSchema),\n/* harmony export */   getBasicUsageStats: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.getBasicUsageStats),\n/* harmony export */   modelRef: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.modelRef),\n/* harmony export */   simulateConstrainedGeneration: () => (/* reexport safe */ _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__.simulateConstrainedGeneration)\n/* harmony export */ });\n/* harmony import */ var _genkit_ai_ai_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @genkit-ai/ai/model */ \"(action-browser)/./node_modules/@genkit-ai/ai/lib/model.mjs\");\n\n\n//# sourceMappingURL=model.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/model.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/plugin.mjs":
/*!********************************************!*\
  !*** ./node_modules/genkit/lib/plugin.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genkitPlugin: () => (/* binding */ genkitPlugin)\n/* harmony export */ });\nfunction genkitPlugin(pluginName, initFn, resolveFn, listActionsFn) {\n  return (genkit) => ({\n    name: pluginName,\n    initializer: async () => {\n      await initFn(genkit);\n    },\n    resolver: async (action, target) => {\n      if (resolveFn) {\n        return await resolveFn(genkit, action, target);\n      }\n    },\n    listActions: async () => {\n      if (listActionsFn) {\n        return await listActionsFn();\n      }\n      return [];\n    }\n  });\n}\n\n//# sourceMappingURL=plugin.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9nZW5raXQvbGliL3BsdWdpbi5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZ2Vua2l0XFxsaWJcXHBsdWdpbi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZ2Vua2l0UGx1Z2luKHBsdWdpbk5hbWUsIGluaXRGbiwgcmVzb2x2ZUZuLCBsaXN0QWN0aW9uc0ZuKSB7XG4gIHJldHVybiAoZ2Vua2l0KSA9PiAoe1xuICAgIG5hbWU6IHBsdWdpbk5hbWUsXG4gICAgaW5pdGlhbGl6ZXI6IGFzeW5jICgpID0+IHtcbiAgICAgIGF3YWl0IGluaXRGbihnZW5raXQpO1xuICAgIH0sXG4gICAgcmVzb2x2ZXI6IGFzeW5jIChhY3Rpb24sIHRhcmdldCkgPT4ge1xuICAgICAgaWYgKHJlc29sdmVGbikge1xuICAgICAgICByZXR1cm4gYXdhaXQgcmVzb2x2ZUZuKGdlbmtpdCwgYWN0aW9uLCB0YXJnZXQpO1xuICAgICAgfVxuICAgIH0sXG4gICAgbGlzdEFjdGlvbnM6IGFzeW5jICgpID0+IHtcbiAgICAgIGlmIChsaXN0QWN0aW9uc0ZuKSB7XG4gICAgICAgIHJldHVybiBhd2FpdCBsaXN0QWN0aW9uc0ZuKCk7XG4gICAgICB9XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9KTtcbn1cbmV4cG9ydCB7XG4gIGdlbmtpdFBsdWdpblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBsdWdpbi5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/plugin.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/registry.js":
/*!*********************************************!*\
  !*** ./node_modules/genkit/lib/registry.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar registry_exports = {};\n__export(registry_exports, {\n  Registry: () => import_registry.Registry\n});\nmodule.exports = __toCommonJS(registry_exports);\nvar import_registry = __webpack_require__(/*! @genkit-ai/core/registry */ \"(action-browser)/./node_modules/@genkit-ai/core/lib/registry.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=registry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/registry.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/genkit/lib/tracing.js":
/*!********************************************!*\
  !*** ./node_modules/genkit/lib/tracing.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\nvar tracing_exports = {};\n__export(tracing_exports, {\n  SPAN_TYPE_ATTR: () => import_tracing.SPAN_TYPE_ATTR,\n  SpanContextSchema: () => import_tracing.SpanContextSchema,\n  SpanDataSchema: () => import_tracing.SpanDataSchema,\n  SpanMetadataSchema: () => import_tracing.SpanMetadataSchema,\n  SpanStatusSchema: () => import_tracing.SpanStatusSchema,\n  TimeEventSchema: () => import_tracing.TimeEventSchema,\n  TraceDataSchema: () => import_tracing.TraceDataSchema,\n  TraceMetadataSchema: () => import_tracing.TraceMetadataSchema,\n  TraceServerExporter: () => import_tracing.TraceServerExporter,\n  appendSpan: () => import_tracing.appendSpan,\n  enableTelemetry: () => import_tracing.enableTelemetry,\n  flushTracing: () => import_tracing.flushTracing,\n  newTrace: () => import_tracing.newTrace,\n  runInNewSpan: () => import_tracing.runInNewSpan,\n  setCustomMetadataAttribute: () => import_tracing.setCustomMetadataAttribute,\n  setCustomMetadataAttributes: () => import_tracing.setCustomMetadataAttributes,\n  setTelemetryServerUrl: () => import_tracing.setTelemetryServerUrl,\n  toDisplayPath: () => import_tracing.toDisplayPath\n});\nmodule.exports = __toCommonJS(tracing_exports);\nvar import_tracing = __webpack_require__(/*! @genkit-ai/core/tracing */ \"(action-browser)/./node_modules/@genkit-ai/core/lib/tracing.js\");\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceMappingURL=tracing.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/genkit/lib/tracing.js\n");

/***/ })

};
;