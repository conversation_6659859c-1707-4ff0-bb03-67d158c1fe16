"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPanelPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _components_admin_SessionManager__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/admin/SessionManager */ \"(app-pages-browser)/./src/components/admin/SessionManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPanelPage() {\n    _s();\n    const { appSettings, dispatch, botSystemStatus } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_9__.useTradingContext)();\n    const [localSettings, setLocalSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(appSettings);\n    const [apiKey, setApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA');\n    const [apiSecret, setApiSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp');\n    const [showApiKey, setShowApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showApiSecret, setShowApiSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [telegramToken, setTelegramToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [telegramChatId, setTelegramChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanelPage.useEffect\": ()=>{\n            setLocalSettings(appSettings);\n        }\n    }[\"AdminPanelPage.useEffect\"], [\n        appSettings\n    ]);\n    const handleSettingsChange = (key, value)=>{\n        setLocalSettings((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const handleSaveAppSettings = ()=>{\n        dispatch({\n            type: 'SET_APP_SETTINGS',\n            payload: localSettings\n        });\n        toast({\n            title: \"App Settings Saved\",\n            description: \"Global application settings have been updated.\"\n        });\n    };\n    const handleSaveApiKeys = async ()=>{\n        try {\n            // Store API keys securely (in a real implementation, these would be encrypted)\n            localStorage.setItem('binance_api_key', apiKey);\n            localStorage.setItem('binance_api_secret', apiSecret);\n            console.log(\"API Keys Saved:\", {\n                apiKey: apiKey.substring(0, 10) + '...',\n                apiSecret: apiSecret.substring(0, 10) + '...'\n            });\n            toast({\n                title: \"API Keys Saved\",\n                description: \"Binance API keys have been saved securely.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save API keys.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleTestApiConnection = async ()=>{\n        try {\n            // Test connection to Binance API\n            const response = await fetch('https://api.binance.com/api/v3/ping');\n            if (response.ok) {\n                toast({\n                    title: \"API Connection Test\",\n                    description: \"Successfully connected to Binance API!\"\n                });\n            } else {\n                toast({\n                    title: \"Connection Failed\",\n                    description: \"Unable to connect to Binance API.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"Connection Error\",\n                description: \"Network error while testing API connection.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleSaveTelegramConfig = ()=>{\n        try {\n            localStorage.setItem('telegram_bot_token', telegramToken);\n            localStorage.setItem('telegram_chat_id', telegramChatId);\n            console.log(\"Telegram Config Saved:\", {\n                telegramToken: telegramToken.substring(0, 10) + '...',\n                telegramChatId\n            });\n            toast({\n                title: \"Telegram Config Saved\",\n                description: \"Telegram settings have been saved successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save Telegram configuration.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleTestTelegram = async ()=>{\n        if (!telegramToken || !telegramChatId) {\n            toast({\n                title: \"Missing Configuration\",\n                description: \"Please enter both Telegram bot token and chat ID.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    chat_id: telegramChatId,\n                    text: '🤖 Test message from Pluto Trading Bot! Your Telegram integration is working correctly.'\n                })\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Telegram Test Successful\",\n                    description: \"Test message sent successfully!\"\n                });\n            } else {\n                toast({\n                    title: \"Telegram Test Failed\",\n                    description: \"Failed to send test message. Check your token and chat ID.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"Telegram Error\",\n                description: \"Network error while testing Telegram integration.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const adminTabs = [\n        {\n            value: \"systemTools\",\n            label: \"System Tools\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 58\n            }, this)\n        },\n        {\n            value: \"apiKeys\",\n            label: \"Exchange API Keys\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 59\n            }, this)\n        },\n        {\n            value: \"telegram\",\n            label: \"Telegram Integration\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 63\n            }, this)\n        },\n        {\n            value: \"sessionManager\",\n            label: \"Session Manager\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 64\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n            className: \"border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                    className: \"flex flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    className: \"text-3xl font-bold text-primary\",\n                                    children: \"Admin Panel\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    children: \"Manage global settings and tools for Pluto Trading Bot.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>router.push('/dashboard'),\n                            className: \"btn-outline-neo\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                \"Return to Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                        defaultValue: \"systemTools\",\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_11__.ScrollArea, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"bg-card border-border border-2 p-1\",\n                                    children: adminTabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: tab.value,\n                                            className: \"px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    tab.icon,\n                                                    \" \",\n                                                    tab.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 22\n                                            }, this)\n                                        }, tab.value, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 20\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"systemTools\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                        className: \"bg-card-foreground/5 border-border border-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                    children: \"System Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 31\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Database Editor, Clean Duplicates, Export/Import, Backup/Restore, Diagnostics - (Placeholders for future implementation).\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"DB Editor Clicked\"\n                                                                    }),\n                                                                children: \"View Database (Read-Only)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Export Orders Clicked\"\n                                                                    }),\n                                                                children: \"Export Orders to Excel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Export History Clicked\"\n                                                                    }),\n                                                                children: \"Export History to Excel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Backup DB Clicked\"\n                                                                    }),\n                                                                children: \"Backup Database\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Restore DB Clicked\"\n                                                                    }),\n                                                                disabled: true,\n                                                                children: \"Restore Database\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Diagnostics Clicked\"\n                                                                    }),\n                                                                children: \"Run System Diagnostics\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"apiKeys\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    className: \"bg-card-foreground/5 border-border border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Exchange API Keys (Binance)\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 29\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Configure your Binance API keys for real trading. Keys are stored securely in browser storage.\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"apiKey\",\n                                                            children: \"API Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"apiKey\",\n                                                                    type: showApiKey ? \"text\" : \"password\",\n                                                                    value: apiKey,\n                                                                    onChange: (e)=>setApiKey(e.target.value),\n                                                                    placeholder: \"Enter your Binance API key\",\n                                                                    className: \"pr-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowApiKey(!showApiKey),\n                                                                    children: showApiKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 187,\n                                                                        columnNumber: 39\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 187,\n                                                                        columnNumber: 72\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 180,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"apiSecret\",\n                                                            children: \"API Secret\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"apiSecret\",\n                                                                    type: showApiSecret ? \"text\" : \"password\",\n                                                                    value: apiSecret,\n                                                                    onChange: (e)=>setApiSecret(e.target.value),\n                                                                    placeholder: \"Enter your Binance API secret\",\n                                                                    className: \"pr-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowApiSecret(!showApiSecret),\n                                                                    children: showApiSecret ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 42\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 75\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: handleSaveApiKeys,\n                                                            className: \"btn-neo\",\n                                                            children: \"Save API Keys\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: handleTestApiConnection,\n                                                            variant: \"outline\",\n                                                            className: \"btn-outline-neo\",\n                                                            children: \"Test Connection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 215,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"telegram\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                            className: \"bg-card-foreground/5 border-border border-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                        children: \"Telegram Configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Configure Telegram bot for real-time trading notifications.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"telegramToken\",\n                                                                    children: \"Telegram Bot Token\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"telegramToken\",\n                                                                    type: \"password\",\n                                                                    value: telegramToken,\n                                                                    onChange: (e)=>setTelegramToken(e.target.value),\n                                                                    placeholder: \"Enter your Telegram bot token\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"telegramChatId\",\n                                                                    children: \"Telegram Chat ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"telegramChatId\",\n                                                                    value: telegramChatId,\n                                                                    onChange: (e)=>setTelegramChatId(e.target.value),\n                                                                    placeholder: \"Enter your Telegram chat ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                                    id: \"notifyOnOrder\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"notifyOnOrder\",\n                                                                    children: \"Notify on Order Execution\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                                    id: \"notifyOnErrors\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 241,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"notifyOnErrors\",\n                                                                    children: \"Notify on Errors\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 242,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    onClick: handleSaveTelegramConfig,\n                                                                    className: \"btn-neo\",\n                                                                    children: \"Save Telegram Config\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    onClick: handleTestTelegram,\n                                                                    variant: \"outline\",\n                                                                    className: \"btn-outline-neo\",\n                                                                    children: \"Test Telegram\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                            className: \"bg-card-foreground/5 border-border border-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                        children: \"Setup Guide\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-600 mb-2\",\n                                                                        children: \"Step 1: Create a Telegram Bot\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                        className: \"text-sm space-y-1 list-decimal list-inside text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: [\n                                                                                    \"Open Telegram and search for \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                        className: \"bg-muted px-1 rounded\",\n                                                                                        children: \"@BotFather\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 259,\n                                                                                        columnNumber: 60\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 259,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: [\n                                                                                    \"Send \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                        className: \"bg-muted px-1 rounded\",\n                                                                                        children: \"/newbot\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 260,\n                                                                                        columnNumber: 36\n                                                                                    }, this),\n                                                                                    \" command\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 260,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Choose a name for your bot (e.g., \"My Trading Bot\")'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 261,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Choose a username ending with \"bot\" (e.g., \"mytradingbot\")'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 262,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Copy the bot token provided by BotFather\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-600 mb-2\",\n                                                                        children: \"Step 2: Get Your Chat ID\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 268,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                        className: \"text-sm space-y-1 list-decimal list-inside text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Start a chat with your new bot\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 270,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Send any message to the bot\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 271,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: [\n                                                                                    \"Visit: \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                        className: \"bg-muted px-1 rounded text-xs\",\n                                                                                        children: \"https://api.telegram.org/bot[YOUR_BOT_TOKEN]/getUpdates\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 272,\n                                                                                        columnNumber: 38\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 272,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Look for \"chat\" and \"id\" fields in the response'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 273,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Copy the chat ID number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 274,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 269,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-600 mb-2\",\n                                                                        children: \"Step 3: Configure Bot\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 279,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-sm space-y-1 list-disc list-inside text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Paste the bot token in the \"Telegram Bot Token\" field'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 281,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Paste the chat ID in the \"Telegram Chat ID\" field'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 282,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Choose your notification preferences\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 283,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Click \"Save Telegram Config\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 284,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Test the connection with \"Test Telegram\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 285,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 280,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-md\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-yellow-600\",\n                                                                            children: \"\\uD83D\\uDCA1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 291,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-yellow-600 mb-1\",\n                                                                                    children: \"Pro Tip:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 293,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-yellow-700\",\n                                                                                    children: \"Keep your bot token secure and never share it publicly. You can regenerate it anytime via BotFather if needed.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 294,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 292,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"sessionManager\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SessionManager__WEBPACK_IMPORTED_MODULE_12__.SessionManager, {}, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPanelPage, \"tbnDcI/g4v3KjLPgVB/4mL2wCfY=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_9__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPanelPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPanelPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});