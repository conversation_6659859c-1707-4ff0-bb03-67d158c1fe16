"use client";

import React, { useState } from 'react';
import { useTradingContext } from '@/contexts/TradingContext';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Volume2, Play, Settings } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Available ringtone options from frontend/ringtones folder
const RINGTONE_OPTIONS = [
  { value: 'G_hades_curse.wav', label: 'Hades Curse' },
  { value: 'G_hades_demat.wav', label: 'Hades Demat' },
  { value: 'G_hades_mat.wav', label: 'Hades Mat' },
  { value: 'G_hades_sanctify.wav', label: 'Hades Sanctify' },
  { value: 'S_mon1.mp3', label: 'Monster 1' },
  { value: 'S_mon2.mp3', label: 'Monster 2' },
  { value: 'Satyr_atk4.wav', label: 'Satyr Attack' },
  { value: 'bells.wav', label: 'Bells' },
  { value: 'bird1.wav', label: 'Bird 1' },
  { value: 'bird7.wav', label: 'Bird 7' },
  { value: 'cheer.wav', label: 'Cheer' },
  { value: 'chest1.wav', label: 'Chest' },
  { value: 'chime2.wav', label: 'Chime' },
  { value: 'dark2.wav', label: 'Dark' },
  { value: 'foundry2.wav', label: 'Foundry' },
  { value: 'goatherd1.wav', label: 'Goatherd' },
  { value: 'marble1.wav', label: 'Marble' },
  { value: 'sanctuary1.wav', label: 'Sanctuary' },
  { value: 'space_bells4a.wav', label: 'Space Bells' },
  { value: 'sparrow1.wav', label: 'Sparrow' },
  { value: 'tax3.wav', label: 'Tax' },
  { value: 'wolf4.wav', label: 'Wolf' }
];

interface AlarmSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AlarmSettings({ isOpen, onClose }: AlarmSettingsProps) {
  const { sessionAlarmConfig, dispatch, playSessionAlarm } = useTradingContext();
  const { toast } = useToast();

  const [localConfig, setLocalConfig] = useState(sessionAlarmConfig);

  const handleSave = () => {
    dispatch({ type: 'SET_SESSION_ALARM_CONFIG', payload: localConfig });
    toast({
      title: "Alarm Settings Saved",
      description: "Your session-specific alarm settings have been updated.",
      duration: 3000
    });
    onClose();
  };

  const handleTestSound = (soundFile: string) => {
    try {
      const audio = new Audio(`/ringtones/${soundFile}`);
      audio.volume = localConfig.volume / 100;
      audio.play().catch(console.error);
    } catch (error) {
      console.error('Failed to play test sound:', error);
      toast({
        title: "Sound Test Failed",
        description: "Could not play the selected sound file.",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Session Alarm Settings
          </DialogTitle>
          <DialogDescription>
            Configure custom alarm sounds and settings for this trading session.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 overflow-y-auto max-h-[60vh] pr-2">
          {/* Volume Control */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Volume2 className="h-4 w-4" />
                Volume Control
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Label>Volume: {localConfig.volume}%</Label>
                <Slider
                  value={[localConfig.volume]}
                  onValueChange={(value) => setLocalConfig({ ...localConfig, volume: value[0] })}
                  max={100}
                  min={0}
                  step={5}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>

          {/* Success Alarm Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm text-green-600">Order Execution Success Alarms</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="buyAlarmEnabled"
                  checked={localConfig.buyAlarmEnabled}
                  onCheckedChange={(checked) =>
                    setLocalConfig({ ...localConfig, buyAlarmEnabled: checked as boolean })
                  }
                />
                <Label htmlFor="buyAlarmEnabled">Enable alerts on successful order execution</Label>
              </div>

              <div className="space-y-2">
                <Label>Success Alarm Sound</Label>
                <div className="flex gap-2">
                  <Select
                    value={localConfig.buyAlarmSound}
                    onValueChange={(value) => setLocalConfig({ ...localConfig, buyAlarmSound: value })}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="max-h-[200px] overflow-y-auto">
                      {RINGTONE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestSound(localConfig.buyAlarmSound)}
                    disabled={!localConfig.buyAlarmEnabled}
                  >
                    <Play className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Error/Failure Alarm Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm text-red-600">Error/Failure Alarms</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="sellAlarmEnabled"
                  checked={localConfig.sellAlarmEnabled}
                  onCheckedChange={(checked) =>
                    setLocalConfig({ ...localConfig, sellAlarmEnabled: checked as boolean })
                  }
                />
                <Label htmlFor="sellAlarmEnabled">Enable alerts on errors/failures</Label>
              </div>

              <div className="space-y-2">
                <Label>Error Alarm Sound</Label>
                <div className="flex gap-2">
                  <Select
                    value={localConfig.sellAlarmSound}
                    onValueChange={(value) => setLocalConfig({ ...localConfig, sellAlarmSound: value })}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="max-h-[200px] overflow-y-auto">
                      {RINGTONE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestSound(localConfig.sellAlarmSound)}
                    disabled={!localConfig.sellAlarmEnabled}
                  >
                    <Play className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave} className="btn-neo">
              Save Settings
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
