"use client";

import React, { useState } from 'react';
import { useTradingContext } from '@/contexts/TradingContext';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Slider } from "@/components/ui/slider";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Volume2, Play, Settings } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

// Available ringtone options
const RINGTONE_OPTIONS = [
  { value: 'default.mp3', label: 'Default' },
  { value: 'bell.mp3', label: 'Bell' },
  { value: 'chime.mp3', label: 'Chime' },
  { value: 'ding.mp3', label: 'Ding' },
  { value: 'notification.mp3', label: 'Notification' },
  { value: 'alert.mp3', label: 'Alert' },
  { value: 'beep.mp3', label: 'Beep' },
  { value: 'buzz.mp3', label: 'Buzz' },
  { value: 'coin.mp3', label: 'Coin' },
  { value: 'success.mp3', label: 'Success' },
  { value: 'warning.mp3', label: 'Warning' },
  { value: 'error.mp3', label: 'Error' },
  { value: 'pop.mp3', label: 'Pop' },
  { value: 'click.mp3', label: 'Click' },
  { value: 'swoosh.mp3', label: 'Swoosh' },
  { value: 'ping.mp3', label: 'Ping' },
  { value: 'tone.mp3', label: 'Tone' },
  { value: 'ring.mp3', label: 'Ring' },
  { value: 'alarm.mp3', label: 'Alarm' },
  { value: 'horn.mp3', label: 'Horn' },
  { value: 'whistle.mp3', label: 'Whistle' },
  { value: 'gong.mp3', label: 'Gong' }
];

interface AlarmSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AlarmSettings({ isOpen, onClose }: AlarmSettingsProps) {
  const { sessionAlarmConfig, dispatch, playSessionAlarm } = useTradingContext();
  const { toast } = useToast();

  const [localConfig, setLocalConfig] = useState(sessionAlarmConfig);

  const handleSave = () => {
    dispatch({ type: 'SET_SESSION_ALARM_CONFIG', payload: localConfig });
    toast({
      title: "Alarm Settings Saved",
      description: "Your session-specific alarm settings have been updated.",
      duration: 3000
    });
    onClose();
  };

  const handleTestSound = (soundFile: string) => {
    try {
      const audio = new Audio(`/ringtones/${soundFile}`);
      audio.volume = localConfig.volume / 100;
      audio.play().catch(console.error);
    } catch (error) {
      console.error('Failed to play test sound:', error);
      toast({
        title: "Sound Test Failed",
        description: "Could not play the selected sound file.",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Session Alarm Settings
          </DialogTitle>
          <DialogDescription>
            Configure custom alarm sounds and settings for this trading session.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Volume Control */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm flex items-center gap-2">
                <Volume2 className="h-4 w-4" />
                Volume Control
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Label>Volume: {localConfig.volume}%</Label>
                <Slider
                  value={[localConfig.volume]}
                  onValueChange={(value) => setLocalConfig({ ...localConfig, volume: value[0] })}
                  max={100}
                  min={0}
                  step={5}
                  className="w-full"
                />
              </div>
            </CardContent>
          </Card>

          {/* Buy Alarm Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm text-green-600">Buy Order Alarms</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="buyAlarmEnabled"
                  checked={localConfig.buyAlarmEnabled}
                  onCheckedChange={(checked) => 
                    setLocalConfig({ ...localConfig, buyAlarmEnabled: checked as boolean })
                  }
                />
                <Label htmlFor="buyAlarmEnabled">Enable buy order alarms</Label>
              </div>

              <div className="space-y-2">
                <Label>Buy Alarm Sound</Label>
                <div className="flex gap-2">
                  <Select
                    value={localConfig.buyAlarmSound}
                    onValueChange={(value) => setLocalConfig({ ...localConfig, buyAlarmSound: value })}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {RINGTONE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestSound(localConfig.buyAlarmSound)}
                    disabled={!localConfig.buyAlarmEnabled}
                  >
                    <Play className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Sell Alarm Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm text-red-600">Sell Order Alarms</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="sellAlarmEnabled"
                  checked={localConfig.sellAlarmEnabled}
                  onCheckedChange={(checked) => 
                    setLocalConfig({ ...localConfig, sellAlarmEnabled: checked as boolean })
                  }
                />
                <Label htmlFor="sellAlarmEnabled">Enable sell order alarms</Label>
              </div>

              <div className="space-y-2">
                <Label>Sell Alarm Sound</Label>
                <div className="flex gap-2">
                  <Select
                    value={localConfig.sellAlarmSound}
                    onValueChange={(value) => setLocalConfig({ ...localConfig, sellAlarmSound: value })}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {RINGTONE_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestSound(localConfig.sellAlarmSound)}
                    disabled={!localConfig.sellAlarmEnabled}
                  >
                    <Play className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleSave} className="btn-neo">
              Save Settings
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
