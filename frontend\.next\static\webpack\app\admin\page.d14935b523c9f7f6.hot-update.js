"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/app/admin/page.tsx":
/*!********************************!*\
  !*** ./src/app/admin/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminPanelPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key-round.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Eye,EyeOff,FileText,Home,KeyRound,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _components_admin_SessionManager__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/admin/SessionManager */ \"(app-pages-browser)/./src/components/admin/SessionManager.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AdminPanelPage() {\n    _s();\n    const { appSettings, dispatch, botSystemStatus } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_10__.useTradingContext)();\n    const [localSettings, setLocalSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(appSettings);\n    const [apiKey, setApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('iVIOLOnigRM31Qzm4UoLYsJo4QYIsd1XeXKztnwHfcijpWiAaWQKRsmx3NO7LrLA');\n    const [apiSecret, setApiSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('jzAnpgIFFv3Ypdhf4jEXljjbkBpfJE5W2aN0zrtypmD3RAjoh2vdQXMr66LOv5fp');\n    const [showApiKey, setShowApiKey] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showApiSecret, setShowApiSecret] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [telegramToken, setTelegramToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [telegramChatId, setTelegramChatId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminPanelPage.useEffect\": ()=>{\n            setLocalSettings(appSettings);\n        }\n    }[\"AdminPanelPage.useEffect\"], [\n        appSettings\n    ]);\n    const handleSettingsChange = (key, value)=>{\n        setLocalSettings((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const handleSaveAppSettings = ()=>{\n        dispatch({\n            type: 'SET_APP_SETTINGS',\n            payload: localSettings\n        });\n        toast({\n            title: \"App Settings Saved\",\n            description: \"Global application settings have been updated.\"\n        });\n    };\n    const handleSaveApiKeys = async ()=>{\n        try {\n            // Store API keys securely (in a real implementation, these would be encrypted)\n            localStorage.setItem('binance_api_key', apiKey);\n            localStorage.setItem('binance_api_secret', apiSecret);\n            console.log(\"API Keys Saved:\", {\n                apiKey: apiKey.substring(0, 10) + '...',\n                apiSecret: apiSecret.substring(0, 10) + '...'\n            });\n            toast({\n                title: \"API Keys Saved\",\n                description: \"Binance API keys have been saved securely.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save API keys.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleTestApiConnection = async ()=>{\n        try {\n            // Test connection to Binance API\n            const response = await fetch('https://api.binance.com/api/v3/ping');\n            if (response.ok) {\n                toast({\n                    title: \"API Connection Test\",\n                    description: \"Successfully connected to Binance API!\"\n                });\n            } else {\n                toast({\n                    title: \"Connection Failed\",\n                    description: \"Unable to connect to Binance API.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"Connection Error\",\n                description: \"Network error while testing API connection.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleSaveTelegramConfig = ()=>{\n        try {\n            localStorage.setItem('telegram_bot_token', telegramToken);\n            localStorage.setItem('telegram_chat_id', telegramChatId);\n            console.log(\"Telegram Config Saved:\", {\n                telegramToken: telegramToken.substring(0, 10) + '...',\n                telegramChatId\n            });\n            toast({\n                title: \"Telegram Config Saved\",\n                description: \"Telegram settings have been saved successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to save Telegram configuration.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleTestTelegram = async ()=>{\n        if (!telegramToken || !telegramChatId) {\n            toast({\n                title: \"Missing Configuration\",\n                description: \"Please enter both Telegram bot token and chat ID.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    chat_id: telegramChatId,\n                    text: '🤖 Test message from Pluto Trading Bot! Your Telegram integration is working correctly.'\n                })\n            });\n            if (response.ok) {\n                toast({\n                    title: \"Telegram Test Successful\",\n                    description: \"Test message sent successfully!\"\n                });\n            } else {\n                toast({\n                    title: \"Telegram Test Failed\",\n                    description: \"Failed to send test message. Check your token and chat ID.\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            toast({\n                title: \"Telegram Error\",\n                description: \"Network error while testing Telegram integration.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const adminTabs = [\n        {\n            value: \"systemTools\",\n            label: \"System Tools\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 58\n            }, this)\n        },\n        {\n            value: \"apiKeys\",\n            label: \"Exchange API Keys\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 59\n            }, this)\n        },\n        {\n            value: \"telegram\",\n            label: \"Telegram Integration\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 63\n            }, this)\n        },\n        {\n            value: \"sessionManager\",\n            label: \"Session Manager\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                className: \"mr-2 h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 64\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n            className: \"border-2 border-border\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                    className: \"flex flex-row justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    className: \"text-3xl font-bold text-primary\",\n                                    children: \"Admin Panel\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    children: \"Manage global settings and tools for Pluto Trading Bot.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>router.push('/dashboard'),\n                            className: \"btn-outline-neo\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                \"Return to Dashboard\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                        defaultValue: \"systemTools\",\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_13__.ScrollArea, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                    className: \"bg-card border-border border-2 p-1\",\n                                    children: adminTabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                            value: tab.value,\n                                            className: \"px-4 py-2 text-sm data-[state=active]:bg-primary data-[state=active]:text-primary-foreground\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    tab.icon,\n                                                    \" \",\n                                                    tab.label\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 22\n                                            }, this)\n                                        }, tab.value, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 20\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"systemTools\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                        className: \"bg-card-foreground/5 border-border border-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                    children: \"System Tools\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 31\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Database Editor, Clean Duplicates, Export/Import, Backup/Restore, Diagnostics - (Placeholders for future implementation).\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 148,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-3 gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"DB Editor Clicked\"\n                                                                    }),\n                                                                children: \"View Database (Read-Only)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 150,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Export Orders Clicked\"\n                                                                    }),\n                                                                children: \"Export Orders to Excel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Export History Clicked\"\n                                                                    }),\n                                                                children: \"Export History to Excel\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 152,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Backup DB Clicked\"\n                                                                    }),\n                                                                children: \"Backup Database\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 153,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Restore DB Clicked\"\n                                                                    }),\n                                                                disabled: true,\n                                                                children: \"Restore Database\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 154,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                className: \"btn-outline-neo\",\n                                                                onClick: ()=>toast({\n                                                                        title: \"Diagnostics Clicked\"\n                                                                    }),\n                                                                children: \"Run System Diagnostics\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 155,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"appSettings\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    className: \"bg-card-foreground/5 border-border border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Application Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 29\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"preferredStablecoin\",\n                                                            children: \"Preferred Stablecoin (for Swap Mode)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.Select, {\n                                                            value: localSettings.preferredStablecoin,\n                                                            onValueChange: (val)=>handleSettingsChange('preferredStablecoin', val),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectTrigger, {\n                                                                    id: \"preferredStablecoin\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectValue, {\n                                                                        placeholder: \"Select Stablecoin\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 172,\n                                                                        columnNumber: 63\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectContent, {\n                                                                    children: _lib_types__WEBPACK_IMPORTED_MODULE_11__.AVAILABLE_STABLECOINS.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_8__.SelectItem, {\n                                                                            value: sc,\n                                                                            children: sc\n                                                                        }, sc, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 174,\n                                                                            columnNumber: 58\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"priceUpdateIntervalMs\",\n                                                            children: \"Price Update Interval (ms)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"priceUpdateIntervalMs\",\n                                                            type: \"number\",\n                                                            value: localSettings.priceUpdateIntervalMs || 1000,\n                                                            onChange: (e)=>handleSettingsChange('priceUpdateIntervalMs', parseInt(e.target.value) || 1000)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    onClick: handleSaveAppSettings,\n                                                    className: \"btn-neo\",\n                                                    children: \"Save App Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"apiKeys\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                    className: \"bg-card-foreground/5 border-border border-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                children: \"Exchange API Keys (Binance)\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 29\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Configure your Binance API keys for real trading. Keys are stored securely in browser storage.\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"apiKey\",\n                                                            children: \"API Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"apiKey\",\n                                                                    type: showApiKey ? \"text\" : \"password\",\n                                                                    value: apiKey,\n                                                                    onChange: (e)=>setApiKey(e.target.value),\n                                                                    placeholder: \"Enter your Binance API key\",\n                                                                    className: \"pr-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowApiKey(!showApiKey),\n                                                                    children: showApiKey ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 215,\n                                                                        columnNumber: 39\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 215,\n                                                                        columnNumber: 72\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"apiSecret\",\n                                                            children: \"API Secret\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"apiSecret\",\n                                                                    type: showApiSecret ? \"text\" : \"password\",\n                                                                    value: apiSecret,\n                                                                    onChange: (e)=>setApiSecret(e.target.value),\n                                                                    placeholder: \"Enter your Binance API secret\",\n                                                                    className: \"pr-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\",\n                                                                    onClick: ()=>setShowApiSecret(!showApiSecret),\n                                                                    children: showApiSecret ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 42\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Eye_EyeOff_FileText_Home_KeyRound_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 75\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: handleSaveApiKeys,\n                                                            className: \"btn-neo\",\n                                                            children: \"Save API Keys\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            onClick: handleTestApiConnection,\n                                                            variant: \"outline\",\n                                                            className: \"btn-outline-neo\",\n                                                            children: \"Test Connection\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"telegram\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                            className: \"bg-card-foreground/5 border-border border-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                        children: \"Telegram Configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Configure Telegram bot for real-time trading notifications.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"telegramToken\",\n                                                                    children: \"Telegram Bot Token\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"telegramToken\",\n                                                                    type: \"password\",\n                                                                    value: telegramToken,\n                                                                    onChange: (e)=>setTelegramToken(e.target.value),\n                                                                    placeholder: \"Enter your Telegram bot token\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"telegramChatId\",\n                                                                    children: \"Telegram Chat ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"telegramChatId\",\n                                                                    value: telegramChatId,\n                                                                    onChange: (e)=>setTelegramChatId(e.target.value),\n                                                                    placeholder: \"Enter your Telegram chat ID\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 262,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                    id: \"notifyOnOrder\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"notifyOnOrder\",\n                                                                    children: \"Notify on Order Execution\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_9__.Switch, {\n                                                                    id: \"notifyOnErrors\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"notifyOnErrors\",\n                                                                    children: \"Notify on Errors\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    onClick: handleSaveTelegramConfig,\n                                                                    className: \"btn-neo\",\n                                                                    children: \"Save Telegram Config\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    onClick: handleTestTelegram,\n                                                                    variant: \"outline\",\n                                                                    className: \"btn-outline-neo\",\n                                                                    children: \"Test Telegram\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                            lineNumber: 272,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                                            className: \"bg-card-foreground/5 border-border border-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                                        children: \"Setup Guide\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-600 mb-2\",\n                                                                        children: \"Step 1: Create a Telegram Bot\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                        className: \"text-sm space-y-1 list-decimal list-inside text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: [\n                                                                                    \"Open Telegram and search for \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                        className: \"bg-muted px-1 rounded\",\n                                                                                        children: \"@BotFather\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 287,\n                                                                                        columnNumber: 60\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 287,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: [\n                                                                                    \"Send \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                        className: \"bg-muted px-1 rounded\",\n                                                                                        children: \"/newbot\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 288,\n                                                                                        columnNumber: 36\n                                                                                    }, this),\n                                                                                    \" command\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 288,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Choose a name for your bot (e.g., \"My Trading Bot\")'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 289,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Choose a username ending with \"bot\" (e.g., \"mytradingbot\")'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 290,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Copy the bot token provided by BotFather\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 291,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-600 mb-2\",\n                                                                        children: \"Step 2: Get Your Chat ID\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 296,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                                        className: \"text-sm space-y-1 list-decimal list-inside text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Start a chat with your new bot\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 298,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Send any message to the bot\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: [\n                                                                                    \"Visit: \",\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                                        className: \"bg-muted px-1 rounded text-xs\",\n                                                                                        children: \"https://api.telegram.org/bot[YOUR_BOT_TOKEN]/getUpdates\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                        lineNumber: 300,\n                                                                                        columnNumber: 38\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 300,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Look for \"chat\" and \"id\" fields in the response'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 301,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Copy the chat ID number\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-yellow-600 mb-2\",\n                                                                        children: \"Step 3: Configure Bot\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                        className: \"text-sm space-y-1 list-disc list-inside text-muted-foreground\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Paste the bot token in the \"Telegram Bot Token\" field'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 309,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Paste the chat ID in the \"Telegram Chat ID\" field'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 310,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: \"Choose your notification preferences\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 311,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Click \"Save Telegram Config\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 312,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                children: 'Test the connection with \"Test Telegram\"'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                lineNumber: 313,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-md\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-yellow-600\",\n                                                                            children: \"\\uD83D\\uDCA1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 319,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"font-medium text-yellow-600 mb-1\",\n                                                                                    children: \"Pro Tip:\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 321,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-yellow-700\",\n                                                                                    children: \"Keep your bot token secure and never share it publicly. You can regenerate it anytime via BotFather if needed.\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                                    lineNumber: 322,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                            lineNumber: 320,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                value: \"sessionManager\",\n                                className: \"mt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_SessionManager__WEBPACK_IMPORTED_MODULE_14__.SessionManager, {}, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\app\\\\admin\\\\page.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminPanelPage, \"tbnDcI/g4v3KjLPgVB/4mL2wCfY=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_10__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AdminPanelPage;\nvar _c;\n$RefreshReg$(_c, \"AdminPanelPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/page.tsx\n"));

/***/ })

});