(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{25731:(e,t,r)=>{"use strict";r.d(t,{Rk:()=>i,ZQ:()=>a,oc:()=>s});let o="http://localhost:5000";async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat(o).concat(e),n=localStorage.getItem("plutoAuthToken"),a={"Content-Type":"application/json",...n?{Authorization:"Bearer ".concat(n)}:{},...t.headers};try{let e;let o=new AbortController,n=setTimeout(()=>o.abort(),1e4),s=await fetch(r,{...t,headers:a,signal:o.signal}).finally(()=>clearTimeout(n));if(401===s.status)throw localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),window.location.href="/login",Error("Authentication expired. Please login again.");let i=s.headers.get("content-type");if(i&&i.includes("application/json"))e=await s.json();else{let t=await s.text();try{e=JSON.parse(t)}catch(r){e={message:t}}}if(!s.ok)throw console.error("API error response:",e),Error(e.error||e.message||"API error: ".concat(s.status));return e}catch(e){if(e instanceof TypeError&&e.message.includes("Failed to fetch"))throw console.error("Network error - Is the backend server running?:",e),Error("Cannot connect to server. Please check if the backend is running.");if("AbortError"===e.name)throw console.error("Request timeout:",e),Error("Request timed out. Server may be unavailable.");throw console.error("API request failed:",e),e}}console.log("API Base URL:",o);let a={login:async(e,t)=>{try{let r=await c(async()=>await n("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:t})}));if(r&&r.access_token)return localStorage.setItem("plutoAuthToken",r.access_token),localStorage.setItem("plutoAuth","true"),r.user&&localStorage.setItem("plutoUser",JSON.stringify(r.user)),!0;return!1}catch(e){return console.error("Login API error:",e),!1}},register:async(e,t,r)=>c(async()=>await n("/auth/register",{method:"POST",body:JSON.stringify({username:e,password:t,email:r})})),logout:async()=>(localStorage.removeItem("plutoAuth"),localStorage.removeItem("plutoAuthToken"),localStorage.removeItem("plutoUser"),!0)},s={getConfig:async e=>n(e?"/trading/config/".concat(e):"/trading/config"),saveConfig:async e=>n("/trading/config",{method:"POST",body:JSON.stringify(e)}),updateConfig:async(e,t)=>n("/trading/config/".concat(e),{method:"PUT",body:JSON.stringify(t)}),startBot:async e=>n("/trading/bot/start/".concat(e),{method:"POST"}),stopBot:async e=>n("/trading/bot/stop/".concat(e),{method:"POST"}),getBotStatus:async e=>n("/trading/bot/status/".concat(e)),getTradeHistory:async e=>n("/trading/history".concat(e?"?configId=".concat(e):"")),getBalances:async()=>n("/trading/balances"),getMarketPrice:async e=>n("/trading/market-data/".concat(e)),getTradingPairs:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return n("/trading/exchange/trading-pairs?exchange=".concat(e))},getCryptocurrencies:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"binance";return n("/trading/exchange/cryptocurrencies?exchange=".concat(e))}},i={getAllSessions:async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return n("/sessions/?include_inactive=".concat(e))},createSession:async e=>n("/sessions/",{method:"POST",body:JSON.stringify(e)}),getSession:async e=>n("/sessions/".concat(e)),updateSession:async(e,t)=>n("/sessions/".concat(e),{method:"PUT",body:JSON.stringify(t)}),deleteSession:async e=>n("/sessions/".concat(e),{method:"DELETE"}),activateSession:async e=>n("/sessions/".concat(e,"/activate"),{method:"POST"}),getSessionHistory:async e=>n("/sessions/".concat(e,"/history")),getActiveSession:async()=>n("/sessions/active")},c=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,r=0,o=async()=>{try{return await e()}catch(e){if((e instanceof TypeError&&e.message.includes("Failed to fetch")||"AbortError"===e.name)&&r<t){let e=500*Math.pow(2,r);return console.log("Retrying after ".concat(e,"ms (attempt ").concat(r+1,"/").concat(t,")...")),r++,await new Promise(t=>setTimeout(t,e)),o()}throw e}};return o()}},33792:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var o=r(95155),n=r(12115),a=r(35695),s=r(40283),i=r(50172);function c(){let e=(0,a.useRouter)(),{isAuthenticated:t,isLoading:r}=(0,s.A)();return(0,n.useEffect)(()=>{r||(t?e.replace("/dashboard"):e.replace("/login"))},[t,r,e]),(0,o.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background",children:[(0,o.jsx)(i.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,o.jsx)("p",{className:"ml-4 text-xl",children:"Initializing Pluto..."})]})}},35695:(e,t,r)=>{"use strict";var o=r(18999);r.o(o,"usePathname")&&r.d(t,{usePathname:function(){return o.usePathname}}),r.o(o,"useRouter")&&r.d(t,{useRouter:function(){return o.useRouter}})},40157:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var o=r(12115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,o.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:c,className:l="",children:u,iconNode:g,...d}=e;return(0,o.createElement)("svg",{ref:t,...s,width:n,height:n,stroke:r,strokeWidth:c?24*Number(i)/Number(n):i,className:a("lucide",l),...d},[...g.map(e=>{let[t,r]=e;return(0,o.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),c=(e,t)=>{let r=(0,o.forwardRef)((r,s)=>{let{className:c,...l}=r;return(0,o.createElement)(i,{ref:s,iconNode:t,className:a("lucide-".concat(n(e)),c),...l})});return r.displayName="".concat(e),r}},40283:(e,t,r)=>{"use strict";r.d(t,{A:()=>u,AuthProvider:()=>l});var o=r(95155),n=r(12115),a=r(35695),s=r(50172),i=r(25731);let c=(0,n.createContext)(void 0),l=e=>{let{children:t}=e,[r,l]=(0,n.useState)(!1),[u,g]=(0,n.useState)(!0),d=(0,a.useRouter)(),h=(0,a.usePathname)();(0,n.useEffect)(()=>{let e=localStorage.getItem("plutoAuth"),t=localStorage.getItem("plutoAuthToken");"true"===e&&t&&l(!0),g(!1)},[]),(0,n.useEffect)(()=>{u||r||"/login"===h?!u&&r&&"/login"===h&&d.push("/dashboard"):d.push("/login")},[r,u,h,d]);let m=async(e,t)=>{g(!0);try{if(await i.ZQ.login(e,t))return l(!0),d.push("/dashboard"),!0;return l(!1),!1}catch(e){return console.error("Login failed:",e),l(!1),!1}finally{g(!1)}},y=async()=>{try{await i.ZQ.logout()}catch(e){console.error("Logout error:",e)}finally{l(!1),d.push("/login")}};return!u||(null==h?void 0:h.startsWith("/_next/static/"))?r||"/login"===h||(null==h?void 0:h.startsWith("/_next/static/"))?(0,o.jsx)(c.Provider,{value:{isAuthenticated:r,login:m,logout:y,isLoading:u},children:t}):(0,o.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,o.jsx)(s.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,o.jsx)("p",{className:"ml-4 text-xl",children:"Redirecting to login..."})]}):(0,o.jsxs)("div",{className:"flex items-center justify-center h-screen bg-background text-foreground",children:[(0,o.jsx)(s.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,o.jsx)("p",{className:"ml-4 text-xl",children:"Loading Pluto..."})]})},u=()=>{let e=(0,n.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},50172:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(40157).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},99225:(e,t,r)=>{Promise.resolve().then(r.bind(r,33792))}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(99225)),_N_E=e.O()}]);