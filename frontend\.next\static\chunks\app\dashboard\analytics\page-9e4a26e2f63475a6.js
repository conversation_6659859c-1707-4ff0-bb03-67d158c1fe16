(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[754],{22346:(e,t,r)=>{"use strict";r.d(t,{w:()=>o});var s=r(95155),a=r(12115),i=r(87489),l=r(59434);let o=a.forwardRef((e,t)=>{let{className:r,orientation:a="horizontal",decorative:o=!0,...d}=e;return(0,s.jsx)(i.b,{ref:t,decorative:o,orientation:a,className:(0,l.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",r),...d})});o.displayName=i.b.displayName},52669:(e,t,r)=>{Promise.resolve().then(r.bind(r,70709))},59409:(e,t,r)=>{"use strict";r.d(t,{bq:()=>m,eb:()=>h,gC:()=>u,l6:()=>c,yv:()=>p});var s=r(95155),a=r(12115),i=r(50663),l=r(79556),o=r(77381),d=r(10518),n=r(59434);let c=i.bL;i.YJ;let p=i.WT,m=a.forwardRef((e,t)=>{let{className:r,children:a,...o}=e;return(0,s.jsxs)(i.l9,{ref:t,className:(0,n.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...o,children:[a,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=i.l9.displayName;let x=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.PP,{ref:t,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})});x.displayName=i.PP.displayName;let f=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.wn,{ref:t,className:(0,n.cn)("flex cursor-default items-center justify-center py-1",r),...a,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})});f.displayName=i.wn.displayName;let u=a.forwardRef((e,t)=>{let{className:r,children:a,position:l="popper",...o}=e;return(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{ref:t,className:(0,n.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:l,...o,children:[(0,s.jsx)(x,{}),(0,s.jsx)(i.LM,{className:(0,n.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,s.jsx)(f,{})]})})});u.displayName=i.UC.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.JU,{ref:t,className:(0,n.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...a})}).displayName=i.JU.displayName;let h=a.forwardRef((e,t)=>{let{className:r,children:a,...l}=e;return(0,s.jsxs)(i.q7,{ref:t,className:(0,n.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...l,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)(i.p4,{children:a})]})});h.displayName=i.q7.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.wv,{ref:t,className:(0,n.cn)("-mx-1 my-1 h-px bg-muted",r),...a})}).displayName=i.wv.displayName},70709:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var s=r(95155),a=r(12115),i=r(3033),l=r(54530),o=r(66695),d=r(59409),n=r(26126),c=r(22346),p=r(77213),m=r(84553),x=r(80659),f=r(97723),u=r(5263),h=r(81203),y=r(37648),g=r(83540),j=r(56965),v=r(94754),N=r(96025),b=r(16238),w=r(94517),C=r(21374),P=r(28184);let L=(e,t)=>"StablecoinSwap"===t.tradingMode?S(e,t):F(e,t),S=(e,t)=>{let r=e.filter(e=>("SELL"===e.type||"SELL"===e.orderType)&&"StablecoinSwap"===e.tradingMode&&void 0!==e.realizedProfitLossCrypto2),s=r.reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0),a=r.reduce((e,t)=>e+(t.realizedProfitLossCrypto1||0),0),i=r.filter(e=>(e.realizedProfitLossCrypto2||0)>0).length,l=r.length>0?i/r.length*100:0,o=e.filter(e=>"StablecoinSwap"===e.tradingMode).length,d=e.filter(e=>("BUY"===e.type||"BUY"===e.orderType)&&"StablecoinSwap"===e.tradingMode).length,n=r.length>0?s/r.length:0,c=r.length>0?a/r.length:0;return{totalProfitLossCrypto1:parseFloat(a.toFixed(t.numDigits)),totalProfitLossCrypto2:parseFloat(s.toFixed(t.numDigits)),winRate:parseFloat(l.toFixed(2)),totalTradesExecuted:o,buyTrades:d,sellTrades:r.length,avgProfitPerTradeCrypto2:parseFloat(n.toFixed(t.numDigits)),avgProfitPerTradeCrypto1:parseFloat(c.toFixed(t.numDigits))}},F=(e,t)=>{let r=e.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2&&("SimpleSpot"===e.tradingMode||!e.tradingMode)),s=r.reduce((e,t)=>e+(t.realizedProfitLossCrypto2||0),0),a=r.reduce((e,t)=>e+(t.realizedProfitLossCrypto1||0),0),i=r.filter(e=>(e.realizedProfitLossCrypto2||0)>0).length,l=r.length>0?i/r.length*100:0,o=e.filter(e=>"SimpleSpot"===e.tradingMode||!e.tradingMode).length,d=e.filter(e=>"BUY"===e.orderType&&("SimpleSpot"===e.tradingMode||!e.tradingMode)).length,n=r.length>0?s/r.length:0,c=r.length>0?a/r.length:0;return{totalProfitLossCrypto1:parseFloat(a.toFixed(t.numDigits)),totalProfitLossCrypto2:parseFloat(s.toFixed(t.numDigits)),winRate:parseFloat(l.toFixed(2)),totalTradesExecuted:o,buyTrades:d,sellTrades:r.length,avgProfitPerTradeCrypto2:parseFloat(n.toFixed(t.numDigits)),avgProfitPerTradeCrypto1:parseFloat(c.toFixed(t.numDigits))}},T=(e,t)=>{let r=e.filter(e=>"SELL"===e.orderType&&void 0!==e.realizedProfitLossCrypto2),s=0;return r.map((e,t)=>(s+=e.realizedProfitLossCrypto2||0,{date:(0,P.GP)(new Date(e.timestamp),"MMM dd HH:mm"),pnl:parseFloat(s.toFixed(4)),trade:t+1}))};function z(){let{orderHistory:e,config:t,getDisplayOrders:r}=(0,p.U)(),[i,l]=(0,a.useState)([]),[P,S]=(0,a.useState)("current"),[F,z]=(0,a.useState)([]),[A,k]=(0,a.useState)(t),M=m.C.getInstance();(0,a.useEffect)(()=>{R()},[]),(0,a.useEffect)(()=>{if("current"===P)z(e),k(t);else{let e=M.loadSession(P);e&&(z(e.orderHistory),k(e.config))}},[P,e,t]);let R=()=>{let e=M.getFilteredSessions(5e3),t=M.getCurrentSessionId();l(e.filter(e=>e.id!==t).sort((e,t)=>t.lastModified-e.lastModified))},D=(0,a.useMemo)(()=>L(F,A),[F,A]),E=(0,a.useMemo)(()=>T(F,A.crypto2),[F,A.crypto2]),U=(0,a.useMemo)(()=>"current"!==P?"0.0000":r().reduce((e,t)=>"Full"===t.status&&void 0!==t.incomeCrypto2?e+t.incomeCrypto2:e,0).toFixed(A.numDigits),[r,A.numDigits,P]),W="current"===P?{name:"Current Session",pair:t.crypto1&&t.crypto2?"".concat(t.crypto1,"/").concat(t.crypto2):"Crypto 1/Crypto 2",isActive:!0}:i.find(e=>e.id===P),B=(0,a.useMemo)(()=>[{title:"Total Realized P/L (".concat(A.crypto1||"Crypto 1",")"),value:D.totalProfitLossCrypto1,icon:(0,s.jsx)(x.A,{className:"h-6 w-6 text-primary"}),description:"Sum of profits from sell trades in Crypto1",isProfit:D.totalProfitLossCrypto1>=0},{title:"Total Realized P/L (".concat(A.crypto2||"Crypto 2",")"),value:D.totalProfitLossCrypto2,icon:(0,s.jsx)(x.A,{className:"h-6 w-6 text-primary"}),description:"Sum of profits from sell trades in Crypto2",isProfit:D.totalProfitLossCrypto2>=0},{title:"Win Rate",value:"".concat(D.winRate,"%"),icon:(0,s.jsx)(f.A,{className:"h-6 w-6 text-primary"}),description:"Profitable sell trades / Total sell trades",isProfit:D.winRate>=50},{title:"Total Trades",value:D.totalTradesExecuted,icon:(0,s.jsx)(u.A,{className:"h-6 w-6 text-primary"}),description:"".concat(D.buyTrades," buys, ").concat(D.sellTrades," sells"),isProfit:!0},{title:"Avg Profit/Trade (".concat(A.crypto2||"Crypto 2",")"),value:D.avgProfitPerTradeCrypto2,icon:(0,s.jsx)(h.A,{className:"h-6 w-6 text-primary"}),description:"Average profit per sell trade",isProfit:D.avgProfitPerTradeCrypto2>=0},{title:"Current Unrealized P/L (".concat(A.crypto2||"Crypto 2",")"),value:U,icon:(0,s.jsx)(y.A,{className:"h-6 w-6 text-primary"}),description:"Unrealized profit/loss from active positions",isProfit:parseFloat(U)>=0,isCurrentOnly:!0}],[D,A,U]);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{className:"text-xl font-bold text-primary",children:"Session Analytics"}),(0,s.jsx)(o.BT,{children:"View trading analytics for current and past sessions."})]}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Select Session:"}),(0,s.jsxs)(d.l6,{value:P,onValueChange:S,children:[(0,s.jsx)(d.bq,{className:"w-full sm:w-[300px]",children:(0,s.jsx)(d.yv,{placeholder:"Select a session"})}),(0,s.jsxs)(d.gC,{children:[(0,s.jsx)(d.eb,{value:"current",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.E,{variant:"default",className:"text-xs",children:"Current"}),(0,s.jsxs)("span",{children:["Current Session (",t.crypto1&&t.crypto2?"".concat(t.crypto1,"/").concat(t.crypto2):"Crypto 1/Crypto 2 = 0",")"]})]})}),i.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.w,{className:"my-1"}),i.map(e=>(0,s.jsx)(d.eb,{value:e.id,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.E,{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Current":"Past"}),(0,s.jsx)("span",{children:e.name})]})},e.id))]})]})]})]})}),W&&(0,s.jsx)("div",{className:"bg-muted/50 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-medium",children:W.name}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:W.pair})]}),W.isActive&&(0,s.jsx)(n.E,{variant:"default",className:"text-xs",children:"Active"})]})})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:B.map((e,t)=>e.isCurrentOnly&&"current"!==P?null:(0,s.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(o.ZB,{className:"text-sm font-medium",children:e.title}),e.icon]}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold ".concat("number"==typeof e.value?e.isProfit?"text-green-600":"text-red-600":"text-foreground"),children:"number"==typeof e.value?e.value.toFixed(A.numDigits):e.value}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},t))}),(0,s.jsxs)(o.Zp,{className:"border-2 border-border",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsxs)(o.ZB,{className:"text-xl font-bold text-primary",children:["Cumulative Profit/Loss Over Time (",A.crypto2,")"]}),(0,s.jsxs)(o.BT,{children:["Chart visualization of trading performance for ",(null==W?void 0:W.name)||"selected session","."]})]}),(0,s.jsx)(o.Wu,{className:"h-80",children:E.length>0?(0,s.jsx)(g.u,{width:"100%",height:"100%",children:(0,s.jsxs)(j.b,{data:E,margin:{top:5,right:20,left:-25,bottom:5},children:[(0,s.jsx)(v.d,{strokeDasharray:"3 3",stroke:"hsl(var(--border))"}),(0,s.jsx)(N.W,{dataKey:"date",stroke:"hsl(var(--muted-foreground))",fontSize:12,tickLine:!1,axisLine:!1}),(0,s.jsx)(b.h,{stroke:"hsl(var(--muted-foreground))",fontSize:12,tickLine:!1,axisLine:!1,tickFormatter:e=>"".concat(e.toFixed(2))}),(0,s.jsx)(w.m,{content:e=>{let{active:t,payload:r,label:a}=e;return t&&r&&r.length?(0,s.jsxs)("div",{className:"bg-card border border-border rounded-lg p-3 shadow-lg",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Date: ".concat(a)}),(0,s.jsxs)("p",{className:"text-sm",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"P/L: "}),(0,s.jsxs)("span",{className:r[0].value>=0?"text-green-600":"text-red-600",children:[r[0].value," ",A.crypto2||"Crypto 2"]})]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Trade #",r[0].payload.trade]})]}):null}}),(0,s.jsx)(C.N,{type:"monotone",dataKey:"pnl",stroke:"hsl(var(--primary))",strokeWidth:2,dot:{fill:"hsl(var(--primary))",strokeWidth:2,r:4},activeDot:{r:6,stroke:"hsl(var(--primary))",strokeWidth:2}})]})}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(u.A,{className:"h-12 w-12 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{children:"No sell trades recorded yet for this session."}),(0,s.jsx)("p",{className:"text-xs",children:"Chart will appear after first profitable trade."})]})})})]})]})}function A(){return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(i.A,{}),(0,s.jsx)(l.A,{}),(0,s.jsx)(z,{})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[823,98,842,377,150,907,318,303,441,684,358],()=>t(52669)),_N_E=e.O()}]);